<?php
/**
 * API Endpoint: Excel Import for Name Dictionary
 * POST /api/excel-import.php - Upload and process Excel file
 * PUT /api/excel-import.php - Handle duplicate resolution and final import
 */

// Set the correct path for includes
$rootPath = dirname(__DIR__);
require_once $rootPath . '/config/config.php';
require_once $rootPath . '/classes/ExcelImportService.php';

header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'POST':
            handleFileUpload();
            break;
        case 'PUT':
            handleDuplicateResolution();
            break;
        default:
            jsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    error_log("Excel Import API Error: " . $e->getMessage());
    jsonResponse(['success' => false, 'error' => 'Internal server error'], 500);
}

function handleFileUpload() {
    // Debug: Log received data
    error_log("Excel Import Debug - POST data: " . print_r($_POST, true));
    error_log("Excel Import Debug - FILES data: " . print_r($_FILES, true));

    // Validate novel ID
    $novelId = isset($_POST['novel_id']) ? (int)$_POST['novel_id'] : 0;
    if (!$novelId) {
        error_log("Excel Import Debug - Novel ID validation failed. Received: " . var_export($_POST['novel_id'] ?? 'NOT_SET', true));
        jsonResponse(['success' => false, 'error' => 'Novel ID is required'], 400);
    }
    
    // Verify novel exists
    $db = Database::getInstance();
    $novel = $db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
    if (!$novel) {
        jsonResponse(['success' => false, 'error' => 'Novel not found'], 404);
    }
    
    // Check if file was uploaded
    if (!isset($_FILES['excel_file']) || $_FILES['excel_file']['error'] !== UPLOAD_ERR_OK) {
        $error = 'No file uploaded';
        if (isset($_FILES['excel_file']['error'])) {
            switch ($_FILES['excel_file']['error']) {
                case UPLOAD_ERR_INI_SIZE:
                case UPLOAD_ERR_FORM_SIZE:
                    $error = 'File is too large';
                    break;
                case UPLOAD_ERR_PARTIAL:
                    $error = 'File upload was interrupted';
                    break;
                case UPLOAD_ERR_NO_TMP_DIR:
                    $error = 'Temporary directory not found';
                    break;
                case UPLOAD_ERR_CANT_WRITE:
                    $error = 'Failed to write file to disk';
                    break;
                case UPLOAD_ERR_EXTENSION:
                    $error = 'File upload stopped by extension';
                    break;
            }
        }
        jsonResponse(['success' => false, 'error' => $error], 400);
    }
    
    $uploadedFile = $_FILES['excel_file'];
    
    // Validate file type
    $allowedTypes = ['application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'];
    $fileType = mime_content_type($uploadedFile['tmp_name']);
    $fileExtension = strtolower(pathinfo($uploadedFile['name'], PATHINFO_EXTENSION));
    
    if (!in_array($fileType, $allowedTypes) && !in_array($fileExtension, ['xls', 'xlsx'])) {
        jsonResponse(['success' => false, 'error' => 'Invalid file type. Please upload .xls or .xlsx files only.'], 400);
    }
    
    // Validate file size (10MB max)
    if ($uploadedFile['size'] > MAX_FILE_SIZE) {
        jsonResponse(['success' => false, 'error' => 'File is too large. Maximum size is 10MB.'], 400);
    }
    
    // Create uploads directory if it doesn't exist
    $uploadDir = APP_ROOT . '/temp/';
    if (!is_dir($uploadDir)) {
        mkdir($uploadDir, 0755, true);
    }
    
    // Generate unique filename
    $filename = 'excel_import_' . uniqid() . '_' . time() . '.' . $fileExtension;
    $filePath = $uploadDir . $filename;
    
    // Move uploaded file
    if (!move_uploaded_file($uploadedFile['tmp_name'], $filePath)) {
        jsonResponse(['success' => false, 'error' => 'Failed to save uploaded file'], 500);
    }
    
    // Process Excel file
    $excelService = new ExcelImportService();
    $result = $excelService->processExcelFile($filePath, $novelId);
    
    // Clean up uploaded file
    unlink($filePath);
    
    if ($result['success']) {
        $data = $result['data'];
        
        // If there are valid entries with no duplicates, import them automatically
        if (!empty($data['valid_entries']) && empty($data['duplicate_entries'])) {
            $importResult = $excelService->importValidEntries($data['valid_entries'], $novelId);
            
            if ($importResult['success']) {
                jsonResponse([
                    'success' => true,
                    'data' => [
                        'imported_count' => $importResult['imported_count'],
                        'duplicate_count' => 0,
                        'error_count' => $data['error_count'],
                        'errors' => array_merge($data['error_entries'], $importResult['errors']),
                        'duplicates' => [],
                        'auto_imported' => true
                    ]
                ]);
            }
        }
        
        // Return results for user review (including duplicates)
        jsonResponse([
            'success' => true,
            'data' => [
                'valid_count' => $data['valid_count'],
                'duplicate_count' => $data['duplicate_count'],
                'error_count' => $data['error_count'],
                'valid_entries' => $data['valid_entries'],
                'duplicates' => $data['duplicate_entries'],
                'errors' => $data['error_entries'],
                'auto_imported' => false
            ]
        ]);
    } else {
        jsonResponse(['success' => false, 'error' => $result['error']], 400);
    }
}

function handleDuplicateResolution() {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['success' => false, 'error' => 'Invalid JSON input'], 400);
    }
    
    $novelId = isset($input['novel_id']) ? (int)$input['novel_id'] : 0;
    $validEntries = isset($input['valid_entries']) ? $input['valid_entries'] : [];
    $duplicateActions = isset($input['duplicate_actions']) ? $input['duplicate_actions'] : [];
    
    if (!$novelId) {
        jsonResponse(['success' => false, 'error' => 'Novel ID is required'], 400);
    }
    
    // Verify novel exists
    $db = Database::getInstance();
    $novel = $db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
    if (!$novel) {
        jsonResponse(['success' => false, 'error' => 'Novel not found'], 404);
    }
    
    $excelService = new ExcelImportService();
    $novelManager = new NovelManager();
    
    $importedCount = 0;
    $updatedCount = 0;
    $skippedCount = 0;
    $errors = [];
    
    // Import valid entries (non-duplicates)
    if (!empty($validEntries)) {
        $importResult = $excelService->importValidEntries($validEntries, $novelId);
        if ($importResult['success']) {
            $importedCount = $importResult['imported_count'];
            $errors = array_merge($errors, $importResult['errors']);
        }
    }
    
    // Handle duplicate actions
    foreach ($duplicateActions as $action) {
        $actionType = $action['action'] ?? 'skip';
        $duplicateData = $action['data'] ?? [];
        
        switch ($actionType) {
            case 'update':
                // Update existing entry
                $existingId = $duplicateData['existing_id'] ?? 0;
                if ($existingId) {
                    $updateResult = $novelManager->updateNameDictionary($novelId, $existingId, [
                        'romanization' => $duplicateData['romanization'],
                        'translation' => $duplicateData['translation'],
                        'name_type' => $duplicateData['name_type']
                    ]);
                    
                    if ($updateResult['success']) {
                        $updatedCount++;
                    } else {
                        $errors[] = [
                            'name' => $duplicateData['original_name'],
                            'error' => 'Failed to update: ' . $updateResult['error']
                        ];
                    }
                }
                break;
                
            case 'import':
                // Import as new entry (with suffix if needed)
                $newName = $duplicateData['original_name'];
                if (isset($duplicateData['suffix'])) {
                    $newName .= $duplicateData['suffix'];
                }
                
                $importResult = $novelManager->addNameToDictionary($novelId, [
                    'original_name' => $newName,
                    'romanization' => $duplicateData['romanization'],
                    'translation' => $duplicateData['translation'],
                    'name_type' => $duplicateData['name_type']
                ]);
                
                if ($importResult['success']) {
                    $importedCount++;
                } else {
                    $errors[] = [
                        'name' => $newName,
                        'error' => 'Failed to import: ' . $importResult['error']
                    ];
                }
                break;
                
            case 'skip':
            default:
                $skippedCount++;
                break;
        }
    }
    
    jsonResponse([
        'success' => true,
        'data' => [
            'imported_count' => $importedCount,
            'updated_count' => $updatedCount,
            'skipped_count' => $skippedCount,
            'errors' => $errors
        ]
    ]);
}
?>
