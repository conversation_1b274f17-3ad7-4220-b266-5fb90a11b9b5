/**
 * Novels List JavaScript
 * Handles novels listing page functionality
 */

class NovelsList {
    constructor() {
        this.novels = [];
        this.filteredNovels = [];
        this.pagination = {};
        this.currentPage = 1;
        this.itemsPerPage = 12;
        this.searchTerm = '';
        this.platformFilter = '';
        this.statusFilter = '';

        this.initializeEventListeners();
        this.loadNovels();
    }

    initializeEventListeners() {
        // Refresh button
        const refreshBtn = document.getElementById('refresh-novels-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadNovels());
        }

        // Search input
        const searchInput = document.getElementById('search-novels');
        if (searchInput) {
            searchInput.addEventListener('input', utils.debounce((e) => {
                this.searchTerm = e.target.value.toLowerCase();
                this.filterAndDisplayNovels();
            }, 300));
        }

        // Platform filter
        const platformFilter = document.getElementById('filter-platform');
        if (platformFilter) {
            platformFilter.addEventListener('change', (e) => {
                this.platformFilter = e.target.value;
                this.filterAndDisplayNovels();
            });
        }

        // Status filter
        const statusFilter = document.getElementById('filter-status');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.statusFilter = e.target.value;
                this.filterAndDisplayNovels();
            });
        }
    }

    async loadNovels() {
        const container = document.getElementById('novels-container');

        try {
            const result = await utils.makeApiRequest('api/novels.php');

            if (result.success && result.data) {
                // Extract novels array from the response data
                this.novels = Array.isArray(result.data.novels) ? result.data.novels : [];
                this.pagination = result.data.pagination || {};
                this.filterAndDisplayNovels();
            } else {
                console.error('Invalid API response:', result);
                this.novels = [];
                container.innerHTML = this.renderError('Failed to load novels');
            }
        } catch (error) {
            console.error('Load novels error:', error);
            this.novels = [];
            container.innerHTML = this.renderError('Network error occurred');
        }
    }

    filterAndDisplayNovels() {
        // Ensure novels is an array
        if (!Array.isArray(this.novels)) {
            console.error('this.novels is not an array:', this.novels);
            this.novels = [];
        }

        // Apply filters
        this.filteredNovels = this.novels.filter(novel => {
            // Search filter
            if (this.searchTerm) {
                const title = (novel.translated_title || novel.original_title || '').toLowerCase();
                const author = (novel.author || '').toLowerCase();
                if (!title.includes(this.searchTerm) && !author.includes(this.searchTerm)) {
                    return false;
                }
            }

            // Platform filter
            if (this.platformFilter && novel.platform !== this.platformFilter) {
                return false;
            }

            // Status filter
            if (this.statusFilter) {
                const progress = utils.calculateProgress(novel.translated_chapters, novel.total_chapters);
                switch (this.statusFilter) {
                    case 'not-started':
                        if (progress > 0) return false;
                        break;
                    case 'in-progress':
                        if (progress === 0 || progress === 100) return false;
                        break;
                    case 'completed':
                        if (progress < 100) return false;
                        break;
                }
            }

            return true;
        });

        this.currentPage = 1;
        this.displayNovels();
        this.updatePagination();
    }

    displayNovels() {
        const container = document.getElementById('novels-container');

        if (this.filteredNovels.length === 0) {
            container.innerHTML = this.renderEmptyState();
            return;
        }

        // Calculate pagination
        const startIndex = (this.currentPage - 1) * this.itemsPerPage;
        const endIndex = startIndex + this.itemsPerPage;
        const novelsToShow = this.filteredNovels.slice(startIndex, endIndex);

        const novelsHtml = this.renderNovelsTable(novelsToShow);
        container.innerHTML = novelsHtml;
    }

    renderNovelsTable(novels) {
        return `
            <div class="table-responsive">
                <table class="table table-striped novels-table">
                    <thead>
                        <tr>
                            <th>Novel</th>
                            <th>Author</th>
                            <th>Platform</th>
                            <th>Progress</th>
                            <th>Added</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${novels.map(novel => this.renderNovelRow(novel)).join('')}
                    </tbody>
                </table>
            </div>
        `;
    }

    renderNovelRow(novel) {
        const progress = utils.calculateProgress(novel.translated_chapters, novel.total_chapters);

        return `
            <tr>
                <td>
                    <div class="novel-title-cell">
                        <div class="novel-title">${utils.escapeHtml(novel.translated_title || novel.original_title)}</div>
                        ${(novel.translated_synopsis || novel.original_synopsis) ? `
                            <div class="novel-synopsis-preview">
                                ${utils.escapeHtml((novel.translated_synopsis || novel.original_synopsis).substring(0, 120))}${(novel.translated_synopsis || novel.original_synopsis).length > 120 ? '...' : ''}
                            </div>
                        ` : ''}
                    </div>
                </td>
                <td>${utils.escapeHtml(novel.author || 'Unknown')}</td>
                <td>
                    <span class="platform-badge platform-${novel.platform}">
                        ${utils.getPlatformName(novel.platform)}
                    </span>
                </td>
                <td>
                    <div class="progress-cell">
                        <div class="progress-info">
                            <small>${novel.translated_chapters}/${novel.total_chapters} (${progress}%)</small>
                        </div>
                        <div class="progress" style="height: 4px;">
                            <div class="progress-bar" style="width: ${progress}%"></div>
                        </div>
                    </div>
                </td>
                <td>
                    <small>${utils.formatDate(novel.created_at)}</small>
                </td>
                <td>
                    <div class="btn-group">
                        <a href="novel-details.php?id=${novel.id}" class="btn btn-primary btn-sm">View</a>
                        <button class="btn btn-outline-secondary btn-sm dropdown-toggle dropdown-toggle-split"
                                type="button" data-bs-toggle="dropdown">
                            <span class="visually-hidden">More</span>
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="novel-details.php?id=${novel.id}">View Details</a></li>
                            ${novel.platform === 'manual' ? `
                                <li><a class="dropdown-item" href="manual-chapter.php?novel_id=${novel.id}">Add Chapter</a></li>
                                ${novel.url && novel.url !== 'manual://novel-' + novel.id ? `
                                    <li><a class="dropdown-item" href="${novel.url}" target="_blank">Original Source</a></li>
                                ` : ''}
                            ` : `
                                <li><a class="dropdown-item" href="${novel.url}" target="_blank">Original Source</a></li>
                            `}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <button class="dropdown-item text-danger"
                                        onclick="novelsList.deleteNovel(${novel.id}, '${utils.escapeHtml(novel.translated_title || novel.original_title).replace(/'/g, "\\'")}')">
                                    Delete
                                </button>
                            </li>
                        </ul>
                    </div>
                </td>
            </tr>
        `;
    }

    renderEmptyState() {
        let message = 'No novels found';
        let suggestion = 'Try adjusting your search or filters.';

        if (!Array.isArray(this.novels) || this.novels.length === 0) {
            message = 'No novels saved yet';
            suggestion = 'Start by adding a novel from the dashboard.';
        }

        return `
            <div class="text-center text-muted py-5">
                <i class="fas fa-book fa-3x mb-3"></i>
                <h5>${message}</h5>
                <p>${suggestion}</p>
                ${(!Array.isArray(this.novels) || this.novels.length === 0) ? `
                    <a href="index.php" class="btn btn-primary">
                        <i class="fas fa-plus me-1"></i>
                        Add Your First Novel
                    </a>
                ` : ''}
            </div>
        `;
    }

    renderError(message) {
        return `
            <div class="text-center text-danger py-5">
                <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                <p>${message}</p>
                <button class="btn btn-primary" onclick="novelsList.loadNovels()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Retry
                </button>
            </div>
        `;
    }

    updatePagination() {
        const paginationContainer = document.getElementById('pagination-container');
        const pagination = document.getElementById('pagination');
        
        const totalPages = Math.ceil(this.filteredNovels.length / this.itemsPerPage);
        
        if (totalPages <= 1) {
            paginationContainer.style.display = 'none';
            return;
        }

        paginationContainer.style.display = 'block';
        
        let paginationHtml = '';
        
        // Previous button
        paginationHtml += `
            <li class="page-item ${this.currentPage === 1 ? 'disabled' : ''}">
                <button class="page-link" onclick="novelsList.goToPage(${this.currentPage - 1})" 
                        ${this.currentPage === 1 ? 'disabled' : ''}>
                    Previous
                </button>
            </li>
        `;
        
        // Page numbers
        for (let i = 1; i <= totalPages; i++) {
            if (i === 1 || i === totalPages || (i >= this.currentPage - 2 && i <= this.currentPage + 2)) {
                paginationHtml += `
                    <li class="page-item ${i === this.currentPage ? 'active' : ''}">
                        <button class="page-link" onclick="novelsList.goToPage(${i})">${i}</button>
                    </li>
                `;
            } else if (i === this.currentPage - 3 || i === this.currentPage + 3) {
                paginationHtml += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
            }
        }
        
        // Next button
        paginationHtml += `
            <li class="page-item ${this.currentPage === totalPages ? 'disabled' : ''}">
                <button class="page-link" onclick="novelsList.goToPage(${this.currentPage + 1})" 
                        ${this.currentPage === totalPages ? 'disabled' : ''}>
                    Next
                </button>
            </li>
        `;
        
        pagination.innerHTML = paginationHtml;
    }

    goToPage(page) {
        const totalPages = Math.ceil(this.filteredNovels.length / this.itemsPerPage);
        if (page >= 1 && page <= totalPages) {
            this.currentPage = page;
            this.displayNovels();
            this.updatePagination();
            utils.scrollToElement('novels-container', 100);
        }
    }

    async deleteNovel(novelId, novelTitle) {
        if (!confirm(`Are you sure you want to delete "${novelTitle}"? This action cannot be undone.`)) {
            return;
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest(`api/novels.php?id=${novelId}`, {
                method: 'DELETE'
            });

            if (result.success) {
                utils.showToast('Novel deleted successfully', 'success');
                this.loadNovels(); // Reload the list
            } else {
                utils.showToast(result.error || 'Failed to delete novel', 'error');
            }
        } catch (error) {
            console.error('Delete novel error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            utils.showLoading(false);
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.novelsList = new NovelsList();
});
