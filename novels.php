<?php
/**
 * Novels Listing Page
 * Displays all saved novels in a dedicated page
 */

require_once 'config/config.php';
require_once 'includes/header.php';

renderHeader('My Novels');
?>

<?php 
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php'; 
renderNavigation('novels');
?>

<div class="container mt-4">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-library me-2"></i>
            My Novels
        </h2>
        <div>
            <a href="index.php" class="btn btn-outline-primary me-2">
                <i class="fas fa-plus me-1"></i>
                Add Novel
            </a>
            <button id="refresh-novels-btn" class="btn btn-outline-secondary">
                <i class="fas fa-sync-alt me-1"></i>
                Refresh
            </button>
        </div>
    </div>

    <!-- Search and Filter -->
    <div class="card mb-4">
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="input-group">
                        <span class="input-group-text">
                            <i class="fas fa-search"></i>
                        </span>
                        <input type="text" id="search-novels" class="form-control" 
                               placeholder="Search novels by title or author...">
                    </div>
                </div>
                <div class="col-md-3">
                    <select id="filter-platform" class="form-select">
                        <option value="">All Platforms</option>
                        <option value="kakuyomu">Kakuyomu</option>
                        <option value="syosetu">Syosetu</option>
                        <option value="shuba69">69书吧</option>
                        <option value="manual">Manual Entry</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <select id="filter-status" class="form-select">
                        <option value="">All Status</option>
                        <option value="not-started">Not Started</option>
                        <option value="in-progress">In Progress</option>
                        <option value="completed">Completed</option>
                    </select>
                </div>
            </div>
        </div>
    </div>

    <!-- Novels Table -->
    <div id="novels-container">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading novels...</p>
        </div>
    </div>

    <!-- Pagination -->
    <nav id="pagination-container" class="mt-4" style="display: none;">
        <ul class="pagination justify-content-center" id="pagination">
        </ul>
    </nav>
</div>

<?php renderFooter(['assets/js/novels-list.js']); ?>
