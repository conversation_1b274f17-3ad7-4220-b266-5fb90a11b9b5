<?php
/**
 * WordPress Integration API
 * Handles posting novels and chapters to WordPress
 */

require_once '../config/config.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            // Post content to WordPress
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['action'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Missing action parameter']);
                exit;
            }
            
            // Initialize WordPress service with optional profile
            $profileId = isset($input['profile_id']) ? (int)$input['profile_id'] : null;
            $wordpressService = $profileId ? new WordPressService($profileId) : new WordPressService();

            switch ($input['action']) {
                case 'test_connection':
                    $result = $wordpressService->testConnection();
                    echo json_encode($result);
                    break;

                case 'get_post_types':
                    $result = $wordpressService->getAvailablePostTypes();
                    echo json_encode($result);
                    break;

                case 'validate_post_types':
                    $result = $wordpressService->validateCustomPostTypes();
                    echo json_encode($result);
                    break;

                case 'post_novel':
                    if (!isset($input['novel_id'])) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'error' => 'Missing novel_id']);
                        exit;
                    }

                    $result = $wordpressService->postNovel((int)$input['novel_id'], $profileId);
                    echo json_encode($result);
                    break;

                case 'post_chapter':
                    if (!isset($input['chapter_id'])) {
                        http_response_code(400);
                        echo json_encode(['success' => false, 'error' => 'Missing chapter_id']);
                        exit;
                    }

                    $result = $wordpressService->postChapter((int)$input['chapter_id'], $profileId);
                    echo json_encode($result);
                    break;

                default:
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => 'Invalid action']);
                    break;
            }
            break;
            
        case 'GET':
            // Get WordPress posting status
            $wordpressService = new WordPressService();
            
            if (isset($_GET['novel_id'])) {
                $novelId = (int)$_GET['novel_id'];
                $status = $wordpressService->getNovelPostingStatus($novelId);
                echo json_encode([
                    'success' => true,
                    'status' => $status
                ]);
            } elseif (isset($_GET['chapter_id'])) {
                $chapterId = (int)$_GET['chapter_id'];
                $status = $wordpressService->getChapterPostingStatus($chapterId);
                echo json_encode([
                    'success' => true,
                    'status' => $status
                ]);
            } elseif (isset($_GET['config_status'])) {
                $isConfigured = $wordpressService->isConfigured();
                echo json_encode([
                    'success' => true,
                    'configured' => $isConfigured
                ]);
            } else {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Missing required parameters']);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("WordPress API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
