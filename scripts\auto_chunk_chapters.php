<?php
/**
 * Auto-chunk existing long chapters
 * This script identifies chapters that exceed the chunk size limit
 * and automatically creates chunks for them
 */

require_once __DIR__ . '/../config/config.php';

echo "Auto-chunking Script for Long Chapters\n";
echo "=====================================\n\n";

try {
    $chapterChunker = new ChapterChunker();
    $db = Database::getInstance();
    
    // Get chunk size limit from preferences
    $chunkSizeLimit = $db->fetchOne(
        "SELECT preference_value FROM user_preferences WHERE preference_key = 'chunk_size_limit'"
    );
    $limit = $chunkSizeLimit ? (int)$chunkSizeLimit['preference_value'] : 25000;
    
    echo "Using chunk size limit: " . number_format($limit) . " characters\n\n";
    
    // Find chapters that need chunking
    $longChapters = $db->fetchAll(
        "SELECT c.id, c.chapter_number, c.novel_id, c.original_title,
                CHAR_LENGTH(c.original_content) as content_length,
                COUNT(cc.id) as existing_chunks
         FROM chapters c
         LEFT JOIN chapter_chunks cc ON c.id = cc.chapter_id
         WHERE CHAR_LENGTH(c.original_content) > ?
           AND c.original_content IS NOT NULL
           AND c.original_content != ''
         GROUP BY c.id
         ORDER BY content_length DESC",
        [$limit]
    );
    
    if (empty($longChapters)) {
        echo "✅ No chapters found that need chunking.\n";
        echo "All chapters are within the size limit of " . number_format($limit) . " characters.\n";
        exit(0);
    }
    
    echo "Found " . count($longChapters) . " chapters that need chunking:\n\n";
    
    $totalProcessed = 0;
    $totalChunksCreated = 0;
    $errors = [];
    
    foreach ($longChapters as $chapter) {
        $chapterInfo = "Chapter {$chapter['chapter_number']} (Novel {$chapter['novel_id']})";
        $contentLength = number_format($chapter['content_length']);
        
        echo "Processing {$chapterInfo}: {$contentLength} characters";
        
        if ($chapter['existing_chunks'] > 0) {
            echo " - Already has {$chapter['existing_chunks']} chunks, skipping\n";
            continue;
        }
        
        echo "\n";
        
        try {
            // Get the full chapter content
            $fullChapter = $db->fetchOne(
                "SELECT original_content FROM chapters WHERE id = ?",
                [$chapter['id']]
            );
            
            if (!$fullChapter || empty($fullChapter['original_content'])) {
                echo "  ❌ Error: No content found\n";
                $errors[] = "{$chapterInfo}: No content found";
                continue;
            }
            
            // Debug: Check if chunking is actually needed
            $actualLength = mb_strlen($fullChapter['original_content']);
            $needsChunking = $chapterChunker->needsChunking($fullChapter['original_content']);

            echo "  Debug: Actual content length: " . number_format($actualLength) . " characters\n";
            echo "  Debug: Needs chunking: " . ($needsChunking ? 'YES' : 'NO') . "\n";

            // Perform chunking
            $result = $chapterChunker->splitChapter($chapter['id'], $fullChapter['original_content']);

            echo "  Debug: Split result: " . json_encode($result) . "\n";

            if ($result['success']) {
                $chunksCreated = $result['chunks_created'];
                $avgChunkSize = $result['average_chunk_size'] ?? 0;

                echo "  ✅ Successfully created {$chunksCreated} chunks\n";
                if ($avgChunkSize > 0) {
                    echo "     Average chunk size: " . number_format($avgChunkSize) . " characters\n";
                }

                $totalProcessed++;
                $totalChunksCreated += $chunksCreated;
            } else {
                echo "  ❌ Error: {$result['error']}\n";
                $errors[] = "{$chapterInfo}: {$result['error']}";
            }
            
        } catch (Exception $e) {
            echo "  ❌ Exception: {$e->getMessage()}\n";
            $errors[] = "{$chapterInfo}: {$e->getMessage()}";
        }
        
        echo "\n";
        
        // Add small delay to avoid overwhelming the system
        usleep(100000); // 0.1 second delay
    }
    
    // Summary
    echo "Auto-chunking Summary\n";
    echo "====================\n";
    echo "Chapters processed: {$totalProcessed}\n";
    echo "Total chunks created: {$totalChunksCreated}\n";
    echo "Errors encountered: " . count($errors) . "\n";
    
    if (!empty($errors)) {
        echo "\nErrors:\n";
        foreach ($errors as $error) {
            echo "  - {$error}\n";
        }
    }
    
    if ($totalProcessed > 0) {
        echo "\n✅ Auto-chunking completed successfully!\n";
        echo "\nNext steps:\n";
        echo "1. Review the chunks in the database\n";
        echo "2. Test translation with chunked chapters\n";
        echo "3. Monitor translation performance and timeout issues\n";
    }
    
} catch (Exception $e) {
    echo "❌ Script failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
