<?php
/**
 * Furigana Processing Service
 * Handles detection, extraction, storage, and display of furigana (ruby text)
 */

class FuriganaService {
    private $db;
    private $translationService;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->translationService = new TranslationService();
    }
    
    /**
     * Process text with furigana markup and store in database
     */
    public function processChapterFurigana(int $chapterId, string $textWithFurigana): array {
        $startTime = microtime(true);
        
        try {
            // Extract furigana pairs from text
            $furiganaData = $this->extractFuriganaData($textWithFurigana);
            
            // Get chapter info
            $chapter = $this->db->fetchOne(
                "SELECT novel_id, chapter_number FROM chapters WHERE id = ?",
                [$chapterId]
            );
            
            if (!$chapter) {
                throw new Exception("Chapter not found");
            }
            
            // Store furigana data in dictionary
            $this->storeFuriganaInDictionary($chapter['novel_id'], $furiganaData, $chapter['chapter_number']);
            
            // Update chapter with furigana content
            $this->db->query(
                "UPDATE chapters SET
                 original_content_with_furigana = ?,
                 furigana_processing_status = 'processed',
                 furigana_count = ?
                 WHERE id = ?",
                [$textWithFurigana, count($furiganaData), $chapterId]
            );
            
            $executionTime = microtime(true) - $startTime;
            
            return [
                'success' => true,
                'furigana_count' => count($furiganaData),
                'execution_time' => round($executionTime, 2),
                'furigana_data' => $furiganaData
            ];
            
        } catch (Exception $e) {
            // Update status to error
            $this->db->query(
                "UPDATE chapters SET furigana_processing_status = 'error' WHERE id = ?",
                [$chapterId]
            );
            
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Extract furigana data from text with markup
     */
    public function extractFuriganaData(string $textWithFurigana): array {
        $furiganaData = [];
        
        // Pattern to match {kanji|furigana} markup
        if (preg_match_all('/\{([^|]+)\|([^}]+)\}/', $textWithFurigana, $matches, PREG_SET_ORDER)) {
            foreach ($matches as $match) {
                $kanji = trim($match[1]);
                $furigana = trim($match[2]);
                
                if (!empty($kanji) && !empty($furigana)) {
                    $key = $kanji . '|' . $furigana;
                    if (!isset($furiganaData[$key])) {
                        $furiganaData[$key] = [
                            'kanji' => $kanji,
                            'furigana' => $furigana,
                            'frequency' => 0
                        ];
                    }
                    $furiganaData[$key]['frequency']++;
                }
            }
        }
        
        return array_values($furiganaData);
    }
    
    /**
     * Store furigana data in dictionary
     */
    private function storeFuriganaInDictionary(int $novelId, array $furiganaData, int $chapterNumber): void {
        foreach ($furiganaData as $item) {
            // Check if this furigana pair already exists
            $existing = $this->db->fetchOne(
                "SELECT id, frequency FROM furigana_dictionary 
                 WHERE novel_id = ? AND kanji_text = ? AND furigana_text = ?",
                [$novelId, $item['kanji'], $item['furigana']]
            );
            
            if ($existing) {
                // Update frequency
                $this->db->query(
                    "UPDATE furigana_dictionary
                     SET frequency = frequency + ?, updated_at = CURRENT_TIMESTAMP
                     WHERE id = ?",
                    [$item['frequency'], $existing['id']]
                );
            } else {
                // Insert new entry
                $this->db->query(
                    "INSERT INTO furigana_dictionary
                     (novel_id, kanji_text, furigana_text, frequency, first_appearance_chapter)
                     VALUES (?, ?, ?, ?, ?)",
                    [$novelId, $item['kanji'], $item['furigana'], $item['frequency'], $chapterNumber]
                );
            }
        }
    }
    
    /**
     * Convert furigana markup to HTML ruby tags
     */
    public function convertToHtmlRuby(string $textWithFurigana): string {
        return preg_replace_callback(
            '/\{([^|]+)\|([^}]+)\}/',
            function($matches) {
                $kanji = htmlspecialchars($matches[1], ENT_QUOTES, 'UTF-8');
                $furigana = htmlspecialchars($matches[2], ENT_QUOTES, 'UTF-8');
                return "<ruby>{$kanji}<rt>{$furigana}</rt></ruby>";
            },
            $textWithFurigana
        );
    }
    
    /**
     * Convert furigana markup to parentheses format
     */
    public function convertToParentheses(string $textWithFurigana): string {
        return preg_replace_callback(
            '/\{([^|]+)\|([^}]+)\}/',
            function($matches) {
                return $matches[1] . '(' . $matches[2] . ')';
            },
            $textWithFurigana
        );
    }
    
    /**
     * Remove furigana markup, keeping only base text
     */
    public function removeFurigana(string $textWithFurigana): string {
        return preg_replace('/\{([^|]+)\|([^}]+)\}/', '$1', $textWithFurigana);
    }
    
    /**
     * Get furigana dictionary for a novel
     */
    public function getFuriganaDictionary(int $novelId): array {
        return $this->db->fetchAll(
            "SELECT * FROM furigana_dictionary 
             WHERE novel_id = ? 
             ORDER BY frequency DESC, kanji_text ASC",
            [$novelId]
        );
    }
    
    /**
     * Update furigana processing preference
     */
    public function updateFuriganaPreference(int $novelId, string $kanjiText, string $furiganaText, string $preference): bool {
        $stmt = $this->db->query(
            "UPDATE furigana_dictionary
             SET processing_preference = ?, is_verified = TRUE, updated_at = CURRENT_TIMESTAMP
             WHERE novel_id = ? AND kanji_text = ? AND furigana_text = ?",
            [$preference, $novelId, $kanjiText, $furiganaText]
        );
        return $stmt->rowCount() > 0;
    }
    
    /**
     * Process furigana for translation based on preferences
     */
    public function processForTranslation(string $textWithFurigana, int $novelId): string {
        // Get user preference for furigana translation mode
        $preference = $this->db->fetchOne(
            "SELECT preference_value FROM user_preferences WHERE preference_key = 'furigana_translation_mode'"
        );
        
        $mode = $preference['preference_value'] ?? 'preserve';
        
        switch ($mode) {
            case 'remove':
                return $this->removeFurigana($textWithFurigana);
            case 'parentheses':
                return $this->convertToParentheses($textWithFurigana);
            case 'romanize':
                return $this->romanizeFurigana($textWithFurigana);
            case 'preserve':
            default:
                return $textWithFurigana;
        }
    }
    
    /**
     * Romanize furigana using AI
     */
    private function romanizeFurigana(string $textWithFurigana): string {
        return preg_replace_callback(
            '/\{([^|]+)\|([^}]+)\}/',
            function($matches) {
                $kanji = $matches[1];
                $furigana = $matches[2];
                
                // Use translation service to romanize furigana
                $romanized = $this->translationService->romanizeText($furigana);
                
                if ($romanized['success']) {
                    return $kanji . '(' . $romanized['romanized_text'] . ')';
                }
                
                // Fallback to original if romanization fails
                return $kanji . '(' . $furigana . ')';
            },
            $textWithFurigana
        );
    }
}
