<?php
/**
 * HTML Entity Fix Script
 * Fixes existing novel and chapter titles that contain HTML entities
 */

require_once __DIR__ . '/../config/config.php';

echo "=== HTML Entity Fix Script ===\n\n";

try {
    $db = Database::getInstance();
    
    echo "1. Checking for novels with HTML entities in titles...\n";
    
    // Get all novels
    $novels = $db->fetchAll("SELECT id, original_title, translated_title FROM novels");
    
    $novelUpdates = 0;
    $chapterUpdates = 0;
    
    foreach ($novels as $novel) {
        $updated = false;
        $updateData = [];
        
        // Check original title
        if (!empty($novel['original_title'])) {
            $cleanTitle = processTitleText($novel['original_title']);
            if ($cleanTitle !== $novel['original_title']) {
                $updateData['original_title'] = $cleanTitle;
                $updated = true;
                echo "   Novel ID {$novel['id']}: Original title\n";
                echo "     Before: {$novel['original_title']}\n";
                echo "     After:  {$cleanTitle}\n";
            }
        }
        
        // Check translated title
        if (!empty($novel['translated_title'])) {
            $cleanTitle = processTitleText($novel['translated_title']);
            if ($cleanTitle !== $novel['translated_title']) {
                $updateData['translated_title'] = $cleanTitle;
                $updated = true;
                echo "   Novel ID {$novel['id']}: Translated title\n";
                echo "     Before: {$novel['translated_title']}\n";
                echo "     After:  {$cleanTitle}\n";
            }
        }
        
        // Update if needed
        if ($updated) {
            $db->update('novels', $updateData, 'id = ?', [$novel['id']]);
            $novelUpdates++;
        }
    }
    
    echo "\n2. Checking for chapters with HTML entities in titles...\n";
    
    // Get all chapters
    $chapters = $db->fetchAll("SELECT id, original_title, translated_title FROM chapters WHERE original_title IS NOT NULL OR translated_title IS NOT NULL");
    
    foreach ($chapters as $chapter) {
        $updated = false;
        $updateData = [];
        
        // Check original title
        if (!empty($chapter['original_title'])) {
            $cleanTitle = processTitleText($chapter['original_title']);
            if ($cleanTitle !== $chapter['original_title']) {
                $updateData['original_title'] = $cleanTitle;
                $updated = true;
                echo "   Chapter ID {$chapter['id']}: Original title\n";
                echo "     Before: {$chapter['original_title']}\n";
                echo "     After:  {$cleanTitle}\n";
            }
        }
        
        // Check translated title
        if (!empty($chapter['translated_title'])) {
            $cleanTitle = processTitleText($chapter['translated_title']);
            if ($cleanTitle !== $chapter['translated_title']) {
                $updateData['translated_title'] = $cleanTitle;
                $updated = true;
                echo "   Chapter ID {$chapter['id']}: Translated title\n";
                echo "     Before: {$chapter['translated_title']}\n";
                echo "     After:  {$cleanTitle}\n";
            }
        }
        
        // Update if needed
        if ($updated) {
            $db->update('chapters', $updateData, 'id = ?', [$chapter['id']]);
            $chapterUpdates++;
        }
    }
    
    echo "\n=== Summary ===\n";
    echo "Novels updated: {$novelUpdates}\n";
    echo "Chapters updated: {$chapterUpdates}\n";
    echo "Total updates: " . ($novelUpdates + $chapterUpdates) . "\n";
    
    if ($novelUpdates > 0 || $chapterUpdates > 0) {
        echo "\n✅ HTML entities have been successfully decoded!\n";
    } else {
        echo "\n✅ No HTML entities found - all titles are clean!\n";
    }
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}
