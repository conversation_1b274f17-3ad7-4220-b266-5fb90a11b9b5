<?php
/**
 * WordPress Connection Health API
 * Provides connection statistics and health monitoring
 */

require_once '../config/config.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'GET':
            $action = $_GET['action'] ?? 'stats';
            
            switch ($action) {
                case 'stats':
                    $profileName = $_GET['profile'] ?? 'default';
                    $hours = isset($_GET['hours']) ? (int)$_GET['hours'] : 24;
                    
                    $monitor = new WordPressConnectionMonitor();
                    $stats = $monitor->getConnectionStats($profileName, $hours);
                    
                    echo json_encode($stats);
                    break;
                    
                case 'issues':
                    $profileName = $_GET['profile'] ?? 'default';
                    
                    $monitor = new WordPressConnectionMonitor();
                    $issues = $monitor->detectIssues($profileName);
                    
                    echo json_encode([
                        'success' => true,
                        'issues' => $issues
                    ]);
                    break;
                    
                case 'profiles':
                    // Get all profiles with their recent stats
                    $profiles = $db->fetchAll(
                        "SELECT profile_name, site_url FROM wordpress_profiles WHERE is_active = 1"
                    );
                    
                    $monitor = new WordPressConnectionMonitor();
                    $profileStats = [];
                    
                    foreach ($profiles as $profile) {
                        $stats = $monitor->getConnectionStats($profile['profile_name'], 24);
                        $profileStats[] = [
                            'profile_name' => $profile['profile_name'],
                            'site_url' => $profile['site_url'],
                            'stats' => $stats['stats'] ?? null,
                            'success_rate' => $stats['success_rate'] ?? 0
                        ];
                    }
                    
                    echo json_encode([
                        'success' => true,
                        'profiles' => $profileStats
                    ]);
                    break;
                    
                case 'recent_activity':
                    $profileName = $_GET['profile'] ?? null;
                    $limit = isset($_GET['limit']) ? (int)$_GET['limit'] : 50;
                    
                    $whereClause = '';
                    $params = [];
                    
                    if ($profileName) {
                        $whereClause = 'WHERE profile_name = ?';
                        $params[] = $profileName;
                    }
                    
                    $recentActivity = $db->fetchAll(
                        "SELECT * FROM wordpress_connection_log 
                         $whereClause
                         ORDER BY created_at DESC 
                         LIMIT ?",
                        array_merge($params, [$limit])
                    );
                    
                    echo json_encode([
                        'success' => true,
                        'activity' => $recentActivity
                    ]);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => 'Invalid action']);
                    break;
            }
            break;
            
        case 'POST':
            $input = json_decode(file_get_contents('php://input'), true);
            $action = $input['action'] ?? '';
            
            switch ($action) {
                case 'clean_logs':
                    $daysToKeep = isset($input['days']) ? (int)$input['days'] : 30;
                    
                    $monitor = new WordPressConnectionMonitor();
                    $deletedCount = $monitor->cleanOldLogs($daysToKeep);
                    
                    echo json_encode([
                        'success' => true,
                        'message' => "Cleaned $deletedCount old log entries"
                    ]);
                    break;
                    
                case 'test_all_profiles':
                    // Test connection to all active profiles
                    $profiles = $db->fetchAll(
                        "SELECT * FROM wordpress_profiles WHERE is_active = 1"
                    );
                    
                    $results = [];
                    
                    foreach ($profiles as $profile) {
                        try {
                            $wordpressService = new WordPressService($profile['id']);
                            $testResult = $wordpressService->testConnection();
                            
                            $results[] = [
                                'profile_name' => $profile['profile_name'],
                                'site_url' => $profile['site_url'],
                                'success' => $testResult['success'],
                                'error' => $testResult['error'] ?? null
                            ];
                        } catch (Exception $e) {
                            $results[] = [
                                'profile_name' => $profile['profile_name'],
                                'site_url' => $profile['site_url'],
                                'success' => false,
                                'error' => $e->getMessage()
                            ];
                        }
                    }
                    
                    echo json_encode([
                        'success' => true,
                        'results' => $results
                    ]);
                    break;
                    
                default:
                    http_response_code(400);
                    echo json_encode(['success' => false, 'error' => 'Invalid action']);
                    break;
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    error_log("WordPress Health API Error: " . $e->getMessage());
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ]);
}
?>
