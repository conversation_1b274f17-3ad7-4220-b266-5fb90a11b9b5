# Name Dictionary Management System

## Overview

The Name Dictionary Management System provides comprehensive tools for editing, organizing, and managing character names and their types in your novel translation application. This system addresses the need to correct misclassified names and maintain proper organization.

## Features

### ✅ **Individual Name Editing**
- Edit name types directly in the novel details modal
- Update romanization and translation fields
- Real-time validation and feedback

### ✅ **Dedicated Management Page**
- Full-featured interface for bulk operations
- Advanced filtering and search capabilities
- Comprehensive table view with all name details

### ✅ **Bulk Operations**
- Bulk type updates for multiple names
- Bulk romanization generation
- Bulk deletion with confirmation
- Select all/none functionality

### ✅ **Advanced Filtering**
- Filter by name type (character, location, organization, etc.)
- Filter by status (translated, romanized, untranslated)
- Real-time search across all name fields
- Pagination with customizable items per page

## Available Name Types

The system supports the following name types:

1. **Character** - Person names, protagonists, side characters
2. **Location** - Places, cities, buildings, geographical locations
3. **Organization** - Companies, guilds, groups, institutions
4. **Country** - Nations, kingdoms, territories
5. **Skill** - Abilities, magic spells, techniques
6. **Monster** - Creatures, beasts, enemies
7. **Other** - Items, concepts, miscellaneous terms

## User Interface Components

### 1. **Enhanced Novel Details Modal**
- Added name type dropdown to each name entry
- Immediate type updates with visual feedback
- "Manage" button linking to full management page

### 2. **Dedicated Management Page** (`name-dictionary.php`)
- Comprehensive table view with all name details
- Advanced filtering and search controls
- Bulk action controls with safety confirmations
- Export functionality for data backup

### 3. **Responsive Design**
- Mobile-friendly interface
- Collapsible bulk actions on smaller screens
- Optimized table layout for different screen sizes

## API Endpoints

### GET `/api/name-dictionary.php`
**Purpose:** Retrieve all names for a novel with statistics

**Parameters:**
- `novel_id` (required) - The novel ID

**Response:**
```json
{
  "success": true,
  "data": {
    "names": [...],
    "statistics": {
      "total_names": 150,
      "by_type": {
        "character": 80,
        "location": 30,
        "skill": 25,
        "other": 15
      },
      "translated_count": 120,
      "romanized_count": 145
    }
  }
}
```

### PUT `/api/name-dictionary.php`
**Purpose:** Bulk update operations

**Actions:**
- `bulk_update_type` - Update name type for multiple entries
- `bulk_romanize` - Generate romanization for multiple entries

**Example Request:**
```json
{
  "novel_id": 1,
  "action": "bulk_update_type",
  "name_ids": [1, 2, 3],
  "name_type": "character"
}
```

### DELETE `/api/name-dictionary.php`
**Purpose:** Bulk delete operations

**Example Request:**
```json
{
  "novel_id": 1,
  "name_ids": [1, 2, 3]
}
```

## Usage Instructions

### 1. **Quick Type Editing**
1. Open any novel details modal
2. Scroll to the Name Dictionary section
3. Use the dropdown next to each name to change its type
4. Changes are saved automatically

### 2. **Bulk Management**
1. Click "Manage" button in the novel details modal
2. Or navigate directly to `name-dictionary.php?novel_id=X`
3. Use filters to find specific names
4. Select multiple names using checkboxes
5. Apply bulk operations using the controls that appear

### 3. **Filtering and Search**
- **Search:** Type in the search box to find names by original, romanization, or translation
- **Type Filter:** Select a specific name type to view only those entries
- **Status Filter:** Filter by translation/romanization status
- **Items per Page:** Choose how many entries to display (25, 50, 100, or All)

### 4. **Bulk Operations**
1. Select names using individual checkboxes or "Select All"
2. Choose an operation from the bulk actions section:
   - **Set Type:** Change the type for all selected names
   - **Generate Romanization:** Auto-generate romanization for selected names
   - **Delete Selected:** Remove selected names (with confirmation)

## File Structure

```
├── name-dictionary.php              # Main management page
├── assets/js/name-dictionary.js     # JavaScript functionality
├── api/name-dictionary.php          # API endpoints
├── test_name_management.php         # Testing interface
└── docs/NAME_DICTIONARY_MANAGEMENT.md
```

## Testing

### 1. **Test Data Setup**
Run `test_name_management.php` to:
- Verify novel exists
- Create sample test data if needed
- View current statistics
- Access management interface

### 2. **Manual Testing Checklist**
- [ ] Individual type editing in novel details modal
- [ ] Bulk type updates work correctly
- [ ] Search and filtering functions properly
- [ ] Pagination works with different page sizes
- [ ] Bulk romanization generation
- [ ] Bulk deletion with proper confirmation
- [ ] Export functionality produces valid CSV
- [ ] Mobile responsiveness

## Integration with Translation System

The name dictionary management system is fully integrated with the existing translation workflow:

1. **Automatic Detection:** Names are still automatically detected during translation
2. **Type Classification:** New names default to appropriate types based on AI detection
3. **Manual Correction:** Users can correct misclassifications using the management tools
4. **Translation Consistency:** Updated name types and translations are immediately used in future translations

## Benefits

1. **Improved Organization:** Proper categorization of names by type
2. **Better Translation Quality:** Consistent handling of different name types
3. **User Control:** Full control over name classifications and translations
4. **Efficiency:** Bulk operations save time when managing large name dictionaries
5. **Data Integrity:** Export functionality provides backup and data portability

## Future Enhancements

1. **Import Functionality:** Allow importing name dictionaries from CSV files
2. **Name Relationships:** Track relationships between characters
3. **Auto-Classification:** Improve AI-based type detection
4. **Version History:** Track changes to name entries over time
5. **Cross-Novel Sharing:** Share name dictionaries between related novels
