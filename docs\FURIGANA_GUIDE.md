# Furigana Support Guide

## Overview

This guide explains how furigana (ruby text) is handled in the Novel Translation Application. <PERSON>rigana provides pronunciation guides for kanji characters in Japanese text, typically displayed as small hiragana or katakana characters above the kanji.

## Features

### 1. **Automatic Detection**
- Furigana is automatically detected during the crawling process
- Supports HTML `<ruby>` tags with `<rt>` elements
- Converts to internal markup format: `{kanji|furigana}`

### 2. **Storage**
- Original content with furigana preserved in `original_content_with_furigana` column
- Clean content (furigana removed) stored in `original_content` column
- Furigana dictionary tracks all kanji-furigana pairs
- Processing status tracked per chapter

### 3. **Display Options**
- **Ruby Text**: Standard HTML ruby display (default)
- **Parentheses**: Kanji(furigana) format
- **Hidden**: Show only kanji, hide furigana

### 4. **Translation Integration**
- Configurable furigana handling during translation
- Options: preserve, remove, translate, or romanize
- AI-based processing for optimal translation quality

## Database Schema

### New Tables

#### `furigana_dictionary`
```sql
CREATE TABLE furigana_dictionary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT NOT NULL,
    kanji_text VARCHAR(200) NOT NULL,
    furigana_text VARCHAR(200) NOT NULL,
    frequency INT DEFAULT 1,
    first_appearance_chapter INT,
    is_verified BOOLEAN DEFAULT FALSE,
    processing_preference ENUM('preserve', 'translate', 'remove', 'romanize') DEFAULT 'preserve',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE
);
```

### Enhanced Tables

#### `chapters` (new columns)
- `original_content_with_furigana LONGTEXT` - Content with furigana markup
- `furigana_processing_status ENUM('none', 'detected', 'processed', 'error')` - Processing status
- `furigana_count INT` - Number of furigana pairs found

#### `user_preferences` (new entries)
- `furigana_display_mode` - Default display mode ('ruby', 'parentheses', 'hidden')
- `furigana_translation_mode` - How to handle furigana during translation
- `furigana_ai_processing` - Enable AI-based furigana processing
- `furigana_auto_detect` - Auto-detect furigana during crawling

## API Endpoints

### `/api/furigana.php`

#### GET Requests
- `GET /api/furigana.php?novel_id=123` - Get furigana statistics
- `GET /api/furigana.php?novel_id=123&action=dictionary` - Get furigana dictionary

#### POST Requests
- Process chapter furigana
- Convert furigana formats

#### PUT Requests
- Update furigana processing preferences

## Usage Examples

### 1. **Crawling with Furigana**
```php
// Automatic detection during chapter saving
$novelManager = new NovelManager();
$result = $novelManager->saveChapter($novelId, $chapterNumber);

if ($result['furigana_detected']) {
    echo "Found {$result['furigana_count']} furigana pairs";
}
```

### 2. **Processing Furigana**
```php
$furiganaService = new FuriganaService();

// Process chapter furigana
$result = $furiganaService->processChapterFurigana($chapterId, $textWithFurigana);

// Convert formats
$htmlRuby = $furiganaService->convertToHtmlRuby($textWithFurigana);
$parentheses = $furiganaService->convertToParentheses($textWithFurigana);
$cleanText = $furiganaService->removeFurigana($textWithFurigana);
```

### 3. **Translation with Furigana**
```php
$translationService = new TranslationService();

// Furigana handling is automatic based on user preferences
$result = $translationService->translateChapter($novelId, $chapterId);
```

### 4. **Frontend Display**
```javascript
// Furigana display is handled automatically in chapter-view.js
// Users can change display mode via dropdown controls

// Programmatic control
chapterView.changeFuriganaDisplay('parentheses');
```

## Configuration

### User Preferences
Set furigana preferences in the database:

```sql
-- Display mode
UPDATE user_preferences 
SET preference_value = 'ruby' 
WHERE preference_key = 'furigana_display_mode';

-- Translation mode
UPDATE user_preferences 
SET preference_value = 'preserve' 
WHERE preference_key = 'furigana_translation_mode';
```

### Per-Novel Settings
Furigana processing preferences can be set per kanji-furigana pair:

```php
$furiganaService->updateFuriganaPreference(
    $novelId, 
    '漢字', 
    'かんじ', 
    'preserve'
);
```

## Best Practices

### 1. **Translation Quality**
- Use 'preserve' mode for literary works to maintain reading aids
- Use 'remove' mode for technical translations where furigana may confuse AI
- Use 'romanize' mode when targeting non-Japanese readers

### 2. **Display Optimization**
- Ruby text works best for Japanese-literate readers
- Parentheses format is more accessible for learners
- Hidden mode reduces visual clutter for advanced readers

### 3. **Performance**
- Furigana processing adds minimal overhead during crawling
- Dictionary lookups are optimized with proper indexing
- Frontend rendering uses efficient CSS and JavaScript

## Troubleshooting

### Common Issues

1. **Furigana not detected**
   - Check if source uses standard HTML ruby tags
   - Verify UTF-8 encoding is properly handled
   - Check crawler implementation for platform-specific markup

2. **Display issues**
   - Ensure CSS ruby styles are loaded
   - Check browser compatibility (modern browsers required)
   - Verify JavaScript furigana processing is working

3. **Translation problems**
   - Check furigana translation mode settings
   - Verify AI model handles furigana markup correctly
   - Consider adjusting processing preferences per novel

### Debug Information
Enable debug logging to track furigana processing:

```php
// Check debug.log for furigana processing messages
tail -f debug.log | grep -i furigana
```

## Browser Compatibility

- **Chrome/Edge**: Full support for HTML ruby tags
- **Firefox**: Full support for HTML ruby tags  
- **Safari**: Full support for HTML ruby tags
- **Mobile browsers**: Generally good support, may vary by device

## Future Enhancements

- AI-powered furigana generation for text without ruby markup
- Bulk furigana processing for existing content
- Advanced display customization options
- Integration with Japanese learning tools
- Export options for different furigana formats
