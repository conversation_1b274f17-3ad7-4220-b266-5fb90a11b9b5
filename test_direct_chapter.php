<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

// Test with a direct chapter URL that we know exists
$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "Testing direct chapter content extraction\n";
echo "Chapter URL: $chapterUrl\n";

// Test basic connectivity first
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $chapterUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding: gzip, deflate, br',
    'Referer: https://69shuba.cx/book/44425.htm'
]);

$html = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "CURL Error: " . ($error ?: 'None') . "\n";
echo "HTML length: " . strlen($html) . "\n";

if ($httpCode == 200 && !empty($html)) {
    echo "HTML encoding: " . mb_detect_encoding($html) . "\n";
    echo "Is valid UTF-8: " . (mb_check_encoding($html, 'UTF-8') ? 'Yes' : 'No') . "\n";
    
    echo "\nFirst 2000 characters of raw HTML:\n";
    echo "---\n";
    echo substr($html, 0, 2000) . "\n";
    echo "---\n";
    
    // Now test with the crawler
    try {
        $crawler = new Shuba69Crawler();
        echo "\nTesting with crawler:\n";
        $chapterData = $crawler->getChapterContent($chapterUrl);
        
        if (isset($chapterData['original_content'])) {
            $content = $chapterData['original_content'];
            echo "Extracted content length: " . strlen($content) . " bytes\n";
            echo "Extracted content length (UTF-8): " . mb_strlen($content, 'UTF-8') . " characters\n";
            echo "Content encoding: " . mb_detect_encoding($content) . "\n";
            
            echo "\nFirst 1000 characters of extracted content:\n";
            echo "---\n";
            echo mb_substr($content, 0, 1000, 'UTF-8') . "\n";
            echo "---\n";
            
            // Check for Chinese characters
            $chineseCharCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content);
            echo "Chinese character count: " . $chineseCharCount . "\n";
            
            if ($chineseCharCount > 0) {
                echo "✓ Chinese characters detected - extraction successful!\n";
            } else {
                echo "✗ No Chinese characters found - extraction may have failed\n";
            }
        } else {
            echo "No content extracted\n";
            print_r($chapterData);
        }
    } catch (Exception $e) {
        echo "Crawler error: " . $e->getMessage() . "\n";
    }
} else {
    echo "Failed to fetch chapter content\n";
}
?>
