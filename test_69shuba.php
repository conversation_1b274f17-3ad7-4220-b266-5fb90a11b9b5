<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$url = 'https://69shuba.cx/book/44425.htm';

try {
    echo "Testing 69shuba crawler with URL: $url\n";
    $novelInfo = $crawler->getNovelInfo($url);
    echo "Novel title: " . $novelInfo['title'] . "\n";
    
    $chapters = $crawler->getChapterList($url);
    if (!empty($chapters)) {
        echo "Found " . count($chapters) . " chapters\n";
        $firstChapter = $chapters[0];
        echo "Testing first chapter: " . $firstChapter['title'] . "\n";
        echo "Chapter URL: " . $firstChapter['url'] . "\n";
        
        $content = $crawler->getChapterContent($firstChapter['url']);
        echo "Content length: " . strlen($content) . "\n";
        echo "First 500 characters:\n";
        echo substr($content, 0, 500) . "\n";
        echo "---\n";
        echo "Character encoding check:\n";
        echo "mb_detect_encoding: " . mb_detect_encoding($content) . "\n";
        echo "mb_check_encoding: " . (mb_check_encoding($content, 'UTF-8') ? 'valid UTF-8' : 'invalid UTF-8') . "\n";
        
        // Let's also check the raw HTML
        echo "\n--- Raw HTML test ---\n";
        $html = $crawler->makeRequest($firstChapter['url']);
        echo "Raw HTML length: " . strlen($html) . "\n";
        echo "Raw HTML encoding: " . mb_detect_encoding($html) . "\n";
        echo "Raw HTML first 200 chars:\n";
        echo substr($html, 0, 200) . "\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
