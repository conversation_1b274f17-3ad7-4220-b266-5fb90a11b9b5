<?php
/**
 * Translation Service using DeepSeek AI
 * Novel Translation Application
 */

class DeepSeekTranslationService {
    private $apiKey;
    private $apiUrl;
    private $model;
    private $db;
    private $currentNovelId;
    private $chapterChunker;
    private $soundEffectsService;
    private $honorificService;
    private $cacheOptimizer;

    public function __construct() {
        $this->apiKey = DEEPSEEK_API_KEY;
        $this->apiUrl = DEEPSEEK_API_URL;
        $this->model = DEEPSEEK_CHAT_MODEL;
        $this->db = Database::getInstance();
        $this->currentNovelId = null;
        $this->chapterChunker = new ChapterChunker();
        $this->soundEffectsService = new SoundEffectsService();
        $this->honorificService = new HonorificService();
        $this->cacheOptimizer = new CacheOptimizationService();
    }

    /**
     * Translate text using DeepSeek AI with enhanced timeout handling
     */
    public function translateText(string $text, string $targetLanguage = 'en', string $sourceLanguage = 'auto', array $context = []): array {
        $startTime = microtime(true);

        try {
            // Validate input
            if (empty(trim($text))) {
                throw new Exception('Text to translate cannot be empty');
            }

            // Check if content might cause timeout and suggest chunking
            $contentAnalysis = $this->analyzeContentForTimeout($text, $context);
            if ($contentAnalysis['high_timeout_risk']) {
                error_log("DeepSeekTranslationService: High timeout risk detected - " . $contentAnalysis['reason']);

                // If this is a chunk that's still too large, try to split it further
                if (isset($context['chunk_number']) && mb_strlen($text) > 10000) {
                    return $this->handleOversizedChunk($text, $targetLanguage, $sourceLanguage, $context);
                }

                // If this is not a chunk and content is very large, suggest immediate chunking
                if (!isset($context['chunk_number']) && mb_strlen($text) > 20000) {
                    throw new Exception('Content too large for single translation. Auto-chunking will be applied automatically.');
                }
            }

            // Log name dictionary usage for debugging
            if (isset($context['names']) && !empty($context['names'])) {
                $nameCount = count($context['names']);
                file_put_contents('debug.log', "DeepSeekTranslationService: Using name dictionary with {$nameCount} entries\n", FILE_APPEND);
                foreach ($context['names'] as $name) {
                    $translation = isset($name['translation']) ? trim($name['translation']) : '';
                    $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';
                    $original = isset($name['original_name']) ? trim($name['original_name']) : '';

                    // Priority: translation > romanization > original (only if not empty)
                    if (!empty($translation)) {
                        $targetName = $translation;
                        $source = 'translation';
                    } elseif (!empty($romanization)) {
                        $targetName = $romanization;
                        $source = 'romanization';
                    } else {
                        $targetName = $original;
                        $source = 'original';
                    }

                    // Skip entries with empty original names or target names
                    if (empty($original) || empty($targetName)) {
                        file_put_contents('debug.log', "DeepSeekTranslationService: Skipping empty name entry - original: '{$original}', target: '{$targetName}'\n", FILE_APPEND);
                        continue;
                    }

                    file_put_contents('debug.log', "DeepSeekTranslationService: Name mapping - {$original} → {$targetName} (using {$source})\n", FILE_APPEND);
                }
            }

            // Prepare the prompt with timeout-optimized instructions and cache optimization
            $basePrompt = $this->buildTranslationPrompt($text, $targetLanguage, $sourceLanguage, $context, $contentAnalysis);
            $prompt = $this->cacheOptimizer->optimizePromptForCaching($basePrompt, $context);

            // Make API request with adaptive timeout
            $response = $this->makeApiRequest($prompt, $context, $contentAnalysis);

            if (!$response['success']) {
                // Check if this was a timeout and we can retry with smaller chunks
                if ($this->isTimeoutError($response)) {
                    error_log("DeepSeekTranslationService: Timeout detected - " . $response['error']);

                    if (!isset($context['chunk_number'])) {
                        // This is not a chunk, suggest auto-chunking
                        throw new Exception('Translation timed out. Content will be automatically split into smaller chunks for processing.');
                    } else {
                        // This is already a chunk that timed out, try emergency splitting
                        if (mb_strlen($text) > 8000) {
                            return $this->handleOversizedChunk($text, $targetLanguage, $sourceLanguage, $context);
                        } else {
                            throw new Exception('Chunk translation timed out despite small size. API may be overloaded.');
                        }
                    }
                }
                throw new Exception($response['error']);
            }

            $translatedText = $this->extractTranslationFromResponse($response['data']);

            // Clean title translations if needed
            if (isset($context['type']) && $context['type'] === 'title') {
                $translatedText = $this->cleanTitleTranslation($translatedText);
            } else {
                // For content translations, ensure formatting is preserved
                $translatedText = $this->restoreFormattingIfLost($text, $translatedText, $context);

                // Restore any punctuation that might have been converted by the API
                $translatedText = $this->restorePunctuationSymbols($text, $translatedText);
            }

            $executionTime = microtime(true) - $startTime;

            // Extract cache hit information from response
            $cacheInfo = $this->extractCacheInfo($response['data']);

            return [
                'success' => true,
                'original_text' => $text,
                'translated_text' => trim($translatedText),
                'target_language' => $targetLanguage,
                'source_language' => $sourceLanguage,
                'execution_time' => round($executionTime, 2),
                'cache_hit_tokens' => $cacheInfo['cache_hit_tokens'],
                'cache_miss_tokens' => $cacheInfo['cache_miss_tokens'],
                'total_tokens' => $cacheInfo['total_tokens'],
                'cache_hit_rate' => $cacheInfo['cache_hit_rate'],
                'estimated_cost_savings' => $cacheInfo['estimated_cost_savings']
            ];

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2),
                'original_text' => $text,
                'target_language' => $targetLanguage
            ];
        }
    }

    /**
     * Analyze content for timeout risk
     */
    private function analyzeContentForTimeout(string $text, array $context): array {
        $length = mb_strlen($text);
        $analysis = [
            'high_timeout_risk' => false,
            'reason' => '',
            'recommended_action' => '',
            'estimated_time' => 0
        ];

        // Base timeout risk on content length (more conservative thresholds)
        if ($length > 15000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 15,000 characters';
            $analysis['recommended_action'] = 'chunk_content';
        } elseif ($length > 12000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Content length exceeds 12,000 characters';
            $analysis['recommended_action'] = 'reduce_timeout';
        }

        // Check for complexity factors that increase timeout risk
        $complexityFactors = [];

        // High dialogue density (more conservative threshold)
        $dialogueMatches = preg_match_all('/「[^」]*」|"[^"]*"/', $text);
        if ($dialogueMatches > 30) {
            $complexityFactors[] = 'high_dialogue_density';
        }

        // Technical terms or complex vocabulary
        $kanjiDensity = preg_match_all('/[\x{4e00}-\x{9faf}]/u', $text) / max(1, $length);
        if ($kanjiDensity > 0.3) { // More conservative threshold
            $complexityFactors[] = 'high_kanji_density';
        }

        // Check for furigana content (more processing intensive)
        if (preg_match('/[\x{3040}-\x{309F}]/u', $text)) {
            $complexityFactors[] = 'furigana_content';
        }

        // Check for long continuous text blocks (harder to process)
        $longBlocks = preg_match_all('/[^\n]{200,}/', $text);
        if ($longBlocks > 5) {
            $complexityFactors[] = 'long_text_blocks';
        }

        // If we have complexity factors and moderate length, increase risk (more conservative)
        if (!empty($complexityFactors) && $length > 8000) {
            $analysis['high_timeout_risk'] = true;
            $analysis['reason'] = 'Complex content (' . implode(', ', $complexityFactors) . ') with length ' . $length . ' characters';
            $analysis['recommended_action'] = 'chunk_content';
        }

        // Estimate translation time (rough approximation)
        $analysis['estimated_time'] = max(30, ($length / 100) * 2); // ~2 seconds per 100 characters

        return $analysis;
    }

    /**
     * Handle oversized chunk by splitting it further
     */
    private function handleOversizedChunk(string $text, string $targetLanguage, string $sourceLanguage, array $context): array {
        error_log("DeepSeekTranslationService: Handling oversized chunk of " . mb_strlen($text) . " characters");

        // Split the chunk into smaller pieces
        $pieces = $this->splitOversizedContent($text);
        $translatedPieces = [];
        $totalTime = 0;

        foreach ($pieces as $index => $piece) {
            $pieceContext = $context;
            $pieceContext['sub_chunk'] = $index + 1;
            $pieceContext['total_sub_chunks'] = count($pieces);

            $result = $this->translateText($piece, $targetLanguage, $sourceLanguage, $pieceContext);

            if (!$result['success']) {
                return $result; // Return error if any piece fails
            }

            $translatedPieces[] = $result['translated_text'];
            $totalTime += $result['execution_time'];
        }

        return [
            'success' => true,
            'original_text' => $text,
            'translated_text' => implode("\n\n", $translatedPieces),
            'target_language' => $targetLanguage,
            'source_language' => $sourceLanguage,
            'execution_time' => $totalTime,
            'sub_chunks_processed' => count($pieces)
        ];
    }

    /**
     * Split oversized content into smaller pieces
     */
    private function splitOversizedContent(string $text): array {
        $maxPieceSize = 8000; // Smaller pieces for emergency splitting
        $pieces = [];

        // Try to split by paragraphs first
        $paragraphs = preg_split('/\n\s*\n/', $text, -1, PREG_SPLIT_NO_EMPTY);

        $currentPiece = '';
        foreach ($paragraphs as $paragraph) {
            if (mb_strlen($currentPiece . $paragraph) > $maxPieceSize && !empty($currentPiece)) {
                $pieces[] = trim($currentPiece);
                $currentPiece = $paragraph;
            } else {
                $currentPiece .= ($currentPiece ? "\n\n" : '') . $paragraph;
            }
        }

        if (!empty($currentPiece)) {
            $pieces[] = trim($currentPiece);
        }

        return $pieces;
    }

    /**
     * Check if error indicates a timeout
     */
    private function isTimeoutError(array $response): bool {
        if (!isset($response['error'])) return false;

        $error = strtolower($response['error']);
        $timeoutIndicators = [
            'timeout',
            'timed out',
            'time limit',
            'deadline exceeded',
            'operation timed out',
            'curl error',
            'request timeout',
            'gateway timeout',
            'connection timeout',
            'read timeout',
            'execution timeout',
            'too large',
            'content too large',
            'content length exceeds'
        ];

        foreach ($timeoutIndicators as $indicator) {
            if (strpos($error, $indicator) !== false) {
                return true;
            }
        }

        // Check for specific HTTP codes that indicate timeout
        if (isset($response['details']['http_code'])) {
            $timeoutCodes = [504, 408, 524]; // Gateway Timeout, Request Timeout, Cloudflare Timeout
            if (in_array($response['details']['http_code'], $timeoutCodes)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Make API request to DeepSeek
     */
    private function makeApiRequest(string $prompt, array $context = [], array $contentAnalysis = []): array {
        $maxRetries = 3;
        $retryDelay = 1; // seconds

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            file_put_contents('debug.log', "DeepSeekTranslationService: Attempt {$attempt} of {$maxRetries}\n", FILE_APPEND);

            $result = $this->executeSingleApiRequest($prompt, $context, $attempt);

            if ($result['success']) {
                return $result;
            }

            // Check if error is retryable
            if (!$this->isRetryableError($result)) {
                file_put_contents('debug.log', "DeepSeekTranslationService: Non-retryable error, stopping retries\n", FILE_APPEND);
                return $result;
            }

            // Wait before retry (exponential backoff)
            if ($attempt < $maxRetries) {
                $delay = $retryDelay * pow(2, $attempt - 1);
                file_put_contents('debug.log', "DeepSeekTranslationService: Waiting {$delay}s before retry\n", FILE_APPEND);
                sleep($delay);
            }
        }

        return $result;
    }

    /**
     * Execute single API request
     */
    private function executeSingleApiRequest(string $prompt, array $context, int $attempt): array {
        error_log("DeepSeekTranslationService: Making API request (attempt {$attempt})");
        error_log("DeepSeekTranslationService: API Key length: " . strlen($this->apiKey));
        error_log("DeepSeekTranslationService: Prompt length: " . strlen($prompt));

        // Build request data in OpenAI format
        $requestData = [
            'model' => $this->model,
            'messages' => [
                [
                    'role' => 'system',
                    'content' => 'You are a professional translator specializing in Japanese to English translation for novels and literature.'
                ],
                [
                    'role' => 'user',
                    'content' => $prompt
                ]
            ],
            'temperature' => 0.3,
            'max_tokens' => 8192,
            'stream' => false
        ];

        // Adjust parameters for title translations
        if (isset($context['type']) && $context['type'] === 'title') {
            $requestData['temperature'] = 0.1;
            $requestData['max_tokens'] = 100;
        }

        $jsonData = json_encode($requestData);
        error_log("DeepSeekTranslationService: Request data size: " . strlen($jsonData) . " bytes");

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $this->apiUrl,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'Authorization: Bearer ' . $this->apiKey,
            ],
            CURLOPT_TIMEOUT => TRANSLATION_TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log response details
        error_log("DeepSeekTranslationService: HTTP Code: " . $httpCode);
        error_log("DeepSeekTranslationService: Response length: " . strlen($response));

        if ($error) {
            error_log("DeepSeekTranslationService: cURL error: " . $error);
            return ['success' => false, 'error' => "cURL error: {$error}", 'retryable' => true];
        }

        if ($httpCode !== 200) {
            error_log("DeepSeekTranslationService: HTTP error response: " . substr($response, 0, 500));

            // Parse error response for better error handling
            $errorDetails = $this->parseErrorResponse($response, $httpCode);

            return [
                'success' => false,
                'error' => "HTTP error: {$httpCode}",
                'details' => $errorDetails,
                'retryable' => $this->isHttpCodeRetryable($httpCode),
                'response' => $response
            ];
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("DeepSeekTranslationService: JSON decode error: " . json_last_error_msg());
            error_log("DeepSeekTranslationService: Raw response: " . substr($response, 0, 500));
            return ['success' => false, 'error' => 'Invalid JSON response: ' . json_last_error_msg(), 'retryable' => false];
        }

        error_log("DeepSeekTranslationService: API request successful");
        return ['success' => true, 'data' => $decodedResponse];
    }

    /**
     * Extract translation from DeepSeek API response
     */
    public function extractTranslationFromResponse(array $response): string {
        if (isset($response['choices'][0]['message']['content'])) {
            return trim($response['choices'][0]['message']['content']);
        }

        throw new Exception('No translation found in response');
    }

    /**
     * Extract cache hit information from DeepSeek API response
     */
    private function extractCacheInfo(array $response): array {
        $cacheInfo = [
            'cache_hit_tokens' => 0,
            'cache_miss_tokens' => 0,
            'total_tokens' => 0,
            'cache_hit_rate' => 0.0,
            'estimated_cost_savings' => 0.0
        ];

        if (isset($response['usage'])) {
            $usage = $response['usage'];

            // Extract cache hit/miss tokens (new fields from DeepSeek)
            $cacheInfo['cache_hit_tokens'] = $usage['prompt_cache_hit_tokens'] ?? 0;
            $cacheInfo['cache_miss_tokens'] = $usage['prompt_cache_miss_tokens'] ?? 0;

            // Calculate total prompt tokens
            $cacheInfo['total_tokens'] = $cacheInfo['cache_hit_tokens'] + $cacheInfo['cache_miss_tokens'];

            // Calculate cache hit rate
            if ($cacheInfo['total_tokens'] > 0) {
                $cacheInfo['cache_hit_rate'] = round(
                    ($cacheInfo['cache_hit_tokens'] / $cacheInfo['total_tokens']) * 100,
                    2
                );
            }

            // Calculate estimated cost savings (DeepSeek pricing: $0.014 vs $0.14 per million tokens)
            $cacheInfo['estimated_cost_savings'] = round(
                ($cacheInfo['cache_hit_tokens'] / 1000000) * (0.14 - 0.014),
                6
            );

            // Log cache performance for monitoring
            if ($cacheInfo['total_tokens'] > 0) {
                error_log("DeepSeekTranslationService: Cache Performance - Hit: {$cacheInfo['cache_hit_tokens']}, Miss: {$cacheInfo['cache_miss_tokens']}, Rate: {$cacheInfo['cache_hit_rate']}%, Savings: \${$cacheInfo['estimated_cost_savings']}");
            }
        }

        return $cacheInfo;
    }

    /**
     * Build translation prompt with timeout optimization
     */
    public function buildTranslationPrompt(string $text, string $targetLanguage, string $sourceLanguage, array $context, array $contentAnalysis = []): string {
        $languageNames = [
            'en' => 'English',
            'ja' => 'Japanese',
            'zh' => 'Chinese',
            'ko' => 'Korean'
        ];

        $targetLangName = $languageNames[$targetLanguage] ?? $targetLanguage;
        $sourceLangName = $sourceLanguage === 'auto' ? 'the source language' : ($languageNames[$sourceLanguage] ?? $sourceLanguage);

        $prompt = "Translate the following text from {$sourceLangName} to {$targetLangName}.\n\n";

        // Add timeout optimization instructions for large content
        if (!empty($contentAnalysis) && $contentAnalysis['high_timeout_risk']) {
            $prompt .= "IMPORTANT: This is a large content block. Please provide a direct, efficient translation without extensive explanations or commentary to ensure timely completion.\n\n";
        }

        // Add strict no-explanation instruction for all translations
        $prompt .= "CRITICAL: Provide ONLY the translation. Do not add any notes, explanations, comments, or meta-text about the translation process, sound effects, formatting, or any other aspects. Your response should contain only the translated text.\n\n";

        // Add explicit anti-romanization instructions with maximum emphasis
        $prompt .= "🚨🚨🚨 CRITICAL TRANSLATION REQUIREMENTS - ABSOLUTE MANDATORY COMPLIANCE 🚨🚨🚨\n\n";
        $prompt .= "⚠️ ZERO TOLERANCE FOR ROMANIZATION - EVERYTHING MUST BE TRANSLATED TO ENGLISH ⚠️\n\n";
        $prompt .= "ABSOLUTE TRANSLATION RULES:\n";
        $prompt .= "1. 🔥 TRANSLATE ALL TEXT TO ENGLISH: Every single word, phrase, sentence, and expression MUST be translated to proper, natural English\n";
        $prompt .= "2. 🚫 ZERO ROMANIZATION: Do NOT romanize ANY Japanese text - translate it to English instead\n";
        $prompt .= "3. 🚫 NO JAPANESE WORDS: Do not leave ANY Japanese words in romanized form in the translation\n";
        $prompt .= "4. ✅ ONLY EXCEPTIONS: The ONLY things that should remain in romanized form are:\n";
        $prompt .= "   - Character names (when specified in the name dictionary below)\n";
        $prompt .= "   - Honorifics attached to names (-san, -kun, -chan, -sama, -sensei, -senpai, etc.)\n";
        $prompt .= "5. 🔥 DIALOGUE AND NARRATIVE: ALL dialogue, thoughts, internal monologue, and narrative text MUST be in proper English\n";
        $prompt .= "6. 🔥 ONOMATOPOEIA: ALL sound effects and onomatopoeia MUST be translated to descriptive English words:\n";
        $prompt .= "   - 'doki doki' → 'nervously' or 'with a pounding heart'\n";
        $prompt .= "   - 'furafura' → 'unsteadily' or 'wobbling'\n";
        $prompt .= "   - 'gira gira' → 'intensely' or 'blazingly'\n";
        $prompt .= "   - 'don' → 'bang' or 'thud'\n";
        $prompt .= "   - 'za' → 'swish' or 'whoosh'\n";
        $prompt .= "7. 🔥 COMMON PHRASES: Translate ALL Japanese expressions to natural English equivalents:\n";
        $prompt .= "   - 'naruhodo' → 'I see' or 'I understand'\n";
        $prompt .= "   - 'sugoi' → 'amazing' or 'incredible'\n";
        $prompt .= "   - 'yabai' → 'dangerous' or 'bad' or 'awesome' (context dependent)\n";
        $prompt .= "   - 'maji de' → 'seriously' or 'really'\n";
        $prompt .= "   - 'sou da ne' → 'that's right' or 'yes, that's so'\n";
        $prompt .= "   - 'hai' → 'yes'\n";
        $prompt .= "   - 'iie' → 'no'\n";
        $prompt .= "   - 'arigatou' → 'thank you'\n";
        $prompt .= "   - 'sumimasen' → 'excuse me' or 'sorry'\n\n";
        $prompt .= "❌ ABSOLUTELY FORBIDDEN: Leaving Japanese phrases, words, or expressions in romanized form\n";
        $prompt .= "❌ ABSOLUTELY FORBIDDEN: Using romanized Japanese instead of English translation\n";
        $prompt .= "✅ ABSOLUTELY REQUIRED: Translate everything to natural, fluent, readable English\n";
        $prompt .= "✅ ABSOLUTELY REQUIRED: Make the text comprehensible to English readers who don't know Japanese\n\n";

        // Add comprehensive formatting and structure preservation instructions with maximum emphasis
        $prompt .= "🚨🚨🚨 CRITICAL PUNCTUATION PRESERVATION - ABSOLUTE MANDATORY COMPLIANCE 🚨🚨🚨\n\n";
        $prompt .= "⚠️⚠️⚠️ ZERO TOLERANCE FOR PUNCTUATION CONVERSION - PRESERVE ALL ORIGINAL SYMBOLS ⚠️⚠️⚠️\n\n";
        $prompt .= "🔥🔥🔥 MANDATORY PUNCTUATION PRESERVATION RULES 🔥🔥🔥\n";
        $prompt .= "1. 🚫 NEVER CONVERT ANY Japanese quotation marks to different types\n";
        $prompt .= "2. 🚫 NEVER CONVERT 『』 to 「」 - these are DIFFERENT symbols that must stay separate\n";
        $prompt .= "3. 🚫 NEVER CONVERT 「」 to 『』 - these are DIFFERENT symbols that must stay separate\n";
        $prompt .= "4. 🚫 NEVER CONVERT Japanese/Chinese brackets: 【】〖〗〔〕〈〉《》 MUST remain exactly as they are\n";
        $prompt .= "5. 🚫 NEVER CONVERT Japanese parentheses: （） MUST remain as （） - NOT as ()\n";
        $prompt .= "6. 🚫 NEVER CONVERT special symbols: ・※〜～ MUST remain exactly as they are\n";
        $prompt .= "7. 🚫 NEVER use English punctuation equivalents for Asian symbols\n\n";
        $prompt .= "✅✅✅ EXACT SYMBOL PRESERVATION REQUIREMENTS ✅✅✅\n";
        $prompt .= "🔸 If original has 「 → translation MUST have 「 (never change to 『 or \")\n";
        $prompt .= "🔸 If original has 」 → translation MUST have 」 (never change to 』 or \")\n";
        $prompt .= "🔸 If original has 『 → translation MUST have 『 (never change to 「 or \")\n";
        $prompt .= "🔸 If original has 』 → translation MUST have 』 (never change to 」 or \")\n";
        $prompt .= "🔸 These are FOUR DIFFERENT symbols - never substitute one for another!\n\n";
        $prompt .= "🚨 CRITICAL: If the original text uses ONLY 『』 quotes, your translation must use ONLY 『』 quotes!\n";
        $prompt .= "🚨 CRITICAL: If the original text uses ONLY 「」 quotes, your translation must use ONLY 「」 quotes!\n";
        $prompt .= "🚨 CRITICAL: Never 'standardize' or 'normalize' punctuation - preserve exactly what's in the original!\n\n";
        $prompt .= "- Keep 【 as 【 (NOT as [)\n";
        $prompt .= "- Keep 】 as 】 (NOT as ])\n";
        $prompt .= "- Keep 〖 as 〖 (NOT as [)\n";
        $prompt .= "- Keep 〗 as 〗 (NOT as ])\n";
        $prompt .= "- Keep 〔 as 〔 (NOT as [)\n";
        $prompt .= "- Keep 〕 as 〕 (NOT as ])\n";
        $prompt .= "- Keep 〈 as 〈 (NOT as <)\n";
        $prompt .= "- Keep 〉 as 〉 (NOT as >)\n";
        $prompt .= "- Keep 《 as 《 (NOT as \")\n";
        $prompt .= "- Keep 》 as 》 (NOT as \")\n";
        $prompt .= "- Keep （ as （ (NOT as ()\n";
        $prompt .= "- Keep ） as ） (NOT as ))\n";
        $prompt .= "- Keep ・ as ・ (NOT as •)\n";
        $prompt .= "- Keep ※ as ※ (NOT as *)\n";
        $prompt .= "- Keep 〜 as 〜 (NOT as ~)\n";
        $prompt .= "- Keep ～ as ～ (NOT as ~)\n\n";
        $prompt .= "🚨 FORMATTING AND STRUCTURE PRESERVATION RULES 🚨\n";
        $prompt .= "1. PRESERVE EXACT LINE BREAKS: Every single \\n (line break) in the original text MUST be preserved as \\n in the translation. Do NOT merge lines.\n";
        $prompt .= "2. PRESERVE PARAGRAPH BREAKS: Every \\n\\n (double line break) in the original MUST remain as \\n\\n in the translation. Do NOT merge paragraphs.\n";
        $prompt .= "3. MAINTAIN TEXT STRUCTURE: The translated text must have the same visual structure as the original - same number of lines, same paragraph breaks, same spacing.\n\n";
        $prompt .= "❌ FORBIDDEN: Do NOT create continuous text blocks. Do NOT merge separate lines or paragraphs.\n";
        $prompt .= "✅ REQUIRED: Preserve the exact visual layout and structure of the original text.\n\n";
        $prompt .= "4. MAINTAIN PARAGRAPH BREAKS: Keep the same paragraph structure and spacing as the original text. Double line breaks (\\n\\n) must remain double line breaks.\n";
        $prompt .= "5. PRESERVE NARRATIVE FLOW: Maintain the exact sequence of dialogue, narration, and action descriptions as they appear in the original.\n";
        $prompt .= "5. PRESERVE DIALOGUE FLOW: Ensure conversations maintain their natural flow and speaker changes are clear\n";
        $prompt .= "6. KEEP ORIGINAL TEXT RHYTHM: Maintain the pacing and rhythm of the original through proper punctuation and line breaks\n\n";

        // Add specific instructions based on context
        if (isset($context['type'])) {
            switch ($context['type']) {
                case 'title':
                    $prompt .= "This is a novel title. Provide a single, clean translation without explanations or alternatives.\n\n";

                    // Add honorific preservation instructions for titles
                    $detectedLanguage = $this->honorificService->detectLanguage($text);
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions($detectedLanguage);
                    break;
                case 'chapter':
                case 'content':
                case 'chunk':
                    $prompt .= "This is novel content. Maintain the narrative style and preserve character names.\n";

                    // Add narrative perspective instructions based on context analysis
                    if (isset($context['narrative_context'])) {
                        $narrativeContext = $context['narrative_context'];
                        $prompt .= $this->buildNarrativePerspectiveInstructions($narrativeContext, $context);
                    }

                    // Add specific dialogue and narrative formatting instructions
                    $prompt .= "DIALOGUE AND NARRATIVE FORMATTING:\n";
                    $prompt .= "- Preserve all dialogue quotation marks and formatting exactly as they appear\n";
                    $prompt .= "- Maintain speaker attribution and dialogue tags in their original positions\n";
                    $prompt .= "- Keep conversation flow natural and easy to follow\n";
                    $prompt .= "- Preserve narrative descriptions between dialogue exactly as formatted\n";
                    $prompt .= "- Maintain the original rhythm and pacing through proper punctuation\n";
                    $prompt .= "- Keep all exclamation points, question marks, and ellipses in dialogue\n";
                    $prompt .= "- Preserve any special formatting for thoughts, internal monologue, or emphasis\n\n";

                    // Add chunk context if available
                    if (isset($context['chunk_number'])) {
                        $prompt .= "This is chunk {$context['chunk_number']}";
                        if (isset($context['total_chunks'])) {
                            $prompt .= " of {$context['total_chunks']}";
                        }
                        $prompt .= " from a larger chapter. Maintain consistency with the overall narrative.\n";

                        // Add context from previous/next chunks if available
                        if (isset($context['previous_context'])) {
                            $prompt .= "Previous context: " . substr($context['previous_context'], 0, 100) . "...\n";
                        }
                        if (isset($context['next_context'])) {
                            $prompt .= "Following context: " . substr($context['next_context'], 0, 100) . "...\n";
                        }
                    }

                    // Sound effects processing removed - all onomatopoeia will be translated to descriptive English

                    // Add dialogue-specific instructions if dialogue is detected
                    $prompt .= $this->addDialogueSpecificInstructions($text);

                    // Add honorific preservation instructions
                    $detectedLanguage = $this->honorificService->detectLanguage($text);
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions($detectedLanguage);
                    $prompt .= "\n";
                    break;
                case 'synopsis':
                    $prompt .= "This is a novel synopsis/description. Keep it engaging and informative.\n\n";

                    // Add honorific preservation instructions for synopsis
                    $detectedLanguage = $this->honorificService->detectLanguage($text);
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions($detectedLanguage);
                    break;
            }
        }

        // Add name consistency instructions with MANDATORY enforcement
        if (isset($context['names']) && !empty($context['names'])) {
            $prompt .= "🚨🚨🚨 MANDATORY NAME DICTIONARY - ABSOLUTE COMPLIANCE REQUIRED 🚨🚨🚨\n\n";
            $prompt .= "⚠️ CHARACTER NAME ENFORCEMENT: You MUST use these exact name translations throughout the text:\n\n";
            $nameCount = 0;
            foreach ($context['names'] as $name) {
                // Use translation if available and not empty, otherwise romanization, otherwise original
                $translation = isset($name['translation']) ? trim($name['translation']) : '';
                $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';
                $original = isset($name['original_name']) ? trim($name['original_name']) : '';

                // Priority: translation > romanization > original (only if not empty)
                if (!empty($translation)) {
                    $targetName = $translation;
                } elseif (!empty($romanization)) {
                    $targetName = $romanization;
                } else {
                    $targetName = $original;
                }

                // Skip entries with empty original names or target names
                if (empty($original) || empty($targetName)) {
                    continue;
                }

                $prompt .= "🔹 {$original} → **{$targetName}** (MANDATORY - use this exact form)\n";
                $nameCount++;
            }
            $prompt .= "\n🚨 CRITICAL REQUIREMENTS:\n";
            $prompt .= "- You MUST use these exact name forms for consistency across all translations\n";
            $prompt .= "- Do NOT create new translations for these names - use the provided forms EXACTLY\n";
            $prompt .= "- Do NOT romanize these names differently - use the specified forms\n";
            $prompt .= "- These name mappings are MANDATORY and override any other considerations\n";
            $prompt .= "- Found {$nameCount} character names that MUST be used consistently\n\n";
            $prompt .= "✅ SUCCESS CRITERIA: Every occurrence of these original names must be replaced with their specified translations\n\n";
        }

        // Add final comprehensive reminder with maximum emphasis
        $prompt .= "🔥🔥🔥 FINAL CRITICAL REMINDER - READ CAREFULLY 🔥🔥🔥\n\n";
        $prompt .= "🚨 TRANSLATION QUALITY REQUIREMENTS:\n";
        $prompt .= "1. 🔥 ENGLISH ONLY: Your translation must be 100% readable English - no Japanese words in romanized form\n";
        $prompt .= "2. 🔥 NATURAL FLOW: The translation must read naturally to English speakers who don't know Japanese\n";
        $prompt .= "3. 🔥 COMPLETE TRANSLATION: Every word, phrase, and expression must be translated to English\n";
        $prompt .= "4. 🔥 NAME DICTIONARY: Use the provided name translations EXACTLY as specified\n";
        $prompt .= "5. 🔥 FORMATTING: Preserve the EXACT structure, formatting, and punctuation of the original text\n\n";
        $prompt .= "🚨 FORMATTING REQUIREMENTS:\n";
        $prompt .= "- Maintain ALL line breaks (\\n) exactly as they appear\n";
        $prompt .= "- Maintain ALL paragraph breaks (\\n\\n) exactly as they appear\n";
        $prompt .= "- Maintain ALL dialogue formatting and punctuation marks\n";
        $prompt .= "- PRESERVE Japanese/Chinese punctuation symbols: 【】〖〗〔〕〈〉《》「」『』（）・※〜～\n";
        $prompt .= "- Do NOT convert Asian punctuation to English equivalents\n";
        $prompt .= "- Do NOT create continuous text - preserve the original layout\n";
        $prompt .= "- Do NOT add explanations, alternatives, or commentary\n";
        $prompt .= "- Provide ONLY the direct translation with PRESERVED formatting\n\n";
        $prompt .= "🚨 SUCCESS CRITERIA:\n";
        $prompt .= "- An English reader should understand everything without knowing Japanese\n";
        $prompt .= "- Character names should match the provided dictionary exactly\n";
        $prompt .= "- All onomatopoeia should be descriptive English words\n";
        $prompt .= "- Japanese/Chinese punctuation symbols should remain in their original form\n";
        $prompt .= "- The text should flow naturally in English while preserving original formatting\n\n";
        $prompt .= "EXAMPLE: If original has 5 lines with 2 paragraph breaks, translation must have 5 lines with 2 paragraph breaks.\n\n";

        $prompt .= "Text to translate:\n" . $text;

        return $prompt;
    }

    /**
     * Build narrative perspective instructions based on context analysis
     */
    private function buildNarrativePerspectiveInstructions(array $narrativeContext, array $fullContext): string {
        $instructions = "\n🚨🚨🚨 CRITICAL NARRATIVE PERSPECTIVE REQUIREMENTS 🚨🚨🚨\n\n";

        $perspective = $narrativeContext['narrative_voice'] ?? 'third_person';
        $confidence = $narrativeContext['narrative_confidence'] ?? 0.3;

        // Check for consistency with previous chunk
        $previousPerspective = null;
        if (isset($fullContext['previous_narrative_context']) && isset($fullContext['previous_narrative_context']['narrative_voice'])) {
            $previousPerspective = $fullContext['previous_narrative_context']['narrative_voice'];
        }

        // IMPLEMENT DEFAULT THIRD PERSON RULE FOR NARRATIVE TEXT
        // Always enforce third person for narrative descriptions, regardless of source perspective
        $enforcedPerspective = 'third_person'; // Default to third person for all narrative text

        // Only preserve original perspective for dialogue and internal thoughts
        $instructions .= "🔥 DEFAULT THIRD PERSON RULE: All narrative text will be converted to third person perspective.\n";
        $instructions .= "📋 CONTENT TYPE HANDLING:\n";
        $instructions .= "• Narrative descriptions (actions, scenes, observations) → ALWAYS third person\n";
        $instructions .= "• Character dialogue (within quotation marks) → Preserve original perspective\n";
        $instructions .= "• Inner thoughts/internal monologue → Preserve original perspective\n\n";

        // Since we're enforcing default third person rule, always use third person instructions
        $instructions .= "📝 MANDATORY THIRD PERSON NARRATIVE (DEFAULT RULE):\n";
        $instructions .= "1. 🔥 NARRATIVE TEXT → THIRD PERSON: All narrative descriptions, actions, scene descriptions, and observations MUST use third person pronouns ('he', 'she', 'they', 'him', 'her', 'them')\n";
        $instructions .= "2. 🔥 NEVER USE FIRST PERSON IN NARRATIVE: Do NOT use 'I', 'me', 'my', 'mine', 'myself' for narrative descriptions\n";
        $instructions .= "3. 🔥 MAINTAIN EXTERNAL PERSPECTIVE: Describe all actions and observations from an outside observer's viewpoint\n";
        $instructions .= "4. 🔥 DIALOGUE ATTRIBUTION: Use 'he said', 'she asked', 'they replied' for character speech attribution\n";
        $instructions .= "5. 🔥 CHARACTER THOUGHTS IN NARRATIVE: Express character thoughts as 'he thought', 'she wondered', 'they realized'\n";
        $instructions .= "6. 🔥 ACTIONS AND OBSERVATIONS: Use 'he saw', 'she heard', 'they felt', 'he moved', 'she did'\n\n";

        $instructions .= "✅ EXCEPTIONS (PRESERVE ORIGINAL PERSPECTIVE):\n";
        $instructions .= "• Character dialogue within quotation marks (「」『』\"\" etc.) → Characters can speak in any person\n";
        $instructions .= "• Direct inner thoughts/internal monologue → Preserve original perspective if clearly marked\n";
        $instructions .= "• Direct speech attribution within quotes → Follow character's natural speech patterns\n\n";

        $instructions .= "❌ NARRATIVE CONVERSION EXAMPLES:\n";
        $instructions .= "- 'I thought to myself' → ✅ 'He thought to himself'\n";
        $instructions .= "- 'My heart raced' → ✅ 'His heart raced'\n";
        $instructions .= "- 'I decided to go' → ✅ 'She decided to go'\n";
        $instructions .= "- 'I felt confused' → ✅ 'They felt confused'\n";
        $instructions .= "- 'I walked to the door' → ✅ 'He walked to the door'\n";
        $instructions .= "- 'My eyes widened' → ✅ 'Her eyes widened'\n\n";

        $instructions .= "✅ DIALOGUE PRESERVATION EXAMPLES:\n";
        $instructions .= "- 「I don't understand」 → ✅ Keep as 「I don't understand」 (character dialogue)\n";
        $instructions .= "- \"You should help me\" → ✅ Keep as \"You should help me\" (character dialogue)\n";
        $instructions .= "- 『We need to go』 → ✅ Keep as 『We need to go』 (character dialogue)\n\n";

        $instructions .= "🔥 ABSOLUTE CONSISTENCY REQUIREMENT (DEFAULT THIRD PERSON RULE):\n";
        $instructions .= "- ALL NARRATIVE TEXT must use third person perspective ('he', 'she', 'they')\n";
        $instructions .= "- NO first person pronouns ('I', 'me', 'my') in narrative descriptions\n";
        $instructions .= "- NO mixing of first and third person within narrative text\n";
        $instructions .= "- NO switching perspectives mid-paragraph or mid-sentence in narrative\n";
        $instructions .= "- Character dialogue within quotes can use any person (preserve original)\n";
        $instructions .= "- Inner thoughts/monologue can preserve original perspective if clearly marked\n";
        $instructions .= "- But ALL narrative text and action descriptions MUST be third person\n\n";

        // Add perspective analysis information for debugging (but enforce third person regardless)
        if ($confidence > 0) {
            $instructions .= "📊 ORIGINAL PERSPECTIVE ANALYSIS (for reference only - DEFAULT THIRD PERSON RULE APPLIED):\n";
            $instructions .= "- Original detected perspective: {$perspective} (confidence: " . round($confidence * 100, 1) . "%)\n";
            $instructions .= "- Enforced perspective: third_person (DEFAULT RULE)\n";
            if (!empty($narrativeContext['perspective_indicators']['first_person'])) {
                $instructions .= "- First person indicators found: " . implode(', ', array_slice($narrativeContext['perspective_indicators']['first_person'], 0, 3)) . "\n";
            }
            if (!empty($narrativeContext['perspective_indicators']['third_person'])) {
                $instructions .= "- Third person indicators found: " . implode(', ', array_slice($narrativeContext['perspective_indicators']['third_person'], 0, 3)) . "\n";
            }
            $instructions .= "- NOTE: All narrative text will be converted to third person regardless of original perspective\n";
            $instructions .= "\n";
        }

        return $instructions;
    }

    // Sound effects context instruction methods removed - no longer needed since all onomatopoeia are translated to English

    /**
     * Detect dialogue content and add specific formatting instructions
     */
    private function addDialogueSpecificInstructions(string $text): string {
        $instructions = "";

        // Detect various dialogue patterns
        $hasJapaneseQuotes = preg_match('/[「」『』]/', $text);
        $hasWesternQuotes = preg_match('/["\'"]/', $text);
        $hasDialogueTags = preg_match('/(said|asked|replied|whispered|shouted|muttered|exclaimed)/i', $text);
        $hasConversation = preg_match('/[「"\'"][^「"\']*[」"\'"][^「"\']*[「"\'"]/', $text);

        if ($hasJapaneseQuotes || $hasWesternQuotes || $hasDialogueTags || $hasConversation) {
            $instructions .= "DIALOGUE DETECTED - SPECIAL FORMATTING REQUIREMENTS:\n";

            if ($hasJapaneseQuotes) {
                // Check which specific types are present
                $has_single_quotes = preg_match('/[「」]/', $text);
                $has_double_quotes = preg_match('/[『』]/', $text);

                if ($has_single_quotes && $has_double_quotes) {
                    $instructions .= "- MIXED Japanese quotation marks detected: Preserve BOTH 「」 and 『』 exactly as they appear - do NOT convert between types\n";
                } elseif ($has_single_quotes) {
                    $instructions .= "- Japanese single quotation marks 「」 detected: Keep ALL as 「」 - do NOT convert to 『』 or English quotes\n";
                } elseif ($has_double_quotes) {
                    $instructions .= "- Japanese double quotation marks 『』 detected: Keep ALL as 『』 - do NOT convert to 「」 or English quotes\n";
                }
            }

            if ($hasWesternQuotes) {
                $instructions .= "- Quotation marks detected: Maintain exact placement and nesting of quotes\n";
            }

            if ($hasDialogueTags) {
                $instructions .= "- Dialogue tags detected: Keep speaker attribution clear and natural\n";
            }

            if ($hasConversation) {
                $instructions .= "- Multi-speaker conversation detected: Ensure each speaker's lines are clearly distinguished\n";
            }

            $instructions .= "- Preserve all dialogue punctuation including commas, periods, exclamation points, and question marks within quotes\n";
            $instructions .= "- Maintain the natural flow and rhythm of conversation\n";
            $instructions .= "- Keep any narrative interruptions within dialogue properly formatted\n\n";
        }

        return $instructions;
    }

    /**
     * Check if an error is retryable
     */
    private function isRetryableError(array $result): bool {
        return isset($result['retryable']) && $result['retryable'] === true;
    }

    /**
     * Check if an HTTP code indicates a retryable error
     */
    private function isHttpCodeRetryable(int $httpCode): bool {
        $retryableCodes = [
            429, // Too Many Requests
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504, // Gateway Timeout
        ];

        return in_array($httpCode, $retryableCodes);
    }

    /**
     * Parse error response to extract meaningful error information
     */
    private function parseErrorResponse(string $response, int $httpCode): array {
        $errorDetails = [
            'http_code' => $httpCode,
            'message' => 'Unknown error',
            'type' => 'unknown'
        ];

        if (!empty($response)) {
            $decoded = json_decode($response, true);
            if ($decoded && isset($decoded['error'])) {
                $error = $decoded['error'];
                $errorDetails['message'] = $error['message'] ?? 'Unknown error';
                $errorDetails['type'] = $error['type'] ?? 'unknown';
                $errorDetails['code'] = $error['code'] ?? $httpCode;

                if (isset($error['type'])) {
                    error_log("DeepSeekTranslationService: API Error Type: " . $error['type']);
                }
            }
        }

        return $errorDetails;
    }

    /**
     * Clean up title translation to ensure single, clean result
     */
    private function cleanTitleTranslation(string $translation): string {
        $original = $translation;

        file_put_contents('debug.log', "DeepSeekTranslationService: Original title response: " . substr($translation, 0, 200) . "\n", FILE_APPEND);

        // Remove common verbose introduction patterns
        $introPatterns = [
            '/^Here are.*?translations?.*?:/i',
            '/^Here are.*?options?.*?:/i',
            '/^The translation.*?:/i',
            '/^Translation.*?:/i',
        ];

        $cleaned = $translation;
        foreach ($introPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        // Remove formatting and take first substantial line
        $lines = explode("\n", $cleaned);
        $bestLine = '';

        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;
            if (preg_match('/^[\*\-\d\.\s]+$/', $line)) continue;
            
            $bestLine = $line;
            break;
        }

        $cleaned = $bestLine ?: $cleaned;

        // Final cleanup
        $cleanupPatterns = [
            '/\([^)]*\)/',              // Remove parenthetical explanations
            '/\[[^\]]*\]/',             // Remove bracketed explanations
            '/^["\'"]*/',               // Remove leading quotes
            '/["\'"]*$/',               // Remove trailing quotes
        ];

        foreach ($cleanupPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        $cleaned = preg_replace('/\s+/', ' ', $cleaned);
        $cleaned = trim($cleaned);

        if (empty($cleaned) || strlen($cleaned) < 3) {
            if (preg_match('/([A-Za-z][A-Za-z0-9\s\-~\[\]]+)/', $original, $matches)) {
                $cleaned = trim($matches[1]);
            } else {
                // Preserve Japanese/Chinese punctuation in fallback cleaning
                $cleaned = trim(substr(preg_replace('/[^\w\s\-~\[\]【】〖〗〔〕〈〉《》「」『』（）・※〜～]/u', '', $original), 0, 100));
            }
        }

        file_put_contents('debug.log', "DeepSeekTranslationService: Cleaned title result: " . $cleaned . "\n", FILE_APPEND);

        return $cleaned;
    }

    /**
     * Restore formatting if lost during translation
     */
    private function restoreFormattingIfLost(string $originalText, string $translatedText, array $context): string {
        // Check if formatting was preserved
        $originalLineBreaks = substr_count($originalText, "\n");
        $translatedLineBreaks = substr_count($translatedText, "\n");
        $originalParagraphs = count(preg_split('/\n\s*\n/', $originalText));
        $translatedParagraphs = count(preg_split('/\n\s*\n/', $translatedText));

        // If formatting is severely lost, attempt to restore it
        if ($originalLineBreaks > 2 && $translatedLineBreaks === 0) {
            file_put_contents('debug.log', "DeepSeekTranslationService: Severe formatting loss detected, attempting restoration\n", FILE_APPEND);
            return $this->attemptFormattingRestoration($originalText, $translatedText);
        }

        // If paragraph structure is lost, attempt to restore it
        if ($originalParagraphs > 2 && $translatedParagraphs === 1) {
            file_put_contents('debug.log', "DeepSeekTranslationService: Paragraph structure lost, attempting restoration\n", FILE_APPEND);
            return $this->attemptParagraphRestoration($originalText, $translatedText);
        }

        // Decode HTML entities that might have been introduced
        $translatedText = html_entity_decode($translatedText, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        return $translatedText;
    }

    /**
     * Attempt to restore formatting by analyzing original structure
     */
    private function attemptFormattingRestoration(string $originalText, string $translatedText): string {
        // Split original text into segments based on structure
        $originalLines = explode("\n", $originalText);
        $translatedWords = preg_split('/\s+/', trim($translatedText));

        if (empty($translatedWords)) {
            return $translatedText;
        }

        $restoredLines = [];
        $wordIndex = 0;
        $totalWords = count($translatedWords);

        foreach ($originalLines as $originalLine) {
            $originalLine = trim($originalLine);

            if (empty($originalLine)) {
                // Empty line in original - preserve as empty line
                $restoredLines[] = '';
                continue;
            }

            // Estimate how many words this line should have based on original
            $originalWords = preg_split('/\s+/', $originalLine);
            $estimatedWords = max(1, count($originalWords));

            // Take words from translated text
            $lineWords = [];
            for ($i = 0; $i < $estimatedWords && $wordIndex < $totalWords; $i++) {
                $lineWords[] = $translatedWords[$wordIndex++];
            }

            if (!empty($lineWords)) {
                $restoredLines[] = implode(' ', $lineWords);
            }
        }

        // Add any remaining words as a final line
        if ($wordIndex < $totalWords) {
            $remainingWords = array_slice($translatedWords, $wordIndex);
            $restoredLines[] = implode(' ', $remainingWords);
        }

        $restored = implode("\n", $restoredLines);
        file_put_contents('debug.log', "DeepSeekTranslationService: Formatting restoration attempted\n", FILE_APPEND);

        return $restored;
    }

    /**
     * Attempt to restore paragraph structure
     */
    private function attemptParagraphRestoration(string $originalText, string $translatedText): string {
        // Split original into paragraphs
        $originalParagraphs = preg_split('/\n\s*\n/', $originalText);
        $translatedSentences = preg_split('/(?<=[.!?])\s+/', trim($translatedText));

        if (empty($translatedSentences)) {
            return $translatedText;
        }

        $restoredParagraphs = [];
        $sentenceIndex = 0;
        $totalSentences = count($translatedSentences);

        foreach ($originalParagraphs as $originalParagraph) {
            $originalParagraph = trim($originalParagraph);

            if (empty($originalParagraph)) {
                continue;
            }

            // Estimate sentences per paragraph
            $originalSentenceCount = preg_match_all('/[.!?]/', $originalParagraph);
            $estimatedSentences = max(1, $originalSentenceCount);

            // Take sentences from translated text
            $paragraphSentences = [];
            for ($i = 0; $i < $estimatedSentences && $sentenceIndex < $totalSentences; $i++) {
                $paragraphSentences[] = trim($translatedSentences[$sentenceIndex++]);
            }

            if (!empty($paragraphSentences)) {
                $restoredParagraphs[] = implode(' ', $paragraphSentences);
            }
        }

        // Add any remaining sentences as a final paragraph
        if ($sentenceIndex < $totalSentences) {
            $remainingSentences = array_slice($translatedSentences, $sentenceIndex);
            $restoredParagraphs[] = implode(' ', $remainingSentences);
        }

        $restored = implode("\n\n", $restoredParagraphs);
        file_put_contents('debug.log', "DeepSeekTranslationService: Paragraph restoration attempted\n", FILE_APPEND);

        return $restored;
    }

    /**
     * Restore punctuation symbols that might have been converted by the API
     */
    private function restorePunctuationSymbols(string $originalText, string $translatedText): string {
        // Enhanced restoration with better symbol mapping and position tracking
        $restoredText = $translatedText;

        // Enhanced restoration with strict symbol preservation - no cross-conversions allowed
        // Each punctuation type should only be restored from English equivalents, never from other Japanese punctuation

        // General symbol restoration map - CRITICAL: No cross-conversions between Japanese punctuation types
        $punctuationMap = [
            '「' => ['"', "'", '"', '"'],
            '」' => ['"', "'", '"', '"'],
            '『' => ['"', "'", '"', '"'],  // Removed '「' to prevent cross-conversion
            '』' => ['"', "'", '"', '"'],  // Removed '」' to prevent cross-conversion
            '【' => ['['],
            '】' => [']'],
            '〖' => ['['],
            '〗' => [']'],
            '〔' => ['['],
            '〕' => [']'],
            '〈' => ['<'],
            '〉' => ['>'],
            '《' => ['"', '"', '"'],
            '》' => ['"', '"', '"'],
            '（' => ['('],
            '）' => [')'],
            '〜' => ['~'],
            '～' => ['~']
        ];

        // Find all original punctuation symbols and their positions
        $originalSymbols = [];
        foreach ($punctuationMap as $original => $possibleConverted) {
            $offset = 0;
            while (($pos = mb_strpos($originalText, $original, $offset)) !== false) {
                $originalSymbols[] = [
                    'symbol' => $original,
                    'position' => $pos,
                    'possible_converted' => $possibleConverted
                ];
                $offset = $pos + 1;
            }
        }

        // Sort by position to maintain order
        usort($originalSymbols, function($a, $b) {
            return $a['position'] - $b['position'];
        });

        // Restore symbols by looking for converted equivalents
        foreach ($originalSymbols as $symbolInfo) {
            $original = $symbolInfo['symbol'];
            $possibleConverted = $symbolInfo['possible_converted'];

            // Check if original symbol is already present
            if (mb_substr_count($restoredText, $original) >= mb_substr_count($originalText, $original)) {
                continue; // Already preserved correctly
            }

            // Try to find and replace converted symbols
            foreach ($possibleConverted as $converted) {
                $pos = mb_strpos($restoredText, $converted);
                if ($pos !== false) {
                    $restoredText = mb_substr($restoredText, 0, $pos) . $original . mb_substr($restoredText, $pos + mb_strlen($converted));
                    file_put_contents('debug.log', "DeepSeekTranslationService: Restored $converted → $original at position $pos\n", FILE_APPEND);
                    break; // Only replace one occurrence per original symbol
                }
            }
        }

        return $restoredText;
    }
}
