<?php
/**
 * Manual Chapter Entry Page
 * Allows users to manually add chapters to novels
 */

require_once 'config/config.php';
require_once 'includes/header.php';

// Get novel ID from URL parameter
$novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;

if ($novelId <= 0) {
    header('Location: novels.php');
    exit;
}

// Get novel information
try {
    $novelManager = new NovelManager();
    $novelDetails = $novelManager->getNovelDetails($novelId);
    $novel = $novelDetails['novel'];
    
    // Verify this is a manual novel
    if ($novel['platform'] !== 'manual') {
        header('Location: novel-details.php?id=' . $novelId);
        exit;
    }
} catch (Exception $e) {
    header('Location: novels.php');
    exit;
}

renderHeader('Add Chapter - ' . htmlspecialchars($novel['original_title']));
?>

<?php 
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php'; 
renderNavigation('manual-chapter');
?>

<div class="container mt-4">
    <!-- Page Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h2>
                <i class="fas fa-plus me-2"></i>
                Add Chapter
            </h2>
            <p class="text-muted mb-0">
                <i class="fas fa-book me-1"></i>
                <?= htmlspecialchars($novel['original_title']) ?>
                <?php if ($novel['translated_title']): ?>
                    <br><small class="text-success"><?= htmlspecialchars($novel['translated_title']) ?></small>
                <?php endif; ?>
            </p>
        </div>
        <div>
            <a href="novel-details.php?id=<?= $novelId ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>
                Back to Novel
            </a>
            <a href="novels.php" class="btn btn-outline-primary">
                <i class="fas fa-list me-1"></i>
                My Novels
            </a>
        </div>
    </div>

    <!-- Chapter Entry Form -->
    <div class="card shadow">
        <div class="card-header bg-primary text-white">
            <h4 class="mb-0">
                <i class="fas fa-file-alt me-2"></i>
                Add New Chapter
            </h4>
        </div>
        <div class="card-body">
            <form id="manual-chapter-form">
                <input type="hidden" id="novel-id" value="<?= $novelId ?>">
                
                <div class="row">
                    <div class="col-md-4">
                        <div class="mb-3">
                            <label for="chapter-number" class="form-label">
                                <i class="fas fa-hashtag me-1"></i>
                                Chapter Number <span class="text-danger">*</span>
                            </label>
                            <input type="number" class="form-control" id="chapter-number" 
                                   min="1" value="<?= $novel['total_chapters'] + 1 ?>" required>
                            <div class="form-text">Current total: <?= $novel['total_chapters'] ?> chapters</div>
                        </div>
                    </div>
                    <div class="col-md-8">
                        <div class="mb-3">
                            <label for="original-title" class="form-label">
                                <i class="fas fa-heading me-1"></i>
                                Original Chapter Title <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="original-title" 
                                   placeholder="Enter the original chapter title" required>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="translated-title" class="form-label">
                        <i class="fas fa-language me-1"></i>
                        Translated Chapter Title
                    </label>
                    <input type="text" class="form-control" id="translated-title" 
                           placeholder="Enter translated chapter title (optional)">
                </div>

                <div class="mb-3">
                    <label for="original-content" class="form-label">
                        <i class="fas fa-align-left me-1"></i>
                        Original Chapter Content <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control" id="original-content" rows="20" 
                              placeholder="Paste or type the original chapter content here..." required></textarea>
                    <div class="form-text">
                        <span id="content-stats">Character count: 0</span>
                        <span class="ms-3" id="chunking-info"></span>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="translated-content" class="form-label">
                        <i class="fas fa-language me-1"></i>
                        Translated Chapter Content
                    </label>
                    <textarea class="form-control" id="translated-content" rows="15" 
                              placeholder="Enter translated content (optional - can be translated later)"></textarea>
                    <div class="form-text">Leave empty to translate later using the AI translation system</div>
                </div>

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-2"></i>
                        Cancel
                    </button>
                    <div>
                        <button type="button" class="btn btn-info me-2" id="save-and-add-another">
                            <i class="fas fa-plus me-2"></i>
                            Save & Add Another
                        </button>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-save me-2"></i>
                            Save Chapter
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="message-container" class="mt-4" style="display: none;">
        <!-- Messages will be inserted here -->
    </div>

    <!-- Existing Chapters -->
    <?php if ($novel['total_chapters'] > 0): ?>
    <div class="card mt-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>
                Existing Chapters (<?= $novel['total_chapters'] ?>)
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <?php 
                $chapters = $novelDetails['chapters'];
                foreach (array_slice($chapters, -10) as $chapter): // Show last 10 chapters
                ?>
                <div class="col-md-6 mb-2">
                    <div class="d-flex justify-content-between align-items-center p-2 border rounded">
                        <span>
                            <strong>Ch. <?= $chapter['chapter_number'] ?></strong>
                            <?= htmlspecialchars($chapter['original_title']) ?>
                        </span>
                        <span class="badge bg-<?= $chapter['translation_status'] === 'completed' ? 'success' : 'secondary' ?>">
                            <?= ucfirst($chapter['translation_status']) ?>
                        </span>
                    </div>
                </div>
                <?php endforeach; ?>
            </div>
            <?php if (count($chapters) > 10): ?>
            <div class="text-center mt-3">
                <a href="novel-details.php?id=<?= $novelId ?>" class="btn btn-outline-primary btn-sm">
                    View All Chapters
                </a>
            </div>
            <?php endif; ?>
        </div>
    </div>
    <?php endif; ?>

    <!-- Help Section -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-lightbulb me-2"></i>
                Chapter Entry Tips
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>Content Guidelines</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Preserve original formatting and line breaks</li>
                        <li><i class="fas fa-check text-success me-2"></i>Keep sound effects in romanized form</li>
                        <li><i class="fas fa-check text-success me-2"></i>Maintain honorifics (-kun, -chan, -sama, etc.)</li>
                        <li><i class="fas fa-check text-success me-2"></i>Large chapters will be automatically chunked</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Translation Features</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-robot text-primary me-2"></i>AI translation with DeepSeek API</li>
                        <li><i class="fas fa-users text-primary me-2"></i>Character name consistency</li>
                        <li><i class="fas fa-edit text-primary me-2"></i>Editable translations</li>
                        <li><i class="fas fa-download text-primary me-2"></i>Word export functionality</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>

<?php renderFooter(['assets/js/manual-chapter.js']); ?>
