<?php
/**
 * API Endpoint: Word Export
 * GET /api/export-word.php?novel_id=<id>&chapter=<number> - Export chapter to Word document
 */

require_once '../config/config.php';

// Only allow GET requests
if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    http_response_code(405);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['error' => 'Method not allowed']);
    exit;
}

// Get parameters
$novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;
$chapterNumber = isset($_GET['chapter']) ? (int)$_GET['chapter'] : 0;

// Validate parameters
if (!$novelId || !$chapterNumber) {
    http_response_code(400);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode(['error' => 'Missing required parameters: novel_id and chapter']);
    exit;
}

try {
    // Log the request for debugging
    logError('Word Export Request', [
        'novel_id' => $novelId,
        'chapter' => $chapterNumber,
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown'
    ]);

    // Initialize the Word export service
    $exportService = new WordExportService();

    // Export the chapter
    $result = $exportService->exportChapter($novelId, $chapterNumber);

    if (!$result['success']) {
        logError('Word Export Failed', [
            'novel_id' => $novelId,
            'chapter' => $chapterNumber,
            'error' => $result['error']
        ]);

        http_response_code(400);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['error' => $result['error']]);
        exit;
    }

    $filepath = $result['filepath'];
    $filename = basename($filepath);

    // Check if file exists and is valid
    if (!file_exists($filepath)) {
        logError('Word Export File Not Found', [
            'novel_id' => $novelId,
            'chapter' => $chapterNumber,
            'expected_path' => $filepath
        ]);

        http_response_code(500);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['error' => 'Failed to generate Word document']);
        exit;
    }

    $fileSize = filesize($filepath);
    if ($fileSize === 0) {
        logError('Word Export File Empty', [
            'novel_id' => $novelId,
            'chapter' => $chapterNumber,
            'filepath' => $filepath
        ]);

        http_response_code(500);
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['error' => 'Generated Word document is empty']);
        exit;
    }

    // Log successful generation
    logError('Word Export Success', [
        'novel_id' => $novelId,
        'chapter' => $chapterNumber,
        'file_size' => $fileSize,
        'filename' => $filename
    ]);

    // Set headers for file download
    header('Content-Type: application/vnd.openxmlformats-officedocument.wordprocessingml.document');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Content-Length: ' . $fileSize);
    header('Cache-Control: must-revalidate');
    header('Pragma: public');

    // Clear any output buffers
    if (ob_get_level()) {
        ob_end_clean();
    }

    // Output the file
    readfile($filepath);

    // Clean up the temporary file after download
    unlink($filepath);

    // Clean up old temporary files
    $exportService->cleanupTempFiles();
    
} catch (Exception $e) {
    logError('Word Export Error: ' . $e->getMessage(), [
        'novel_id' => $novelId,
        'chapter' => $chapterNumber,
        'trace' => $e->getTraceAsString()
    ]);
    
    http_response_code(500);
    header('Content-Type: application/json; charset=utf-8');
    echo json_encode([
        'error' => 'Failed to export chapter: ' . $e->getMessage()
    ]);
}
?>
