<?php
// Test the alternative domain (.com) for chapter content

$chapterUrls = [
    'cx' => 'https://69shuba.cx/txt/44425/29853970',
    'com' => 'https://69shuba.com/txt/44425/29853970'
];

foreach ($chapterUrls as $domain => $chapterUrl) {
    echo "=== Testing $domain domain ===\n";
    echo "URL: $chapterUrl\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $chapterUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'Referer: https://69shuba.' . $domain . '/book/44425.htm',
        'Cache-Control: no-cache',
        'Pragma: no-cache'
    ]);
    
    $rawHtml = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $finalUrl = curl_getinfo($ch, CURLINFO_EFFECTIVE_URL);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "Final URL: $finalUrl\n";
    echo "HTML Length: " . strlen($rawHtml) . "\n";
    
    if ($httpCode == 200 && !empty($rawHtml)) {
        // Convert encoding
        $html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
        
        $dom = new DOMDocument();
        libxml_use_internal_errors(true);
        $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
        libxml_clear_errors();
        
        $allText = $dom->textContent;
        
        echo "Text length: " . strlen($allText) . "\n";
        echo "Chinese chars: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $allText) . "\n";
        
        // Check for expected content
        $hasNovelTitle = strpos($allText, '从零开始缔造游戏帝国') !== false;
        $hasChapterTitle = strpos($allText, '第1章') !== false && strpos($allText, '怒气') !== false;
        $isHomepage = strpos($allText, '小说点击排行榜') !== false || strpos($allText, '精品小说推荐') !== false;
        
        echo "Has novel title: " . ($hasNovelTitle ? 'Yes' : 'No') . "\n";
        echo "Has chapter title: " . ($hasChapterTitle ? 'Yes' : 'No') . "\n";
        echo "Is homepage: " . ($isHomepage ? 'Yes' : 'No') . "\n";
        
        if ($hasNovelTitle && $hasChapterTitle && !$isHomepage) {
            echo "✓ Correct chapter page found on $domain!\n";
            
            // Try to extract content
            if (preg_match('/第1章.*?怒气.*?(.{500,}?)(?:上一章|下一章|目录|Copyright|69书吧|$)/s', $allText, $matches)) {
                $content = trim($matches[1]);
                echo "Story content length: " . strlen($content) . "\n";
                echo "Chinese chars in story: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content) . "\n";
                
                if (preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content) > 50) {
                    echo "First 1000 characters:\n";
                    echo "---\n";
                    echo mb_substr($content, 0, 1000, 'UTF-8') . "\n";
                    echo "---\n";
                    break; // Success, no need to test other domains
                }
            }
        } else {
            echo "✗ Wrong page on $domain\n";
            if ($isHomepage) {
                echo "  Redirected to homepage - anti-bot protection active\n";
            }
        }
    } else {
        echo "Failed to fetch from $domain\n";
    }
    
    echo "\n";
}

// Test if we can access the novel info page to verify the novel exists
echo "=== Testing Novel Info Page ===\n";
$novelUrl = 'https://69shuba.cx/book/44425.htm';
echo "URL: $novelUrl\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $novelUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_ENCODING, '');

$rawHtml = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode == 200 && !empty($rawHtml)) {
    $html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
    $hasNovelTitle = strpos($html, '从零开始缔造游戏帝国') !== false;
    echo "Novel info page accessible: " . ($hasNovelTitle ? 'Yes' : 'No') . "\n";
    
    if ($hasNovelTitle) {
        echo "✓ Novel exists and info page is accessible\n";
        echo "Issue: Chapter pages are being blocked by anti-bot protection\n";
    }
} else {
    echo "Novel info page not accessible\n";
}
?>
