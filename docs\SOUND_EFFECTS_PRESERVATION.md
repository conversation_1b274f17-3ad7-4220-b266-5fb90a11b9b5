# Sound Effects Preservation in Novel Translation

## Overview

The novel translation application now includes automatic detection and preservation of sound effects (onomatopoeia) in their original romanized form. This feature ensures that sound effects maintain their dramatic impact and atmosphere rather than being translated to potentially less effective equivalents.

## Why Preserve Sound Effects?

Sound effects in novels, especially Japanese light novels and web novels, are carefully chosen by authors to:
- Create specific atmospheric effects
- Convey the intensity and nature of actions
- Maintain the original cultural context
- Preserve the dramatic impact intended by the author

Translating sound effects often results in:
- Loss of dramatic impact
- Cultural disconnect
- Awkward phrasing in the target language
- Mismatched atmosphere

## How It Works

### 1. Detection Phase
The system uses multiple methods to detect sound effects:

**Pattern-Based Detection:**
- Katakana repetitions (common for sound effects)
- Specific sound effect patterns (ドン, バン, ガン, etc.)
- Elongated sounds with repetition marks (ー, ッ)
- Sound effects in parentheses or special formatting

**AI-Assisted Detection:**
- For complex cases that patterns might miss
- Context-aware identification
- Fallback when pattern detection is insufficient

### 2. Romanization Phase
Detected sound effects are converted to romanized form:
- Direct mapping for common sound effects
- AI-powered romanization for complex cases
- Consistent romanization standards (Hepburn for Japanese)

### 3. Translation Phase
During translation:
- Sound effects are temporarily replaced with preservation markers
- Translation instructions include sound effect preservation guidelines
- AI translator is instructed to maintain markers unchanged

### 4. Restoration Phase
After translation:
- Preservation markers are replaced with romanized sound effects
- Original formatting and placement are maintained

## Examples

### Input Text:
```
彼はドアをドンと叩いた。「誰かいるのか？」と叫んだ。
バン！ガシャン！という音が響いた。
```

### Detection Results:
- ドン → don
- バン → ban  
- ガシャン → gashan

### Final Translation:
```
He knocked on the door with a *don*. "Is anyone there?" he shouted.
*Ban! Gashan!* The sounds echoed.
```

## Supported Sound Effect Types

### Impact Sounds
- ドン (don) - thud, bang
- バン (ban) - bang, slam
- ガン (gan) - clang, bang
- ドカ (doka) - thump, whack

### Movement Sounds  
- ザッ (za) - swish, whoosh
- シュッ (shu) - swish, swift movement
- ヒュー (hyu) - whoosh, whistle

### Environmental Sounds
- ザーザー (zaazaa) - heavy rain
- ゴロゴロ (gorogoro) - rumbling thunder
- パラパラ (parapara) - light rain, pattering

### Emotional Sounds
- ドキドキ (dokidoki) - heartbeat, excitement
- ガーン (gaan) - shock, dismay
- ワクワク (wakuwaku) - excitement, anticipation

## Configuration

The sound effects preservation is automatically enabled for chapter content translation. It can be controlled through the translation context:

```php
$translationResult = $translationService->translateText(
    $text, 
    'en', 
    'auto', 
    ['type' => 'chapter'] // Enables sound effects preservation
);
```

## Technical Implementation

### Files Modified:
- `classes/SoundEffectsService.php` - New service for sound effects handling
- `classes/TranslationService.php` - Integration with translation pipeline

### Key Methods:
- `preprocessTextForTranslation()` - Detects and marks sound effects
- `postprocessTranslatedText()` - Restores romanized sound effects
- `detectSoundEffects()` - Multi-method detection system
- `romanizeSoundEffect()` - Converts to romanized form

### Preservation Markers:
Sound effects are temporarily replaced with markers like:
```
【SOUND_EFFECT:SE_0】
【SOUND_EFFECT:SE_1】
```

These markers are preserved during translation and replaced with romanized sound effects in the final output.

## Testing

Run the test script to verify functionality:
```
http://localhost/wc/test_sound_effects.php
```

The test demonstrates:
- Detection accuracy for various sound effect types
- Romanization quality
- Integration with the translation system
- Preservation through the complete translation pipeline

## Benefits

1. **Maintains Atmosphere**: Original dramatic impact is preserved
2. **Cultural Authenticity**: Keeps the Japanese cultural context
3. **Reader Experience**: Familiar sound effects for manga/anime fans
4. **Author Intent**: Preserves the specific sounds chosen by the author
5. **Consistency**: Standardized romanization across all translations

## Future Enhancements

Potential improvements for future versions:
- Support for other languages' onomatopoeia
- User-configurable romanization preferences
- Sound effect glossary for readers
- Custom sound effect dictionaries
- Visual formatting options for sound effects
