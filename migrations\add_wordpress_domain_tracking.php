<?php
/**
 * Migration: Add WordPress Domain Tracking
 * Adds domain tracking to wordpress_posts table for multi-domain support
 */

require_once '../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Starting WordPress domain tracking migration...\n";
    
    // Check if wordpress_domain column already exists
    $columns = $db->fetchAll("SHOW COLUMNS FROM wordpress_posts LIKE 'wordpress_domain'");
    
    if (empty($columns)) {
        echo "Adding wordpress_domain column...\n";
        
        // Add wordpress_domain column
        $db->query("ALTER TABLE wordpress_posts ADD COLUMN wordpress_domain VARCHAR(255) NOT NULL DEFAULT ''");
        
        // Get current WordPress site URL from preferences
        $currentSiteUrl = $db->fetchOne(
            "SELECT preference_value FROM user_preferences WHERE preference_key = 'wordpress_site_url'"
        );
        
        if ($currentSiteUrl && !empty($currentSiteUrl['preference_value'])) {
            $domain = parse_url($currentSiteUrl['preference_value'], PHP_URL_HOST);
            if ($domain) {
                echo "Updating existing posts with current domain: $domain\n";
                $db->query(
                    "UPDATE wordpress_posts SET wordpress_domain = ? WHERE wordpress_domain = ''",
                    [$domain]
                );
            }
        }
        
        echo "✓ wordpress_domain column added successfully\n";
    } else {
        echo "✓ wordpress_domain column already exists\n";
    }
    
    // Drop old unique constraints
    echo "Updating unique constraints...\n";
    
    try {
        $db->query("ALTER TABLE wordpress_posts DROP INDEX unique_novel_post");
        echo "✓ Dropped old unique_novel_post constraint\n";
    } catch (Exception $e) {
        echo "Note: unique_novel_post constraint may not exist\n";
    }
    
    try {
        $db->query("ALTER TABLE wordpress_posts DROP INDEX unique_chapter_post");
        echo "✓ Dropped old unique_chapter_post constraint\n";
    } catch (Exception $e) {
        echo "Note: unique_chapter_post constraint may not exist\n";
    }
    
    // Add new unique constraints with domain
    try {
        $db->query("ALTER TABLE wordpress_posts ADD UNIQUE KEY unique_novel_post_domain (novel_id, post_type, wordpress_domain)");
        echo "✓ Added unique_novel_post_domain constraint\n";
    } catch (Exception $e) {
        echo "Note: unique_novel_post_domain constraint may already exist\n";
    }
    
    try {
        $db->query("ALTER TABLE wordpress_posts ADD UNIQUE KEY unique_chapter_post_domain (chapter_id, wordpress_domain)");
        echo "✓ Added unique_chapter_post_domain constraint\n";
    } catch (Exception $e) {
        echo "Note: unique_chapter_post_domain constraint may already exist\n";
    }
    
    // Add domain index
    try {
        $db->query("ALTER TABLE wordpress_posts ADD INDEX idx_wordpress_domain (wordpress_domain)");
        echo "✓ Added wordpress_domain index\n";
    } catch (Exception $e) {
        echo "Note: wordpress_domain index may already exist\n";
    }
    
    echo "\n✅ WordPress domain tracking migration completed successfully!\n";
    echo "\nWhat this enables:\n";
    echo "- Track which WordPress domain each post was sent to\n";
    echo "- Allow same novel/chapter to be posted to multiple domains\n";
    echo "- Prevent duplicate posts within the same domain\n";
    echo "- Better organization for multi-domain setups\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
