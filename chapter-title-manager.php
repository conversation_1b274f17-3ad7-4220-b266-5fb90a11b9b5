<?php
require_once 'config/config.php';

// Get novel ID from URL parameter
$novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : null;

if (!$novelId) {
    header('Location: index.php');
    exit;
}

try {
    $db = Database::getInstance();
    $novelManager = new NovelManager();
    
    // Get novel information
    $novel = $db->fetchOne(
        "SELECT * FROM novels WHERE id = ?",
        [$novelId]
    );
    
    if (!$novel) {
        header('Location: index.php');
        exit;
    }
    
    // Get chapter title statistics
    $stats = $db->fetchOne(
        "SELECT 
            COUNT(*) as total_chapters,
            COUNT(CASE WHEN original_title IS NOT NULL AND original_title != '' THEN 1 END) as chapters_with_original_titles,
            COUNT(CASE WHEN translated_title IS NOT NULL AND translated_title != '' THEN 1 END) as chapters_with_translated_titles,
            COUNT(CASE WHEN original_title IS NOT NULL AND original_title != '' AND (translated_title IS NULL OR translated_title = '') THEN 1 END) as chapters_needing_translation
         FROM chapters 
         WHERE novel_id = ?",
        [$novelId]
    );
    
    // Get chapters with pagination
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $perPage = 20;
    $offset = ($page - 1) * $perPage;
    
    $chapters = $db->fetchAll(
        "SELECT chapter_number, original_title, translated_title, translation_status
         FROM chapters 
         WHERE novel_id = ? 
         ORDER BY chapter_number 
         LIMIT ? OFFSET ?",
        [$novelId, $perPage, $offset]
    );
    
    $totalPages = ceil($stats['total_chapters'] / $perPage);
    
} catch (Exception $e) {
    $error = $e->getMessage();
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapter Title Manager - <?= htmlspecialchars($novel['original_title'] ?? 'Novel') ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .title-status {
            font-size: 0.8em;
            padding: 2px 6px;
            border-radius: 3px;
        }
        .status-translated {
            background-color: #d4edda;
            color: #155724;
        }
        .status-missing {
            background-color: #f8d7da;
            color: #721c24;
        }
        .chapter-row:hover {
            background-color: #f8f9fa;
        }
        .progress-container {
            margin: 20px 0;
        }
        .action-buttons {
            margin: 20px 0;
        }
        .translation-progress {
            display: none;
            margin-top: 10px;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.9em;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <!-- Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h2 class="mb-1">
                            <i class="fas fa-heading text-primary me-2"></i>
                            Chapter Title Manager
                        </h2>
                        <nav aria-label="breadcrumb">
                            <ol class="breadcrumb mb-0">
                                <li class="breadcrumb-item">
                                    <a href="index.php">
                                        <i class="fas fa-home"></i> Dashboard
                                    </a>
                                </li>
                                <li class="breadcrumb-item">
                                    <a href="novel-details.php?id=<?= $novelId ?>">
                                        <i class="fas fa-book"></i> Novel Details
                                    </a>
                                </li>
                                <li class="breadcrumb-item active" aria-current="page">
                                    <i class="fas fa-heading"></i> Title Manager
                                </li>
                            </ol>
                        </nav>
                    </div>
                    <div>
                        <a href="novel-details.php?id=<?= $novelId ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-1"></i>
                            Back to Novel
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Novel Info -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title">
                            <?= htmlspecialchars($novel['original_title']) ?>
                            <?php if ($novel['translated_title']): ?>
                                <small class="text-muted">/ <?= htmlspecialchars($novel['translated_title']) ?></small>
                            <?php endif; ?>
                        </h5>
                        <div class="row">
                            <div class="col-md-3">
                                <strong>Total Chapters:</strong> <?= $stats['total_chapters'] ?>
                            </div>
                            <div class="col-md-3">
                                <strong>With Original Titles:</strong> <?= $stats['chapters_with_original_titles'] ?>
                            </div>
                            <div class="col-md-3">
                                <strong>With Translated Titles:</strong> <?= $stats['chapters_with_translated_titles'] ?>
                            </div>
                            <div class="col-md-3">
                                <strong>Need Translation:</strong> 
                                <span class="badge bg-warning"><?= $stats['chapters_needing_translation'] ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Bulk Actions</h6>
                        <div class="btn-group me-3" role="group">
                            <button type="button" class="btn btn-primary" onclick="translateMissingTitles()">
                                <i class="fas fa-language me-1"></i>
                                Translate Missing Titles
                            </button>
                            <button type="button" class="btn btn-warning" onclick="retranslateAllTitles()">
                                <i class="fas fa-redo me-1"></i>
                                Re-translate All Titles
                            </button>
                        </div>
                        <div class="btn-group" role="group">
                            <button type="button" class="btn btn-info" onclick="refreshPage()">
                                <i class="fas fa-sync me-1"></i>
                                Refresh
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Progress Section -->
        <div id="progress-section" class="row mb-4" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h6 class="card-title">Translation Progress</h6>
                        <div class="progress mb-3">
                            <div id="progress-bar" class="progress-bar" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div id="progress-text" class="text-muted">Ready to start...</div>
                        <div id="log-output" class="log-output mt-3" style="display: none;"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Chapters Table -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">Chapter Titles (Page <?= $page ?> of <?= $totalPages ?>)</h6>
                    </div>
                    <div class="card-body p-0">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead class="table-light">
                                    <tr>
                                        <th style="width: 80px;">Chapter</th>
                                        <th style="width: 40%;">Original Title</th>
                                        <th style="width: 40%;">Translated Title</th>
                                        <th style="width: 100px;">Status</th>
                                        <th style="width: 180px;">Actions</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($chapters as $chapter): ?>
                                        <tr class="chapter-row" data-chapter="<?= $chapter['chapter_number'] ?>">
                                            <td class="fw-bold"><?= $chapter['chapter_number'] ?></td>
                                            <td>
                                                <?php if ($chapter['original_title']): ?>
                                                    <?= htmlspecialchars($chapter['original_title']) ?>
                                                <?php else: ?>
                                                    <em class="text-muted">No original title</em>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div id="translated-title-<?= $chapter['chapter_number'] ?>">
                                                    <?php if ($chapter['translated_title']): ?>
                                                        <?= htmlspecialchars($chapter['translated_title']) ?>
                                                    <?php else: ?>
                                                        <em class="text-muted">Not translated</em>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                            <td>
                                                <?php if ($chapter['translated_title']): ?>
                                                    <span class="title-status status-translated">Translated</span>
                                                <?php else: ?>
                                                    <span class="title-status status-missing">Missing</span>
                                                <?php endif; ?>
                                            </td>
                                            <td>
                                                <div class="btn-group-vertical btn-group-sm" role="group">
                                                    <?php if ($chapter['original_title']): ?>
                                                        <button type="button" class="btn btn-outline-primary mb-1"
                                                                onclick="translateSingleTitle(<?= $chapter['chapter_number'] ?>)">
                                                            <i class="fas fa-language"></i>
                                                            <?= $chapter['translated_title'] ? 'Re-translate' : 'Translate' ?>
                                                        </button>
                                                    <?php endif; ?>

                                                    <?php if ($chapter['original_content']): ?>
                                                        <a href="chapter-view.php?novel_id=<?= $novelId ?>&chapter=<?= $chapter['chapter_number'] ?>"
                                                           class="btn btn-outline-info mb-1"
                                                           title="Edit original chapter content">
                                                            <i class="fas fa-edit"></i>
                                                            Edit Text
                                                        </a>
                                                    <?php endif; ?>

                                                    <?php if (!$chapter['original_title'] && !$chapter['original_content']): ?>
                                                        <span class="text-muted">N/A</span>
                                                    <?php endif; ?>
                                                </div>
                                            </td>
                                        </tr>
                                    <?php endforeach; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Pagination -->
        <?php if ($totalPages > 1): ?>
            <div class="row mt-4">
                <div class="col-12">
                    <nav aria-label="Chapter pagination">
                        <ul class="pagination justify-content-center">
                            <?php if ($page > 1): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?novel_id=<?= $novelId ?>&page=<?= $page - 1 ?>">Previous</a>
                                </li>
                            <?php endif; ?>
                            
                            <?php for ($i = max(1, $page - 2); $i <= min($totalPages, $page + 2); $i++): ?>
                                <li class="page-item <?= $i === $page ? 'active' : '' ?>">
                                    <a class="page-link" href="?novel_id=<?= $novelId ?>&page=<?= $i ?>"><?= $i ?></a>
                                </li>
                            <?php endfor; ?>
                            
                            <?php if ($page < $totalPages): ?>
                                <li class="page-item">
                                    <a class="page-link" href="?novel_id=<?= $novelId ?>&page=<?= $page + 1 ?>">Next</a>
                                </li>
                            <?php endif; ?>
                        </ul>
                    </nav>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        const novelId = <?= $novelId ?>;
        let isTranslating = false;

        function showProgress() {
            document.getElementById('progress-section').style.display = 'block';
            document.getElementById('log-output').style.display = 'block';
        }

        function hideProgress() {
            document.getElementById('progress-section').style.display = 'none';
        }

        function updateProgress(percent, text) {
            const progressBar = document.getElementById('progress-bar');
            const progressText = document.getElementById('progress-text');
            
            progressBar.style.width = percent + '%';
            progressBar.setAttribute('aria-valuenow', percent);
            progressText.textContent = text;
        }

        function addLog(message) {
            const logOutput = document.getElementById('log-output');
            logOutput.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            logOutput.scrollTop = logOutput.scrollHeight;
        }

        function clearLog() {
            document.getElementById('log-output').textContent = '';
        }

        async function translateMissingTitles() {
            if (isTranslating) return;
            
            if (!confirm('Translate all missing chapter titles? This may take a while.')) {
                return;
            }
            
            isTranslating = true;
            showProgress();
            clearLog();
            updateProgress(0, 'Starting translation of missing titles...');
            addLog('Starting translation of missing titles...');
            
            try {
                const response = await fetch('/wc/api/chapter-titles.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        novel_id: novelId,
                        translate_all: true,
                        force_retranslate: false
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateProgress(100, `Translation completed! ${result.data.successful_translations} titles translated.`);
                    addLog(`Success: ${result.data.message}`);
                    
                    // Refresh the page after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    updateProgress(0, 'Translation failed');
                    addLog(`Error: ${result.error}`);
                }
            } catch (error) {
                updateProgress(0, 'Translation failed');
                addLog(`Error: ${error.message}`);
            } finally {
                isTranslating = false;
            }
        }

        async function retranslateAllTitles() {
            if (isTranslating) return;
            
            if (!confirm('Re-translate ALL chapter titles? This will overwrite existing translations and may take a while.')) {
                return;
            }
            
            isTranslating = true;
            showProgress();
            clearLog();
            updateProgress(0, 'Starting re-translation of all titles...');
            addLog('Starting re-translation of all titles...');
            
            try {
                const response = await fetch('/wc/api/chapter-titles.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        novel_id: novelId,
                        translate_all: true,
                        force_retranslate: true
                    })
                });
                
                const result = await response.json();
                
                if (result.success) {
                    updateProgress(100, `Re-translation completed! ${result.data.successful_translations} titles translated.`);
                    addLog(`Success: ${result.data.message}`);
                    
                    // Refresh the page after a short delay
                    setTimeout(() => {
                        window.location.reload();
                    }, 2000);
                } else {
                    updateProgress(0, 'Re-translation failed');
                    addLog(`Error: ${result.error}`);
                }
            } catch (error) {
                updateProgress(0, 'Re-translation failed');
                addLog(`Error: ${error.message}`);
            } finally {
                isTranslating = false;
            }
        }

        async function translateSingleTitle(chapterNumber) {
            if (isTranslating) return;
            
            isTranslating = true;
            const button = event.target.closest('button');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Translating...';
            button.disabled = true;
            
            try {
                const response = await fetch('/wc/api/chapter-titles.php', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        novel_id: novelId,
                        chapter_number: chapterNumber,
                        force_retranslate: true
                    })
                });
                
                const result = await response.json();
                
                if (result.success && result.data.results && result.data.results.length > 0) {
                    const chapterResult = result.data.results[0];
                    if (chapterResult.success) {
                        // Update the translated title in the table
                        const titleCell = document.getElementById(`translated-title-${chapterNumber}`);
                        titleCell.innerHTML = chapterResult.translated_title;
                        
                        // Update button text
                        button.innerHTML = '<i class="fas fa-language"></i> Re-translate';
                        
                        // Show success message
                        alert('Title translated successfully!');
                    } else {
                        alert(`Translation failed: ${chapterResult.error}`);
                    }
                } else {
                    alert(`Translation failed: ${result.error || 'Unknown error'}`);
                }
            } catch (error) {
                alert(`Translation failed: ${error.message}`);
            } finally {
                button.innerHTML = originalText;
                button.disabled = false;
                isTranslating = false;
            }
        }

        function refreshPage() {
            window.location.reload();
        }
    </script>
</body>
</html>
