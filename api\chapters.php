<?php
/**
 * API Endpoint: Chapter Management
 * GET /api/chapters.php?novel_id=<id> - Get novel details with chapters
 * POST /api/chapters.php - Save chapter content
 * PUT /api/chapters.php - Translate chapter
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

/**
 * Clean data for JSON encoding to fix UTF-8 issues
 */
function cleanDataForJson($data) {
    if (is_array($data)) {
        return array_map('cleanDataForJson', $data);
    } elseif (is_string($data)) {
        // Remove null bytes and fix encoding
        $cleaned = str_replace("\0", '', $data);
        if (!mb_check_encoding($cleaned, 'UTF-8')) {
            $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'auto');
        }
        // Remove any remaining problematic characters
        $cleaned = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $cleaned);
        return $cleaned;
    }
    return $data;
}

$method = $_SERVER['REQUEST_METHOD'];

try {
    $novelManager = new NovelManager();
} catch (Exception $e) {
    logError('Failed to initialize NovelManager: ' . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'Database connection failed'
    ], 500);
}

try {
    switch ($method) {
        case 'GET':
            handleGetChapters($novelManager);
            break;

        case 'POST':
            handleSaveChapter($novelManager);
            break;

        case 'PUT':
            handleTranslateChapter($novelManager);
            break;

        case 'PATCH':
            // Check the action type for different update operations
            $input = json_decode(file_get_contents('php://input'), true);
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'update_title':
                        handleUpdateChapterTitle($novelManager);
                        break;
                    case 'update_original_content':
                        handleUpdateOriginalContent($novelManager);
                        break;
                    default:
                        handleUpdateTranslation($novelManager);
                        break;
                }
            } else {
                handleUpdateTranslation($novelManager);
            }
            break;

        case 'DELETE':
            $input = json_decode(file_get_contents('php://input'), true);
            if (isset($input['action'])) {
                switch ($input['action']) {
                    case 'delete_chapter':
                        handleDeleteChapter($novelManager);
                        break;
                    case 'recrawl_chapter':
                        handleRecrawlChapter($novelManager);
                        break;
                    default:
                        handleDeleteTranslations($novelManager);
                        break;
                }
            } else {
                handleDeleteTranslations($novelManager);
            }
            break;

        case 'OPTIONS':
            // Handle preflight requests for CORS
            header('Access-Control-Allow-Origin: *');
            header('Access-Control-Allow-Methods: GET, POST, PUT, PATCH, DELETE, OPTIONS');
            header('Access-Control-Allow-Headers: Content-Type');
            http_response_code(200);
            exit;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    logError('Chapters API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    // Provide more specific error messages based on the exception
    $errorMessage = 'An error occurred while processing your request';
    $statusCode = 500;

    // Check for specific error types
    if (strpos($e->getMessage(), 'HTTP error: 429') !== false || strpos($e->getMessage(), 'quota') !== false) {
        $errorMessage = 'Translation service quota exceeded. Please wait and try again later.';
        $statusCode = 429;
    } elseif (strpos($e->getMessage(), 'HTTP error: 503') !== false || strpos($e->getMessage(), 'overloaded') !== false) {
        $errorMessage = 'Translation service is temporarily overloaded. Please try again in a few minutes.';
        $statusCode = 503;
    } elseif (strpos($e->getMessage(), 'timeout') !== false || strpos($e->getMessage(), 'timed out') !== false) {
        $errorMessage = 'Translation request timed out. The chapter may be too large. Please try again.';
        $statusCode = 408;
    } elseif (strpos($e->getMessage(), 'Translation failed') !== false) {
        $errorMessage = 'Translation failed due to API issues. Please try again.';
        $statusCode = 400;
    } elseif (strpos($e->getMessage(), 'Chapter not found') !== false) {
        $errorMessage = 'Chapter not found. Please ensure the chapter exists.';
        $statusCode = 404;
    } elseif (strpos($e->getMessage(), 'cURL error') !== false) {
        $errorMessage = 'Network connection error. Please check your internet connection and try again.';
        $statusCode = 502;
    }

    jsonResponse([
        'success' => false,
        'error' => $errorMessage
    ], $statusCode);
}

/**
 * Handle GET request - Get novel details with chapters
 */
function handleGetChapters($novelManager) {
    if (!isset($_GET['novel_id']) || !is_numeric($_GET['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id parameter is required'], 400);
    }

    $novelId = (int)$_GET['novel_id'];

    // Parse optional parameters
    $options = [];

    // Search parameter
    if (isset($_GET['search']) && !empty(trim($_GET['search']))) {
        $options['search'] = trim($_GET['search']);
    }

    // Pagination parameters
    if (isset($_GET['page']) && is_numeric($_GET['page'])) {
        $options['page'] = max(1, (int)$_GET['page']);
    }

    if (isset($_GET['limit']) && is_numeric($_GET['limit'])) {
        $limit = (int)$_GET['limit'];
        $options['limit'] = $limit === 999999 ? null : max(1, min(100, $limit)); // Handle "all" option
    } else {
        // Default pagination limit
        $options['limit'] = 10;
    }

    // Chapter range filter parameters
    if (isset($_GET['from_chapter']) && is_numeric($_GET['from_chapter'])) {
        $options['from_chapter'] = max(1, (int)$_GET['from_chapter']);
    }

    if (isset($_GET['to_chapter']) && is_numeric($_GET['to_chapter'])) {
        $options['to_chapter'] = max(1, (int)$_GET['to_chapter']);
    }

    try {
        $result = $novelManager->getNovelDetails($novelId, $options);

        jsonResponse([
            'success' => true,
            'data' => $result
        ]);

    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => $e->getMessage()
        ], 404);
    }
}

/**
 * Handle POST request - Save chapter content or create manual chapter
 */
function handleSaveChapter($novelManager) {
    try {
        // Ensure clean output buffer
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Enable error reporting for debugging but prevent HTML output
        error_reporting(E_ALL);
        ini_set('display_errors', 0);
        ini_set('log_errors', 1);

        // Log the incoming request for debugging
        error_log("Chapters API: Save chapter request received");

        $rawInput = file_get_contents('php://input');
        error_log("Chapters API: Raw input: " . $rawInput);

        $input = json_decode($rawInput, true);

        if (!$input) {
            error_log("Chapters API: Invalid JSON input - Raw: " . $rawInput);
            jsonResponse(['error' => 'Invalid JSON input'], 400);
        }

        // Validate required fields
        if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
            error_log("Chapters API: Invalid novel_id: " . ($input['novel_id'] ?? 'not set'));
            jsonResponse(['error' => 'Valid novel_id is required'], 400);
        }

        if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
            error_log("Chapters API: Invalid chapter_number: " . ($input['chapter_number'] ?? 'not set'));
            jsonResponse(['error' => 'Valid chapter_number is required'], 400);
        }

        $novelId = (int)$input['novel_id'];
        $chapterNumber = (int)$input['chapter_number'];

        error_log("Chapters API: Processing save for novel {$novelId}, chapter {$chapterNumber}");

        // Check if this is a manual chapter creation (has content in the request)
        if (isset($input['original_content']) && !empty(trim($input['original_content']))) {
            // This is a manual chapter creation
            error_log("Chapters API: Manual chapter creation");
            $result = $novelManager->addManualChapter($novelId, $input);
        } else {
            // This is a regular chapter save (crawled content)
            error_log("Chapters API: Regular chapter save (crawled content)");
            $result = $novelManager->saveChapter($novelId, $chapterNumber);
        }

        // Test JSON encoding before sending response
        $testJson = json_encode($result);
        if ($testJson === false) {
            error_log("Chapters API: JSON encoding failed for result: " . json_last_error_msg());

            // Clean the result data to fix encoding issues
            $cleanedResult = cleanDataForJson($result);
            $testJson = json_encode($cleanedResult);

            if ($testJson === false) {
                error_log("Chapters API: JSON encoding still failed after cleaning: " . json_last_error_msg());
                jsonResponse([
                    'success' => false,
                    'error' => 'Server error: Unable to encode response data'
                ], 500);
            }

            $result = $cleanedResult;
        }

        error_log("Chapters API: Save result JSON length: " . strlen($testJson));

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }

    } catch (Exception $e) {
        error_log("Chapters API: Exception in handleSaveChapter: " . $e->getMessage());
        error_log("Chapters API: Exception trace: " . $e->getTraceAsString());

        jsonResponse([
            'success' => false,
            'error' => 'Server error: ' . $e->getMessage()
        ], 500);
    } catch (Error $e) {
        error_log("Chapters API: Fatal error in handleSaveChapter: " . $e->getMessage());
        error_log("Chapters API: Error trace: " . $e->getTraceAsString());

        jsonResponse([
            'success' => false,
            'error' => 'Server error: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle PUT request - Translate chapter
 */
function handleTranslateChapter($novelManager) {
    // Increase execution time limit for translation operations
    set_time_limit(300); // 5 minutes for translation operations

    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    // Log the incoming request for debugging
    error_log("Chapters API: Translation request received");
    error_log("Chapters API: Raw input: " . $rawInput);

    if (!$input) {
        error_log("Chapters API: Invalid JSON input - Raw: " . $rawInput);
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Debug: Log parsed input
    file_put_contents('debug.log', "Parsed input: " . json_encode($input) . "\n", FILE_APPEND);

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        file_put_contents('debug.log', "Invalid novel_id: " . ($input['novel_id'] ?? 'not set') . "\n", FILE_APPEND);
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        file_put_contents('debug.log', "Invalid chapter_number: " . ($input['chapter_number'] ?? 'not set') . "\n", FILE_APPEND);
        jsonResponse(['error' => 'Valid chapter_number is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $targetLanguage = isset($input['target_language']) ? sanitizeInput($input['target_language']) : DEFAULT_TARGET_LANGUAGE;
    $action = isset($input['action']) ? sanitizeInput($input['action']) : 'translate';

    // Support both single chapter and bulk operations
    $chapterNumbers = [];
    if (isset($input['chapter_number']) && is_numeric($input['chapter_number'])) {
        // Single chapter operation
        $chapterNumbers = [(int)$input['chapter_number']];
    } elseif (isset($input['chapter_numbers']) && is_array($input['chapter_numbers'])) {
        // Bulk operation
        $chapterNumbers = array_map('intval', $input['chapter_numbers']);
    } else {
        jsonResponse(['error' => 'Either chapter_number or chapter_numbers array is required'], 400);
    }

    if (empty($chapterNumbers)) {
        jsonResponse(['error' => 'No chapters specified for translation'], 400);
    }

    file_put_contents('debug.log', "Starting {$action} - Novel ID: {$novelId}, Chapters: " . implode(',', $chapterNumbers) . ", Language: {$targetLanguage}\n", FILE_APPEND);

    try {
        if ($action === 'retranslate') {
            $result = $novelManager->retranslateChapters($novelId, $chapterNumbers, $targetLanguage);
        } else {
            $result = $novelManager->translateChapters($novelId, $chapterNumbers, $targetLanguage);
        }

        // Debug: Output result details
        file_put_contents('debug.log', "Translation result: " . json_encode($result) . "\n", FILE_APPEND);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            // Determine appropriate HTTP status code based on error type
            $statusCode = 400; // Default to bad request
            $errorMessage = $result['error'] ?? 'Unknown error';

            // For bulk operations, check individual results for specific error types
            if (isset($result['results']) && is_array($result['results'])) {
                foreach ($result['results'] as $chapterResult) {
                    if (!$chapterResult['success'] && isset($chapterResult['error'])) {
                        $chapterError = $chapterResult['error'];

                        // Check for specific error types and set appropriate status codes
                        if (strpos($chapterError, 'HTTP error: 503') !== false ||
                            strpos($chapterError, 'overloaded') !== false ||
                            strpos($chapterError, 'Service Unavailable') !== false) {
                            $statusCode = 503;
                            $errorMessage = $chapterError;
                            break; // Use the first specific error found
                        } elseif (strpos($chapterError, 'HTTP error: 429') !== false ||
                                  strpos($chapterError, 'quota') !== false ||
                                  strpos($chapterError, 'rate limit') !== false) {
                            $statusCode = 429;
                            $errorMessage = $chapterError;
                            break; // Use the first specific error found
                        } elseif (strpos($chapterError, 'timeout') !== false ||
                                  strpos($chapterError, 'timed out') !== false) {
                            $statusCode = 408;
                            $errorMessage = $chapterError;
                            break; // Use the first specific error found
                        } elseif (strpos($chapterError, 'Chapter content not saved') !== false) {
                            // This is a legitimate 400 error - keep default status code
                            $errorMessage = $chapterError;
                            break;
                        }
                    }
                }
            } else {
                // Single operation - check the main error message
                if (strpos($errorMessage, 'HTTP error: 503') !== false ||
                    strpos($errorMessage, 'overloaded') !== false ||
                    strpos($errorMessage, 'Service Unavailable') !== false) {
                    $statusCode = 503;
                } elseif (strpos($errorMessage, 'HTTP error: 429') !== false ||
                          strpos($errorMessage, 'quota') !== false ||
                          strpos($errorMessage, 'rate limit') !== false) {
                    $statusCode = 429;
                } elseif (strpos($errorMessage, 'timeout') !== false ||
                          strpos($errorMessage, 'timed out') !== false) {
                    $statusCode = 408;
                }
            }

            jsonResponse([
                'success' => false,
                'error' => $errorMessage
            ], $statusCode);
        }
    } catch (Exception $e) {
        // Debug: Output exception details
        file_put_contents('debug.log', "Exception: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString() . "\n", FILE_APPEND);

        // Determine status code based on exception message
        $statusCode = 500;
        $errorMessage = 'Translation failed: ' . $e->getMessage();

        if (strpos($e->getMessage(), 'HTTP error: 503') !== false ||
            strpos($e->getMessage(), 'overloaded') !== false) {
            $statusCode = 503;
            $errorMessage = 'Translation service is temporarily overloaded. Please try again in a few minutes.';
        } elseif (strpos($e->getMessage(), 'HTTP error: 429') !== false ||
                  strpos($e->getMessage(), 'quota') !== false) {
            $statusCode = 429;
            $errorMessage = 'Translation service quota exceeded. Please wait and try again later.';
        } elseif (strpos($e->getMessage(), 'timeout') !== false ||
                  strpos($e->getMessage(), 'timed out') !== false) {
            $statusCode = 408;
            $errorMessage = 'Translation request timed out. The chapter may be too large. Please try again.';
        }

        jsonResponse([
            'success' => false,
            'error' => $errorMessage
        ], $statusCode);
    }
}

/**
 * Handle PATCH request - Update chapter translation
 */
function handleUpdateTranslation($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        jsonResponse(['error' => 'Valid chapter_number is required'], 400);
    }

    if (!isset($input['translated_content']) || empty(trim($input['translated_content']))) {
        jsonResponse(['error' => 'Translated content is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];
    $translatedContent = trim($input['translated_content']);

    try {
        $result = $novelManager->updateChapterTranslation($novelId, $chapterNumber, $translatedContent);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }
    } catch (Exception $e) {
        error_log("Chapters API: Exception during translation update: " . $e->getMessage());

        jsonResponse([
            'success' => false,
            'error' => 'Failed to update translation: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle DELETE request - Clear chapter translations
 */
function handleDeleteTranslations($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    $novelId = (int)$input['novel_id'];

    // Support both single chapter and bulk operations
    $chapterNumbers = [];
    if (isset($input['chapter_number']) && is_numeric($input['chapter_number'])) {
        // Single chapter operation
        $chapterNumbers = [(int)$input['chapter_number']];
    } elseif (isset($input['chapter_numbers']) && is_array($input['chapter_numbers'])) {
        // Bulk operation
        $chapterNumbers = array_map('intval', $input['chapter_numbers']);
    } else {
        jsonResponse(['error' => 'Either chapter_number or chapter_numbers array is required'], 400);
    }

    if (empty($chapterNumbers)) {
        jsonResponse(['error' => 'No chapters specified for deletion'], 400);
    }

    try {
        $result = $novelManager->clearChapterTranslations($novelId, $chapterNumbers);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }
    } catch (Exception $e) {
        error_log("Chapters API: Exception during translation deletion: " . $e->getMessage());

        jsonResponse([
            'success' => false,
            'error' => 'Failed to clear translations: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle PATCH request - Update chapter title
 */
function handleUpdateChapterTitle($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        jsonResponse(['error' => 'Valid chapter_number is required'], 400);
    }

    if (!isset($input['translated_title']) || empty(trim($input['translated_title']))) {
        jsonResponse(['error' => 'Translated title is required and cannot be empty'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];
    $translatedTitle = trim($input['translated_title']);

    try {
        $result = $novelManager->updateChapterTitle($novelId, $chapterNumber, $translatedTitle);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }
    } catch (Exception $e) {
        error_log("Chapters API: Exception during title update: " . $e->getMessage());

        jsonResponse([
            'success' => false,
            'error' => 'Failed to update chapter title: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle PATCH request - Update chapter original content
 */
function handleUpdateOriginalContent($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        jsonResponse(['error' => 'Valid chapter_number is required'], 400);
    }

    if (!isset($input['original_content']) || empty(trim($input['original_content']))) {
        jsonResponse(['error' => 'Original content is required and cannot be empty'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];
    $originalContent = trim($input['original_content']);

    try {
        $result = $novelManager->updateChapterOriginalContent($novelId, $chapterNumber, $originalContent);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }
    } catch (Exception $e) {
        error_log("Chapters API: Exception during original content update: " . $e->getMessage());

        jsonResponse([
            'success' => false,
            'error' => 'Failed to update chapter original content: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle DELETE request with action 'recrawl_chapter' - Recrawl chapter content
 */
function handleRecrawlChapter($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        jsonResponse(['error' => 'Valid chapter_number is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];

    try {
        $result = $novelManager->recrawlChapter($novelId, $chapterNumber);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }
    } catch (Exception $e) {
        error_log("Chapters API: Exception during chapter recrawl: " . $e->getMessage());

        jsonResponse([
            'success' => false,
            'error' => 'Failed to recrawl chapter: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle DELETE request with action 'delete_chapter' - Clear chapter content while preserving entry
 */
function handleDeleteChapter($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        jsonResponse(['error' => 'Valid chapter_number is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];

    try {
        $result = $novelManager->deleteChapter($novelId, $chapterNumber);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }
    } catch (Exception $e) {
        error_log("Chapters API: Exception during chapter content clearing: " . $e->getMessage());

        jsonResponse([
            'success' => false,
            'error' => 'Failed to clear chapter content: ' . $e->getMessage()
        ], 500);
    }
}
