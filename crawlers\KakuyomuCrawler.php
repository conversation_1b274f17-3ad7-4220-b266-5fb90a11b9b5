<?php
/**
 * Ka<PERSON><PERSON><PERSON> (kakuyomu.jp) Crawler
 * Novel Translation Application
 */

class KakuyomuCrawler extends BaseCrawler {
    
    private const BASE_URL = 'https://kakuyomu.jp';
    
    /**
     * Validate Kakuyomu URL
     */
    protected function validateUrl(string $url): bool {
        return preg_match('/kakuyomu\.jp\/works\/\d+/', $url) === 1;
    }
    
    /**
     * Get novel information from Kakuyomu
     */
    public function getNovelInfo(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Kakuyomu URL: {$url}");
        }
        
        $this->log("Fetching novel info from: {$url}");
        
        try {
            $html = $this->makeRequest($url);
            $dom = $this->parseHtml($html);
            
            // Extract novel information
            $title = $this->extractTitle($dom);
            $author = $this->extractAuthor($dom);
            $synopsis = $this->extractSynopsis($dom);
            $publishDate = $this->extractPublishDate($dom);
            $totalChapters = $this->extractTotalChapters($dom);
            
            $this->log("Successfully extracted novel info: {$title}");
            
            return [
                'platform' => 'kakuyomu',
                'url' => $url,
                'original_title' => $title,
                'author' => $author,
                'original_synopsis' => $synopsis,
                'publication_date' => $publishDate,
                'total_chapters' => $totalChapters,
                'language' => 'ja'
            ];
            
        } catch (Exception $e) {
            $this->log("Error fetching novel info: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Get chapter list from Kakuyomu
     */
    public function getChapterList(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Kakuyomu URL: {$url}");
        }

        $this->log("Fetching chapter list from: {$url}");

        try {
            $html = $this->makeRequest($url);
            $dom = $this->parseHtml($html);

            $chapters = [];

            // First, try to extract chapters from all visible and collapsed sections
            $chapters = $this->extractAllChapters($dom);

            // If we didn't find many chapters, try alternative extraction methods
            if (count($chapters) < 5) {
                $this->log("Found only " . count($chapters) . " chapters, trying alternative extraction methods");
                $alternativeChapters = $this->extractChaptersAlternative($dom);
                if (count($alternativeChapters) > count($chapters)) {
                    $chapters = $alternativeChapters;
                }
            }

            // Remove duplicates and renumber
            $chapters = $this->removeDuplicateChapters($chapters);

            $this->log("Found " . count($chapters) . " chapters");

            return $chapters;

        } catch (Exception $e) {
            $this->log("Error fetching chapter list: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Extract all chapters including those in collapsed volumes
     */
    private function extractAllChapters(DOMDocument $dom): array {
        $chapters = [];

        // First, try the new Kakuyomu structure with WorkTocAccordion
        $chapters = $this->extractChaptersFromNewStructure($dom);

        if (!empty($chapters)) {
            $this->log("Found " . count($chapters) . " chapters using new Kakuyomu structure");
            return $chapters;
        }

        // Fallback to old structure
        $volumeSelectors = [
            '.widget-toc-chapter',
            '.widget-toc-section',
            '.js-vertical-composition-item',
            '.widget-toc-main'
        ];

        foreach ($volumeSelectors as $selector) {
            $volumes = $this->querySelectorAll($dom, $selector);
            if ($volumes->length > 0) {
                $this->log("Found " . $volumes->length . " volumes/sections using selector: {$selector}");

                foreach ($volumes as $volume) {
                    $volumeChapters = $this->extractChaptersFromVolume($volume);
                    $chapters = array_merge($chapters, $volumeChapters);
                }

                if (!empty($chapters)) {
                    break; // Found chapters, no need to try other selectors
                }
            }
        }

        // If no chapters found in volumes, try direct episode extraction
        if (empty($chapters)) {
            $chapters = $this->extractChaptersDirect($dom);
        }

        return $chapters;
    }

    /**
     * Extract chapters from new Kakuyomu structure (2024+ with WorkTocAccordion)
     */
    private function extractChaptersFromNewStructure(DOMDocument $dom): array {
        $chapters = [];

        // Look for the main TOC container
        $tocContainer = $this->querySelector($dom, '._workId__toc___I_tx');
        if (!$tocContainer) {
            $this->log("New structure TOC container not found");
            return [];
        }

        $this->log("Found new structure TOC container");

        // Try to extract from embedded JSON data first (most reliable)
        $chapters = $this->extractChaptersFromJsonData($dom);
        if (!empty($chapters)) {
            $this->log("Successfully extracted " . count($chapters) . " chapters from JSON data");
            return $chapters;
        }

        // Fallback: extract from visible accordion sections
        $accordionSections = $this->querySelectorAll($tocContainer, '.WorkTocAccordion_contents__6nJhY');
        $this->log("Found " . $accordionSections->length . " accordion sections");

        foreach ($accordionSections as $section) {
            $sectionChapters = $this->extractChaptersFromAccordionSection($section);
            $chapters = array_merge($chapters, $sectionChapters);
        }

        return $chapters;
    }

    /**
     * Extract chapters from embedded JSON data in the page
     */
    private function extractChaptersFromJsonData(DOMDocument $dom): array {
        $chapters = [];

        // Look for Next.js data in script tags
        $scriptElements = $this->querySelectorAll($dom, 'script[id="__NEXT_DATA__"]');

        foreach ($scriptElements as $script) {
            $jsonContent = $script->textContent;
            if (empty($jsonContent)) continue;

            try {
                $data = json_decode($jsonContent, true);
                if (!$data) continue;

                // Navigate through the JSON structure to find episodes
                $episodes = $this->extractEpisodesFromJsonData($data);
                if (!empty($episodes)) {
                    $this->log("Found " . count($episodes) . " episodes in JSON data");

                    foreach ($episodes as $i => $episode) {
                        $chapters[] = [
                            'chapter_number' => $i + 1,
                            'chapter_url' => $this->normalizeUrl('/works/' . $episode['workId'] . '/episodes/' . $episode['id'], self::BASE_URL),
                            'original_title' => $episode['title']
                        ];
                    }

                    return $chapters;
                }

            } catch (Exception $e) {
                $this->log("Error parsing JSON data: " . $e->getMessage());
                continue;
            }
        }

        return [];
    }

    /**
     * Extract episodes from JSON data structure
     */
    private function extractEpisodesFromJsonData(array $data): array {
        $episodes = [];

        // Navigate through the Apollo state to find episodes
        if (isset($data['props']['pageProps']['__APOLLO_STATE__'])) {
            $apolloState = $data['props']['pageProps']['__APOLLO_STATE__'];

            // Look for table of contents chapters
            foreach ($apolloState as $key => $value) {
                if (strpos($key, 'TableOfContentsChapter:') === 0 && isset($value['episodeUnions'])) {
                    foreach ($value['episodeUnions'] as $episodeRef) {
                        if (isset($episodeRef['__ref'])) {
                            $episodeKey = $episodeRef['__ref'];
                            if (isset($apolloState[$episodeKey])) {
                                $episode = $apolloState[$episodeKey];
                                if (isset($episode['id'], $episode['title'])) {
                                    // Extract work ID from the current page
                                    $workId = $this->extractWorkIdFromJsonData($data);
                                    if ($workId) {
                                        $episodes[] = [
                                            'id' => $episode['id'],
                                            'title' => $episode['title'],
                                            'workId' => $workId,
                                            'publishedAt' => $episode['publishedAt'] ?? null
                                        ];
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }

        // Sort by published date if available
        if (!empty($episodes)) {
            usort($episodes, function($a, $b) {
                if (!isset($a['publishedAt']) || !isset($b['publishedAt'])) {
                    return 0;
                }
                return strcmp($a['publishedAt'], $b['publishedAt']);
            });
        }

        return $episodes;
    }

    /**
     * Extract work ID from JSON data
     */
    private function extractWorkIdFromJsonData(array $data): ?string {
        if (isset($data['query']['workId'])) {
            return $data['query']['workId'];
        }

        // Look in Apollo state for work references
        if (isset($data['props']['pageProps']['__APOLLO_STATE__'])) {
            $apolloState = $data['props']['pageProps']['__APOLLO_STATE__'];
            foreach ($apolloState as $key => $value) {
                if (strpos($key, 'Work:') === 0) {
                    return str_replace('Work:', '', $key);
                }
            }
        }

        return null;
    }

    /**
     * Extract chapters from accordion section
     */
    private function extractChaptersFromAccordionSection(DOMElement $section): array {
        $chapters = [];

        // Look for episode links in this section
        $episodeLinks = $this->querySelectorAll($section->ownerDocument, 'a[href*="/episodes/"]');

        foreach ($episodeLinks as $link) {
            // Check if this link is within the current section
            if ($section->contains($link)) {
                $chapterUrl = $link->getAttribute('href');
                $chapterTitle = $this->cleanText($link->textContent);

                if ($this->isValidChapterLink($chapterUrl, $chapterTitle)) {
                    $fullChapterUrl = $this->normalizeUrl($chapterUrl, self::BASE_URL);

                    $chapters[] = [
                        'chapter_number' => 0, // Will be renumbered later
                        'chapter_url' => $fullChapterUrl,
                        'original_title' => $chapterTitle
                    ];
                }
            }
        }

        return $chapters;
    }

    /**
     * Extract chapters from a volume/section container
     */
    private function extractChaptersFromVolume(DOMElement $volume): array {
        $chapters = [];

        // Look for episode links within this volume
        $episodeSelectors = [
            'a[href*="/episodes/"]',
            '.widget-toc-episode a',
            '.js-vertical-composition-item a',
            '.episode-link'
        ];

        foreach ($episodeSelectors as $selector) {
            $episodeElements = $this->querySelectorAll($volume->ownerDocument, $selector);

            // Filter to only episodes within this volume
            foreach ($episodeElements as $element) {
                if ($volume->contains($element) || $element->parentNode === $volume) {
                    $chapterUrl = $element->getAttribute('href');
                    $chapterTitle = $this->cleanText($element->textContent);

                    if ($this->isValidChapterLink($chapterUrl, $chapterTitle)) {
                        $fullChapterUrl = $this->normalizeUrl($chapterUrl, self::BASE_URL);

                        $chapters[] = [
                            'chapter_number' => 0, // Will be renumbered later
                            'chapter_url' => $fullChapterUrl,
                            'original_title' => $chapterTitle
                        ];
                    }
                }
            }

            if (!empty($chapters)) {
                break; // Found chapters with this selector
            }
        }

        return $chapters;
    }

    /**
     * Extract chapters directly from the DOM (fallback method)
     */
    private function extractChaptersDirect(DOMDocument $dom): array {
        $chapters = [];

        // Try multiple selectors for chapter links
        $selectors = [
            'a[href*="/episodes/"]',
            '.widget-toc-episode a',
            '.widget-toc-main a',
            '.episode-list a',
            '.chapter-list a'
        ];

        foreach ($selectors as $selector) {
            $chapterElements = $this->querySelectorAll($dom, $selector);

            if ($chapterElements->length > 0) {
                $this->log("Found " . $chapterElements->length . " chapter elements using selector: {$selector}");

                foreach ($chapterElements as $element) {
                    $chapterUrl = $element->getAttribute('href');
                    $chapterTitle = $this->cleanText($element->textContent);

                    if ($this->isValidChapterLink($chapterUrl, $chapterTitle)) {
                        $fullChapterUrl = $this->normalizeUrl($chapterUrl, self::BASE_URL);

                        $chapters[] = [
                            'chapter_number' => 0, // Will be renumbered later
                            'chapter_url' => $fullChapterUrl,
                            'original_title' => $chapterTitle
                        ];
                    }
                }

                if (!empty($chapters)) {
                    break; // Found chapters with this selector
                }
            }
        }

        return $chapters;
    }

    /**
     * Alternative extraction method using different approach
     */
    private function extractChaptersAlternative(DOMDocument $dom): array {
        $chapters = [];

        // Try to find all links that look like episode URLs
        $xpath = new DOMXPath($dom);
        $episodeLinks = $xpath->query('//a[contains(@href, "/episodes/")]');

        if ($episodeLinks->length > 0) {
            $this->log("Found " . $episodeLinks->length . " episode links using XPath");

            foreach ($episodeLinks as $link) {
                $chapterUrl = $link->getAttribute('href');
                $chapterTitle = $this->cleanText($link->textContent);

                if ($this->isValidChapterLink($chapterUrl, $chapterTitle)) {
                    $fullChapterUrl = $this->normalizeUrl($chapterUrl, self::BASE_URL);

                    $chapters[] = [
                        'chapter_number' => 0, // Will be renumbered later
                        'chapter_url' => $fullChapterUrl,
                        'original_title' => $chapterTitle
                    ];
                }
            }
        }

        return $chapters;
    }

    /**
     * Validate if a link is a valid chapter link
     */
    private function isValidChapterLink(string $chapterUrl, string $chapterTitle): bool {
        return $chapterUrl && $chapterTitle &&
               strpos($chapterUrl, '/episodes/') !== false && // Must be episode URL
               !preg_match('/から読む$/', $chapterTitle) && // Skip "1話目から読む" type links
               !preg_match('/^(次へ|前へ|目次|もっと見る|続きを読む)$/', $chapterTitle) && // Skip navigation links
               strlen(trim($chapterTitle)) > 0; // Must have actual title
    }

    /**
     * Remove duplicate chapters and renumber
     */
    private function removeDuplicateChapters(array $chapters): array {
        $uniqueChapters = [];
        $seenUrls = [];

        foreach ($chapters as $chapter) {
            $url = $chapter['chapter_url'];
            if (!in_array($url, $seenUrls)) {
                $seenUrls[] = $url;
                $uniqueChapters[] = $chapter;
            }
        }

        // Renumber chapters
        foreach ($uniqueChapters as $index => &$chapter) {
            $chapter['chapter_number'] = $index + 1;
        }

        return $uniqueChapters;
    }
    
    /**
     * Get chapter content from Kakuyomu
     */
    public function getChapterContent(string $chapterUrl): array {
        $this->log("Fetching chapter content from: {$chapterUrl}");
        
        try {
            $html = $this->makeRequest($chapterUrl);
            $dom = $this->parseHtml($html);
            
            // Extract chapter title with fallback selectors
            $title = $this->extractChapterTitle($dom);

            // Extract chapter content with fallback selectors
            $contentElement = $this->findContentElement($dom);
            if (!$contentElement) {
                throw new Exception("Chapter content not found");
            }
            
            $content = $this->extractChapterText($contentElement);
            
            $this->log("Successfully extracted chapter content");
            
            return [
                'original_title' => $title,
                'original_content' => $content,
                'word_count' => mb_strlen($content)
            ];
            
        } catch (Exception $e) {
            $this->log("Error fetching chapter content: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Extract novel title
     */
    private function extractTitle(DOMDocument $dom): string {
        $titleElement = $this->querySelector($dom, '.widget-workTitle');
        if (!$titleElement) {
            $titleElement = $this->querySelector($dom, 'h1');
        }
        
        return $titleElement ? $this->cleanText($titleElement->textContent) : '';
    }
    
    /**
     * Extract author name
     */
    private function extractAuthor(DOMDocument $dom): string {
        $authorSelectors = [
            '.widget-workAuthor-name a',
            '.widget-workAuthor-name',
            '.widget-workAuthor a',
            '.widget-workAuthor',
            '.author-name',
            '.work-author',
            'a[href*="/users/"]'
        ];

        foreach ($authorSelectors as $selector) {
            $authorElement = $this->querySelector($dom, $selector);
            if ($authorElement) {
                $authorText = $this->cleanText($authorElement->textContent);
                if (!empty($authorText)) {
                    $this->log("Author extracted using selector '{$selector}': {$authorText}");
                    return $authorText;
                }
            }
        }

        $this->log("Author extraction failed - no matching elements found", 'warning');
        return '';
    }
    
    /**
     * Extract synopsis from the 概要 (Overview) section
     */
    private function extractSynopsis(DOMDocument $dom): string {
        $xpath = new DOMXPath($dom);

        // Method 1: Look for the specific Kakuyomu synopsis structure
        // Based on the HTML structure, the synopsis is in a specific container
        $synopsisSelectors = [
            // Try different possible selectors for Kakuyomu synopsis
            '.widget-workSummary .ui-truncateTextButton-expandee',
            '.widget-workSummary .ui-truncateTextButton-text',
            '.widget-workSummary-text',
            '.widget-workSummary',
            '.work-summary-text',
            '.work-summary',
            '.summary-text',
            '.summary'
        ];

        foreach ($synopsisSelectors as $selector) {
            $element = $this->querySelector($dom, $selector);
            if ($element) {
                $text = $this->cleanText($element->textContent);
                $text = $this->cleanSynopsisText($text);

                if (!empty($text) && strlen($text) > 50) {
                    $this->log("Synopsis extracted using selector '{$selector}': " . substr($text, 0, 100) . "...");
                    return $text;
                }
            }
        }

        // Method 2: Look for text content that matches the expected synopsis pattern and try to get complete content
        $synopsisFragments = [];
        $textNodes = $xpath->query('//text()[contains(., "超人気Vtuber") or contains(., "作家志望の無職") or contains(., "友村友人") or contains(., "エルダーフロンティア") or contains(., "集められた") or contains(., "先行プレイヤー")]');

        foreach ($textNodes as $textNode) {
            // Get the parent element to potentially capture more complete content
            $parentElement = $textNode->parentNode;
            while ($parentElement && $parentElement->nodeType === XML_ELEMENT_NODE) {
                $parentText = $this->cleanText($parentElement->textContent);
                $parentText = $this->cleanSynopsisText($parentText);

                // If parent contains more complete content, use it
                if (strlen($parentText) > strlen($textNode->textContent) &&
                    strlen($parentText) < 3000 &&
                    $this->containsNovelContent($parentText) &&
                    !$this->containsUnwantedContent($parentText)) {

                    $synopsisFragments[] = $parentText;
                    $this->log("Found synopsis from parent element: " . substr($parentText, 0, 100) . "...");
                    break;
                }
                $parentElement = $parentElement->parentNode;
            }

            // Also add the original text node content
            $text = $this->cleanText($textNode->textContent);
            $text = $this->cleanSynopsisText($text);

            if (strlen($text) > 50 && $this->containsNovelContent($text) && !$this->containsUnwantedContent($text)) {
                $synopsisFragments[] = $text;
                $this->log("Found synopsis fragment: " . substr($text, 0, 100) . "...");
            }
        }

        // Try to combine fragments to get complete synopsis
        if (!empty($synopsisFragments)) {
            // Remove duplicates and sort by length (longer first)
            $synopsisFragments = array_unique($synopsisFragments);
            usort($synopsisFragments, function($a, $b) {
                return strlen($b) - strlen($a);
            });

            // Try to find the most complete fragment or combine them
            $bestSynopsis = $this->combineSynopsisFragments($synopsisFragments);
            if (!empty($bestSynopsis)) {
                $this->log("Synopsis extracted from combined fragments: " . substr($bestSynopsis, 0, 100) . "...");
                return $bestSynopsis;
            }
        }

        // Method 3: Look for larger containers that might contain the complete synopsis
        $allElements = $xpath->query('//*[contains(text(), "超人気Vtuber") or contains(text(), "友村友人") or contains(text(), "エルダーフロンティア")]');

        foreach ($allElements as $element) {
            $text = $this->cleanText($element->textContent);
            $text = $this->cleanSynopsisText($text);

            if (strlen($text) > 300 && strlen($text) < 5000 &&
                preg_match('/(超人気Vtuber|友村友人|エルダーフロンティア)/', $text) &&
                !$this->containsUnwantedContent($text)) {

                $this->log("Synopsis extracted from large container: " . substr($text, 0, 100) . "...");
                return $text;
            }
        }

        // Method 4: Look for paragraphs containing synopsis-like content
        $paragraphs = $this->querySelectorAll($dom, 'p');
        foreach ($paragraphs as $p) {
            $text = $this->cleanText($p->textContent);
            $text = $this->cleanSynopsisText($text);

            if (strlen($text) > 100 &&
                preg_match('/(超人気Vtuber|作家志望の無職|友村友人|エルダーフロンティア|転移|転生|異世界|主人公|物語|作品|小説|冒険|魔法|勇者|薬師|Vtuber|ゲーム|プレイヤー|世界|職業)/', $text) &&
                !$this->containsUnwantedContent($text)) {

                $this->log("Synopsis extracted from paragraph: " . substr($text, 0, 100) . "...");
                return $text;
            }
        }

        // Method 5: Look in div elements for synopsis content
        $divs = $this->querySelectorAll($dom, 'div');
        foreach ($divs as $div) {
            $text = $this->cleanText($div->textContent);
            $text = $this->cleanSynopsisText($text);

            if (strlen($text) > 200 && strlen($text) < 3000 &&
                preg_match('/(超人気Vtuber|作家志望の無職|友村友人|エルダーフロンティア)/', $text) &&
                !$this->containsUnwantedContent($text)) {

                $this->log("Synopsis extracted from div: " . substr($text, 0, 100) . "...");
                return $text;
            }
        }

        $this->log("Synopsis extraction failed - no matching elements found", 'warning');
        return '';
    }



    /**
     * Clean synopsis text by removing unwanted elements
     */
    private function cleanSynopsisText(string $text): string {
        // Remove "続きを読む" from anywhere in the text
        $text = preg_replace('/続きを読む\s*/', '', $text);

        // Remove section headers
        $text = preg_replace('/^概要\s*/', '', $text);
        $text = preg_replace('/概要\s*/', '', $text);

        // Remove trailing sections that shouldn't be part of synopsis
        $text = preg_replace('/関連小説.*$/s', '', $text);
        $text = preg_replace('/詳細.*$/s', '', $text);
        $text = preg_replace('/おすすめレビュー.*$/s', '', $text);
        $text = preg_replace('/目次.*$/s', '', $text);
        $text = preg_replace('/レビュー.*$/s', '', $text);
        $text = preg_replace('/もっと見る.*$/s', '', $text);
        $text = preg_replace('/フォロー.*$/s', '', $text);
        $text = preg_replace('/ログイン.*$/s', '', $text);

        // Remove ellipsis patterns that indicate truncation
        $text = preg_replace('/…\s*$/', '', $text);
        $text = preg_replace('/\.\.\.\s*$/', '', $text);

        // Clean up extra whitespace and normalize
        $text = preg_replace('/\s+/', ' ', $text);
        $text = trim($text);

        return $text;
    }

    /**
     * Combine synopsis fragments to create complete synopsis
     */
    private function combineSynopsisFragments(array $fragments): string {
        if (empty($fragments)) {
            return '';
        }

        // If we have a long fragment (likely complete), use it
        foreach ($fragments as $fragment) {
            if (strlen($fragment) > 500) {
                return $fragment;
            }
        }

        // Try to intelligently combine fragments
        $combined = '';
        $usedFragments = [];

        // Start with the fragment that looks like the beginning
        foreach ($fragments as $fragment) {
            if (preg_match('/^(超人気Vtuber|作家志望の無職|集められた|友村友人)/', $fragment)) {
                $combined = $fragment;
                $usedFragments[] = $fragment;
                break;
            }
        }

        // If no clear beginning found, use the longest fragment
        if (empty($combined)) {
            $combined = $fragments[0];
            $usedFragments[] = $fragments[0];
        }

        // Try to add complementary fragments
        foreach ($fragments as $fragment) {
            if (!in_array($fragment, $usedFragments) &&
                strlen($fragment) > 50 &&
                strlen($combined . ' ' . $fragment) < 2000) {

                // Check if this fragment adds new content
                $similarity = similar_text($combined, $fragment);
                if ($similarity < strlen($fragment) * 0.7) { // Less than 70% similar
                    $combined .= ' ' . $fragment;
                    $usedFragments[] = $fragment;
                }
            }
        }

        return trim($combined);
    }



    /**
     * Check if text contains novel content indicators
     */
    private function containsNovelContent(string $text): bool {
        return preg_match('/(転移|転生|異世界|主人公|物語|作品|小説|冒険|魔法|勇者|薬師|Vtuber|ゲーム|プレイヤー|世界|職業|友村|エルダーフロンティア)/', $text) ||
               preg_match('/。.*。/', $text); // Multiple sentences
    }

    /**
     * Check if text contains unwanted content
     */
    private function containsUnwantedContent(string $text): bool {
        return preg_match('/(話|章|エピソード|更新|公開|投稿|作者|タグ|ランキング|レビュー|フォロー|ログイン|新規登録|マイページ|目次|もっと見る|待って|読ませて|面白|楽しみ|お疲れ|ありがとう|おかえり|毎秒更新|身体に気をつけて|★|☆|評価|点数|いいね|@|件の|投稿|時間|分|秒|Good|Excellent|なろうのシリーズ|ドハマリ|見に来ました|作品読んで|好きになったら|美味しくいただける|タイプの作者|島.*ひとみ|曲どれか|気に入ったら|頭のネジ|簡単に外れる|ねっとりじっとり|重たい美女|大好きな犬|熱い視線|定評のある|作者さん)/', $text);
    }
    
    /**
     * Extract publication date
     */
    private function extractPublishDate(DOMDocument $dom): ?string {
        // First try to extract from the first chapter's publication date
        $firstChapterDate = $this->extractFirstChapterDate($dom);
        if ($firstChapterDate) {
            $this->log("Publication date extracted from first chapter: {$firstChapterDate}");
            return $firstChapterDate;
        }

        // Try work metadata selectors
        $dateSelectors = [
            '.widget-workMeta-item',
            '.widget-workMeta',
            '.work-meta',
            '.widget-workAuthor-activityTime',
            '.widget-workAuthor time',
            '.publish-date',
            '.created-date',
            '.date',
            'time[datetime]'
        ];

        foreach ($dateSelectors as $selector) {
            $dateElements = $this->querySelectorAll($dom, $selector);

            foreach ($dateElements as $element) {
                // Check for datetime attribute first
                if ($element->hasAttribute('datetime')) {
                    $datetime = $element->getAttribute('datetime');
                    $date = $this->extractDate($datetime);
                    if ($date) {
                        $this->log("Publication date extracted from datetime attribute '{$selector}': {$date}");
                        return $date;
                    }
                }

                $dateText = $this->cleanText($element->textContent);

                // Look for Japanese publication keywords or any date pattern
                if (strpos($dateText, '公開') !== false ||
                    strpos($dateText, '投稿') !== false ||
                    strpos($dateText, '作成') !== false ||
                    strpos($dateText, '更新') !== false ||
                    preg_match('/\d{4}/', $dateText)) {

                    $date = $this->extractDate($dateText);
                    if ($date) {
                        $this->log("Publication date extracted using selector '{$selector}': {$date}");
                        return $date;
                    }
                }
            }
        }

        // Try to find date in meta tags
        $metaElements = $this->querySelectorAll($dom, 'meta[property*="date"], meta[name*="date"], meta[property*="time"], meta[name*="time"]');
        foreach ($metaElements as $element) {
            $content = $element->getAttribute('content');
            if ($content) {
                $date = $this->extractDate($content);
                if ($date) {
                    $this->log("Publication date extracted from meta tag: {$date}");
                    return $date;
                }
            }
        }

        // Try JSON-LD structured data
        $jsonLdElements = $this->querySelectorAll($dom, 'script[type="application/ld+json"]');
        foreach ($jsonLdElements as $element) {
            $jsonContent = $element->textContent;
            if ($jsonContent) {
                $data = json_decode($jsonContent, true);
                if ($data && isset($data['datePublished'])) {
                    $date = $this->extractDate($data['datePublished']);
                    if ($date) {
                        $this->log("Publication date extracted from JSON-LD: {$date}");
                        return $date;
                    }
                }
            }
        }

        $this->log("Publication date extraction failed - no matching elements found", 'warning');
        return null;
    }

    /**
     * Extract publication date from the first chapter
     */
    private function extractFirstChapterDate(DOMDocument $dom): ?string {
        // Look for the first episode/chapter and its publication date
        $episodeSelectors = [
            '.widget-toc-episode',
            '.js-vertical-composition-item',
            '.episode-item'
        ];

        foreach ($episodeSelectors as $selector) {
            $episodes = $this->querySelectorAll($dom, $selector);
            if ($episodes->length > 0) {
                $firstEpisode = $episodes->item(0);

                // Look for time elements within the first episode
                $timeElements = $this->querySelectorAll($firstEpisode->ownerDocument, 'time');
                foreach ($timeElements as $timeElement) {
                    if ($firstEpisode->contains($timeElement)) {
                        if ($timeElement->hasAttribute('datetime')) {
                            $datetime = $timeElement->getAttribute('datetime');
                            $date = $this->extractDate($datetime);
                            if ($date) {
                                return $date;
                            }
                        }

                        $timeText = $this->cleanText($timeElement->textContent);
                        $date = $this->extractDate($timeText);
                        if ($date) {
                            return $date;
                        }
                    }
                }

                // Look for date text within the episode
                $episodeText = $this->cleanText($firstEpisode->textContent);
                $date = $this->extractDate($episodeText);
                if ($date) {
                    return $date;
                }
            }
        }

        return null;
    }
    
    /**
     * Extract total chapters
     */
    private function extractTotalChapters(DOMDocument $dom): int {
        // Count episodes in the table of contents
        $episodeElements = $this->querySelectorAll($dom, '.widget-toc-episode');
        if ($episodeElements->length > 0) {
            return $episodeElements->length;
        }
        
        // Alternative: look for episode count in metadata
        $metaElements = $this->querySelectorAll($dom, '.widget-workMeta-item');
        foreach ($metaElements as $element) {
            $text = $this->cleanText($element->textContent);
            if (preg_match('/(\d+)話/', $text, $matches)) {
                return (int) $matches[1];
            }
        }
        
        return 0;
    }
    
    /**
     * Extract chapter title with fallback selectors
     */
    private function extractChapterTitle(DOMDocument $dom): string {
        $titleSelectors = [
            '.widget-episodeTitle',
            '.p-episode__title',
            '.episode-title',
            'h1',
            '.chapter-title'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title)) {
                    return $title;
                }
            }
        }

        return '';
    }

    /**
     * Find content element with fallback selectors
     */
    private function findContentElement(DOMDocument $dom): ?DOMElement {
        $contentSelectors = [
            '.widget-episodeBody',
            '.p-episode__body',
            '.episode-body',
            '.chapter-content',
            '.content',
            '.novel-body'
        ];

        foreach ($contentSelectors as $selector) {
            $contentElement = $this->querySelector($dom, $selector);
            if ($contentElement) {
                // Check if element has meaningful content
                $text = trim($contentElement->textContent);
                if (!empty($text) && strlen($text) > 50) {
                    return $contentElement;
                }
            }
        }

        return null;
    }

    /**
     * Extract chapter text content
     */
    private function extractChapterText(DOMElement $contentElement): string {
        $content = '';

        // Process each paragraph with furigana support
        $paragraphs = $this->querySelectorAll($contentElement->ownerDocument, '.widget-episodeBody p');

        if ($paragraphs->length > 0) {
            foreach ($paragraphs as $paragraph) {
                $paragraphText = $this->getTextContentWithFurigana($paragraph);
                $content .= $this->cleanTextWithFurigana($paragraphText) . "\n\n";
            }
        } else {
            // Fallback: get all text content with furigana
            $content = $this->getTextContentWithFurigana($contentElement);
            $content = $this->cleanTextWithFurigana($content);
        }

        // Clean up the content
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        return $content;
    }

    /**
     * Clean text while preserving furigana markup and paragraph structure
     */
    private function cleanTextWithFurigana(string $text): string {
        // Remove HTML entities first
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Clean up furigana markup spacing
        $text = preg_replace('/\s*\{\s*([^|]+)\s*\|\s*([^}]+)\s*\}\s*/', '{$1|$2}', $text);

        // Preserve paragraph breaks while cleaning up other whitespace
        // Split by paragraph breaks (double newlines), clean each paragraph, then rejoin
        $paragraphs = preg_split('/\n\s*\n/', $text);
        $cleanedParagraphs = [];

        foreach ($paragraphs as $paragraph) {
            // Clean whitespace within each paragraph (but preserve single newlines)
            $paragraph = preg_replace('/[ \t]+/', ' ', $paragraph); // Only collapse spaces and tabs
            $paragraph = preg_replace('/\n[ \t]*\n/', "\n", $paragraph); // Clean up line breaks with spaces
            $paragraph = trim($paragraph);

            if (!empty($paragraph)) {
                $cleanedParagraphs[] = $paragraph;
            }
        }

        // Rejoin paragraphs with double newlines
        $text = implode("\n\n", $cleanedParagraphs);

        return $text;
    }
}
