/**
 * Manual Novel Entry JavaScript
 * Handles manual novel creation form functionality
 */

class ManualNovelEntry {
    constructor() {
        this.form = document.getElementById('manual-novel-form');
        this.messageContainer = document.getElementById('message-container');
        
        this.initializeEventListeners();
    }

    initializeEventListeners() {
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.submitNovel();
            });
        }

        // Auto-translate synopsis if original is provided
        const originalSynopsis = document.getElementById('original-synopsis');
        const translatedSynopsis = document.getElementById('translated-synopsis');
        
        if (originalSynopsis && translatedSynopsis) {
            let translateTimeout;
            originalSynopsis.addEventListener('input', () => {
                clearTimeout(translateTimeout);
                translateTimeout = setTimeout(() => {
                    this.suggestTranslation();
                }, 2000); // Wait 2 seconds after user stops typing
            });
        }

        // Auto-translate title if original is provided
        const originalTitle = document.getElementById('original-title');
        const translatedTitle = document.getElementById('translated-title');
        
        if (originalTitle && translatedTitle) {
            let titleTranslateTimeout;
            originalTitle.addEventListener('input', () => {
                clearTimeout(titleTranslateTimeout);
                titleTranslateTimeout = setTimeout(() => {
                    this.suggestTitleTranslation();
                }, 1500); // Wait 1.5 seconds after user stops typing
            });
        }
    }

    async submitNovel() {
        const submitBtn = this.form.querySelector('button[type="submit"]');
        const originalText = submitBtn.innerHTML;
        
        try {
            // Show loading state
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating Novel...';
            
            // Collect form data
            const formData = {
                original_title: document.getElementById('original-title').value.trim(),
                translated_title: document.getElementById('translated-title').value.trim() || null,
                author: document.getElementById('author').value.trim() || null,
                source_url: document.getElementById('source-url').value.trim() || null,
                original_synopsis: document.getElementById('original-synopsis').value.trim(),
                translated_synopsis: document.getElementById('translated-synopsis').value.trim() || null,
                publication_date: document.getElementById('publication-date').value || null
            };

            // Validate required fields
            if (!formData.original_title) {
                throw new Error('Original title is required');
            }
            if (!formData.original_synopsis) {
                throw new Error('Original synopsis is required');
            }

            // Submit to API
            const response = await utils.makeApiRequest('api/novels.php', {
                method: 'POST',
                body: JSON.stringify({
                    ...formData,
                    platform: 'manual'
                })
            });

            if (response.success) {
                this.showMessage('success', 'Novel created successfully!', 
                    `Novel "${formData.original_title}" has been created. You can now add chapters to it.`);
                
                // Reset form
                this.form.reset();
                
                // Redirect to novel details or chapter entry after a delay
                setTimeout(() => {
                    const novelId = response.data.novel_id;
                    window.location.href = `manual-chapter.php?novel_id=${novelId}`;
                }, 2000);
            } else {
                throw new Error(response.error || 'Failed to create novel');
            }

        } catch (error) {
            console.error('Manual novel creation error:', error);
            this.showMessage('error', 'Creation Failed', error.message);
        } finally {
            // Restore button state
            submitBtn.disabled = false;
            submitBtn.innerHTML = originalText;
        }
    }

    async suggestTitleTranslation() {
        const originalTitle = document.getElementById('original-title').value.trim();
        const translatedTitle = document.getElementById('translated-title');
        
        if (!originalTitle || translatedTitle.value.trim()) {
            return; // Don't translate if original is empty or translated already has content
        }

        try {
            const response = await utils.makeApiRequest('api/translate.php', {
                method: 'POST',
                body: JSON.stringify({
                    text: originalTitle,
                    target_language: 'en',
                    type: 'title'
                })
            });

            if (response.success && response.translated_text) {
                translatedTitle.value = response.translated_text;
                translatedTitle.style.backgroundColor = '#e8f5e8'; // Light green to indicate auto-filled
                setTimeout(() => {
                    translatedTitle.style.backgroundColor = '';
                }, 2000);
            }
        } catch (error) {
            console.log('Title translation suggestion failed:', error.message);
            // Fail silently for suggestions
        }
    }

    async suggestTranslation() {
        const originalSynopsis = document.getElementById('original-synopsis').value.trim();
        const translatedSynopsis = document.getElementById('translated-synopsis');
        
        if (!originalSynopsis || translatedSynopsis.value.trim()) {
            return; // Don't translate if original is empty or translated already has content
        }

        try {
            const response = await utils.makeApiRequest('api/translate.php', {
                method: 'POST',
                body: JSON.stringify({
                    text: originalSynopsis,
                    target_language: 'en',
                    type: 'synopsis'
                })
            });

            if (response.success && response.translated_text) {
                translatedSynopsis.value = response.translated_text;
                translatedSynopsis.style.backgroundColor = '#e8f5e8'; // Light green to indicate auto-filled
                setTimeout(() => {
                    translatedSynopsis.style.backgroundColor = '';
                }, 3000);
            }
        } catch (error) {
            console.log('Synopsis translation suggestion failed:', error.message);
            // Fail silently for suggestions
        }
    }

    showMessage(type, title, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
        
        this.messageContainer.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${icon} me-2"></i>
                <strong>${title}</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        this.messageContainer.style.display = 'block';
        
        // Scroll to message
        this.messageContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ManualNovelEntry();
});
