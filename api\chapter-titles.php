<?php
/**
 * API Endpoint: Chapter Title Translation
 * PUT /api/chapter-titles.php - Translate chapter titles
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $novelManager = new NovelManager();
} catch (Exception $e) {
    logError('Failed to initialize NovelManager: ' . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'Database connection failed'
    ], 500);
}

try {
    switch ($method) {
        case 'PUT':
            handleTranslateChapterTitles($novelManager);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    logError('Chapter Titles API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    // Provide specific error messages
    $errorMessage = 'An error occurred while translating titles';
    $statusCode = 500;

    if (strpos($e->getMessage(), 'HTTP error: 429') !== false || strpos($e->getMessage(), 'quota') !== false) {
        $errorMessage = 'Translation service quota exceeded. Please wait and try again later.';
        $statusCode = 429;
    } elseif (strpos($e->getMessage(), 'HTTP error: 503') !== false || strpos($e->getMessage(), 'overloaded') !== false) {
        $errorMessage = 'Translation service is temporarily overloaded. Please try again in a few minutes.';
        $statusCode = 503;
    } elseif (strpos($e->getMessage(), 'timeout') !== false || strpos($e->getMessage(), 'timed out') !== false) {
        $errorMessage = 'Translation request timed out. Please try again.';
        $statusCode = 408;
    }

    jsonResponse([
        'success' => false,
        'error' => $errorMessage
    ], $statusCode);
}

/**
 * Handle PUT request - Translate chapter titles
 */
function handleTranslateChapterTitles($novelManager) {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $targetLanguage = isset($input['target_language']) ? sanitizeInput($input['target_language']) : DEFAULT_TARGET_LANGUAGE;
    $forceRetranslate = isset($input['force_retranslate']) ? (bool)$input['force_retranslate'] : false;

    // Support both single chapter and bulk operations
    $chapterNumbers = [];
    if (isset($input['chapter_number']) && is_numeric($input['chapter_number'])) {
        // Single chapter operation
        $chapterNumbers = [(int)$input['chapter_number']];
    } elseif (isset($input['chapter_numbers']) && is_array($input['chapter_numbers'])) {
        // Bulk operation
        $chapterNumbers = array_map('intval', $input['chapter_numbers']);
    } elseif (isset($input['translate_all']) && $input['translate_all'] === true) {
        // Translate all chapter titles for the novel
        $chapterNumbers = 'all';
    } else {
        jsonResponse(['error' => 'Either chapter_number, chapter_numbers array, or translate_all flag is required'], 400);
    }

    file_put_contents('debug.log', "Starting title translation - Novel ID: {$novelId}, Chapters: " . (is_array($chapterNumbers) ? implode(',', $chapterNumbers) : 'all') . ", Language: {$targetLanguage}, Force: " . ($forceRetranslate ? 'yes' : 'no') . "\n", FILE_APPEND);

    try {
        $result = $novelManager->translateChapterTitles($novelId, $chapterNumbers, $targetLanguage, $forceRetranslate);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ]);
        } else {
            // Determine appropriate HTTP status code based on error type
            $statusCode = 400;
            $errorMessage = $result['error'] ?? 'Unknown error';

            // Check for specific error types in bulk results
            if (isset($result['results']) && is_array($result['results'])) {
                foreach ($result['results'] as $chapterResult) {
                    if (!$chapterResult['success'] && isset($chapterResult['error'])) {
                        $chapterError = $chapterResult['error'];

                        if (strpos($chapterError, 'HTTP error: 503') !== false) {
                            $statusCode = 503;
                            $errorMessage = $chapterError;
                            break;
                        } elseif (strpos($chapterError, 'HTTP error: 429') !== false) {
                            $statusCode = 429;
                            $errorMessage = $chapterError;
                            break;
                        } elseif (strpos($chapterError, 'timeout') !== false) {
                            $statusCode = 408;
                            $errorMessage = $chapterError;
                            break;
                        }
                    }
                }
            }

            jsonResponse([
                'success' => false,
                'error' => $errorMessage
            ], $statusCode);
        }
    } catch (Exception $e) {
        file_put_contents('debug.log', "Title translation exception: " . $e->getMessage() . "\nTrace: " . $e->getTraceAsString() . "\n", FILE_APPEND);

        $statusCode = 500;
        $errorMessage = 'Title translation failed: ' . $e->getMessage();

        if (strpos($e->getMessage(), 'HTTP error: 503') !== false) {
            $statusCode = 503;
            $errorMessage = 'Translation service is temporarily overloaded. Please try again in a few minutes.';
        } elseif (strpos($e->getMessage(), 'HTTP error: 429') !== false) {
            $statusCode = 429;
            $errorMessage = 'Translation service quota exceeded. Please wait and try again later.';
        } elseif (strpos($e->getMessage(), 'timeout') !== false) {
            $statusCode = 408;
            $errorMessage = 'Translation request timed out. Please try again.';
        }

        jsonResponse([
            'success' => false,
            'error' => $errorMessage
        ], $statusCode);
    }
}
?>
