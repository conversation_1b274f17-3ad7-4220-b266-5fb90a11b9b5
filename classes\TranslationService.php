<?php
/**
 * Translation Service using Google Gemini AI
 * Novel Translation Application
 */

class TranslationService {
    private $deepSeekService;
    private $db;
    private $currentNovelId;
    private $chapterChunker;
    private $soundEffectsService;
    private $honorificService;

    // Legacy Gemini properties for backward compatibility
    private $apiKey;
    private $apiUrl;
    private $fallbackApiUrl;

    public function __construct() {
        // Initialize DeepSeek service as primary
        $this->deepSeekService = new DeepSeekTranslationService();

        // Keep Gemini configuration for fallback
        $this->apiKey = GEMINI_API_KEY;
        $this->apiUrl = GEMINI_API_URL;
        $this->fallbackApiUrl = GEMINI_FALLBACK_URL;

        $this->db = Database::getInstance();
        $this->currentNovelId = null;
        $this->chapterChunker = new ChapterChunker();
        $this->soundEffectsService = new SoundEffectsService();
        $this->honorificService = new HonorificService();
    }
    
    /**
     * Translate text using DeepSeek AI (with Gemini fallback)
     */
    public function translateText(string $text, string $targetLanguage = 'en', string $sourceLanguage = 'auto', array $context = []): array {
        $startTime = microtime(true);

        try {
            // Preprocess text for honorifics preservation only - NO sound effects processing
            $honorifics = [];
            $textToTranslate = $text;

            if (isset($context['type']) && $context['type'] === 'chapter') {
                // Process honorifics only - sound effects will be translated directly to English
                $honorificResult = $this->honorificService->preprocessTextForTranslation($text, $sourceLanguage);
                $textToTranslate = $honorificResult['text'];
                $honorifics = $honorificResult['honorifics'];

                // Add honorifics to context for prompt building
                $context['honorifics'] = $honorifics;
            } elseif (isset($context['type']) && in_array($context['type'], ['title', 'synopsis'])) {
                // Process honorifics for titles and synopsis too
                $honorificResult = $this->honorificService->preprocessTextForTranslation($textToTranslate, $sourceLanguage);
                $textToTranslate = $honorificResult['text'];
                $honorifics = $honorificResult['honorifics'];
                $context['honorifics'] = $honorifics;
            }

            file_put_contents('debug.log', "TranslationService: Using DeepSeek API for translation\n", FILE_APPEND);

            // Try DeepSeek first
            $result = $this->deepSeekService->translateText($textToTranslate, $targetLanguage, $sourceLanguage, $context);

            if ($result['success']) {
                file_put_contents('debug.log', "TranslationService: DeepSeek translation successful\n", FILE_APPEND);

                $translatedText = $result['translated_text'];

                // Validate formatting preservation before postprocessing
                $formattingValidation = $this->validateFormattingPreservation($textToTranslate, $translatedText);
                if (!$formattingValidation['valid']) {
                    file_put_contents('debug.log', "TranslationService: Formatting validation issues: " . implode(', ', $formattingValidation['issues']) . "\n", FILE_APPEND);
                }

                // Postprocess to restore honorifics only - NO sound effects processing
                if (!empty($honorifics)) {
                    // Restore honorifics only
                    $translatedText = $this->honorificService->postprocessTranslatedText($translatedText, $honorifics);
                }
                // Sound effects are now translated directly to English - no postprocessing needed

                // Clean up title translations to ensure single, clean result
                if (isset($context['type']) && $context['type'] === 'title') {
                    $translatedText = $this->cleanTitleTranslation($translatedText);

                    // Validate the cleaned result
                    $validation = $this->validateTitleTranslation($translatedText);
                    if (!$validation['is_valid']) {
                        file_put_contents('debug.log', "TranslationService: Title validation failed: " . implode(', ', $validation['issues']) . "\n", FILE_APPEND);
                    }
                }

                $executionTime = microtime(true) - $startTime;

                return [
                    'success' => true,
                    'original_text' => $text,
                    'translated_text' => $translatedText,
                    'source_language' => $sourceLanguage,
                    'target_language' => $targetLanguage,
                    'execution_time' => round($executionTime, 2),
                    'character_count' => mb_strlen($text),
                    'translated_count' => mb_strlen($translatedText),
                    'api_used' => 'deepseek',
                    'cache_hit_tokens' => $result['cache_hit_tokens'] ?? 0,
                    'cache_miss_tokens' => $result['cache_miss_tokens'] ?? 0,
                    'total_tokens' => $result['total_tokens'] ?? 0,
                    'cache_hit_rate' => $result['cache_hit_rate'] ?? 0.0,
                    'estimated_cost_savings' => $result['estimated_cost_savings'] ?? 0.0
                ];
            }

            // If DeepSeek fails, try Gemini as fallback
            file_put_contents('debug.log', "TranslationService: DeepSeek failed, trying Gemini fallback: " . $result['error'] . "\n", FILE_APPEND);

            return $this->translateTextWithGemini($text, $targetLanguage, $sourceLanguage, $context, []);

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            // Try Gemini as fallback
            file_put_contents('debug.log', "TranslationService: Exception with DeepSeek, trying Gemini fallback: " . $e->getMessage() . "\n", FILE_APPEND);

            try {
                return $this->translateTextWithGemini($text, $targetLanguage, $sourceLanguage, $context, []);
            } catch (Exception $fallbackException) {
                return [
                    'success' => false,
                    'error' => 'Both DeepSeek and Gemini failed. DeepSeek: ' . $e->getMessage() . '. Gemini: ' . $fallbackException->getMessage(),
                    'execution_time' => round(microtime(true) - $startTime, 2),
                    'original_text' => $text,
                    'target_language' => $targetLanguage
                ];
            }
        }
    }

    /**
     * Translate text using Gemini AI (fallback method)
     */
    private function translateTextWithGemini(string $text, string $targetLanguage = 'en', string $sourceLanguage = 'auto', array $context = [], array $soundEffects = []): array {
        $startTime = microtime(true);

        try {
            // Preprocess text for honorifics preservation only - NO sound effects processing
            $honorifics = $context['honorifics'] ?? [];
            $textToTranslate = $text;

            // Process honorifics if not already done
            if (empty($honorifics) && isset($context['type']) && in_array($context['type'], ['chapter', 'title', 'synopsis'])) {
                $honorificResult = $this->honorificService->preprocessTextForTranslation($textToTranslate, $sourceLanguage);
                $textToTranslate = $honorificResult['text'];
                $honorifics = $honorificResult['honorifics'];
                $context['honorifics'] = $honorifics;
            }

            // Prepare the prompt
            $prompt = $this->buildTranslationPrompt($textToTranslate, $targetLanguage, $sourceLanguage, $context);

            // Use more restrictive parameters for title translations
            $apiConfig = [];
            if (isset($context['type']) && $context['type'] === 'title') {
                $apiConfig = [
                    'temperature' => 0.1,    // Lower temperature for more focused responses
                    'topK' => 20,            // Reduced topK for less variety
                    'topP' => 0.8,           // Lower topP for more deterministic output
                    'maxOutputTokens' => 100 // Limit output length for titles
                ];
            }

            // Make API request
            $response = $this->makeApiRequest($prompt, $apiConfig);

            if (!$response['success']) {
                throw new Exception($response['error']);
            }

            $translatedText = $this->extractTranslationFromResponse($response['data']);

            // Postprocess to restore honorifics only - NO sound effects processing
            if (!empty($honorifics)) {
                // Restore honorifics only
                $translatedText = $this->honorificService->postprocessTranslatedText($translatedText, $honorifics);
            }
            // Sound effects are now translated directly to English - no postprocessing needed

            // Clean up title translations to ensure single, clean result
            if (isset($context['type']) && $context['type'] === 'title') {
                $translatedText = $this->cleanTitleTranslation($translatedText);

                // Validate the cleaned result
                $validation = $this->validateTitleTranslation($translatedText);
                if (!$validation['is_valid']) {
                    file_put_contents('debug.log', "TranslationService: Title validation failed: " . implode(', ', $validation['issues']) . "\n", FILE_APPEND);

                    // If validation fails and we have a very problematic result, try a simplified approach
                    if (count($validation['issues']) > 2 || strlen($translatedText) > 150) {
                        file_put_contents('debug.log', "TranslationService: Attempting simplified title translation\n", FILE_APPEND);

                        $simplifiedPrompt = "Translate this Japanese title to English. Return only the translated title, no explanations:\n\n" . $text;
                        $retryResponse = $this->makeApiRequest($simplifiedPrompt, [
                            'temperature' => 0.05,
                            'topK' => 10,
                            'topP' => 0.7,
                            'maxOutputTokens' => 50
                        ]);

                        if ($retryResponse['success']) {
                            $retryTranslation = $this->extractTranslationFromResponse($retryResponse['data']);
                            $retryTranslation = $this->cleanTitleTranslation($retryTranslation);

                            $retryValidation = $this->validateTitleTranslation($retryTranslation);
                            if ($retryValidation['is_valid'] || count($retryValidation['issues']) < count($validation['issues'])) {
                                $translatedText = $retryTranslation;
                                file_put_contents('debug.log', "TranslationService: Simplified approach successful\n", FILE_APPEND);
                            }
                        }
                    }
                }
            }

            $executionTime = microtime(true) - $startTime;

            return [
                'success' => true,
                'original_text' => $text,
                'translated_text' => $translatedText,
                'source_language' => $sourceLanguage,
                'target_language' => $targetLanguage,
                'execution_time' => round($executionTime, 2),
                'character_count' => mb_strlen($text),
                'translated_count' => mb_strlen($translatedText),
                'api_used' => 'gemini_fallback'
            ];

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2),
                'original_text' => $text,
                'api_used' => 'gemini_fallback'
            ];
        }
    }

    /**
     * Translate novel chapter with name consistency and chunking support
     */
    public function translateChapter(int $novelId, int $chapterId, string $targetLanguage = 'en'): array {
        try {
            // Set current novel ID for name extraction
            $this->currentNovelId = $novelId;

            file_put_contents('debug.log', "TranslationService: Starting chapter translation - Novel ID: {$novelId}, Chapter ID: {$chapterId}\n", FILE_APPEND);

            // Get chapter data
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE id = ? AND novel_id = ?",
                [$chapterId, $novelId]
            );

            if (!$chapter) {
                file_put_contents('debug.log', "TranslationService: Chapter not found - ID: {$chapterId}\n", FILE_APPEND);
                throw new Exception("Chapter not found");
            }

            // Check if chapter has chunks (created during save process)
            if ($this->chapterChunker->hasChunks($chapterId)) {
                file_put_contents('debug.log', "TranslationService: Chapter has chunks, using chunked translation\n", FILE_APPEND);
                return $this->translateChapterChunks($novelId, $chapterId, $targetLanguage);
            } else {
                file_put_contents('debug.log', "TranslationService: Chapter has no chunks, using regular translation\n", FILE_APPEND);
                return $this->translateChapterRegular($novelId, $chapterId, $targetLanguage);
            }

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Exception caught: " . $e->getMessage() . "\n", FILE_APPEND);
            file_put_contents('debug.log', "TranslationService: Exception trace: " . $e->getTraceAsString() . "\n", FILE_APPEND);

            // Update chapter status on error
            $this->db->update('chapters',
                ['translation_status' => 'error'],
                'id = ?',
                [$chapterId]
            );

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'chapter_id' => $chapterId
            ];
        }
    }



    /**
     * Translate chapter using chunks for large content
     */
    private function translateChapterChunks(int $novelId, int $chapterId, string $targetLanguage): array {
        try {
            // Get chapter data
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE id = ? AND novel_id = ?",
                [$chapterId, $novelId]
            );

            // Get name dictionary for consistency
            $nameDictionary = $this->getNameDictionary($novelId);
            file_put_contents('debug.log', "TranslationService: Name dictionary entries: " . count($nameDictionary) . "\n", FILE_APPEND);

            // Log detailed name dictionary for debugging
            if (!empty($nameDictionary)) {
                file_put_contents('debug.log', "TranslationService: Name dictionary details:\n", FILE_APPEND);
                foreach ($nameDictionary as $name) {
                    $targetName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
                    $source = $name['translation'] ? 'translation' : ($name['romanization'] ? 'romanization' : 'original');
                    file_put_contents('debug.log', "  - {$name['original_name']} → {$targetName} (using {$source})\n", FILE_APPEND);
                }
            } else {
                file_put_contents('debug.log', "TranslationService: No name dictionary entries found for novel {$novelId}\n", FILE_APPEND);
            }

            // Update chapter status
            $this->db->update('chapters',
                ['translation_status' => 'translating'],
                'id = ?',
                [$chapterId]
            );

            // Translate title first
            file_put_contents('debug.log', "TranslationService: Starting title translation\n", FILE_APPEND);
            $titleResult = $this->translateText(
                $chapter['original_title'],
                $targetLanguage,
                'auto',
                ['type' => 'title', 'names' => $nameDictionary]
            );

            if (!$titleResult['success']) {
                throw new Exception("Title translation failed: " . $titleResult['error']);
            }

            // Get all chunks for this chapter
            $chunks = $this->chapterChunker->getChapterChunks($chapterId);
            file_put_contents('debug.log', "TranslationService: Found " . count($chunks) . " chunks to translate\n", FILE_APPEND);

            $translatedChunks = [];
            $totalCharacters = 0;
            $totalExecutionTime = 0;

            // Translate each chunk with enhanced context preservation and incremental name extraction
            foreach ($chunks as $index => $chunk) {
                file_put_contents('debug.log', "TranslationService: Translating chunk {$chunk['chunk_number']}/{" . count($chunks) . "}\n", FILE_APPEND);

                // Update chunk status
                $this->db->update('chapter_chunks',
                    ['translation_status' => 'translating'],
                    'id = ?',
                    [$chunk['id']]
                );

                // Prepare enhanced context for chunk translation
                $chunkContext = [
                    'type' => 'chapter',
                    'names' => $nameDictionary,
                    'chunk_number' => $chunk['chunk_number'],
                    'total_chunks' => count($chunks),
                    'is_first_chunk' => $index === 0,
                    'is_last_chunk' => $index === count($chunks) - 1
                ];

                // Add enhanced context from adjacent chunks for better continuity
                if ($index > 0) {
                    $prevChunk = $chunks[$index - 1];
                    $chunkContext['previous_context'] = mb_substr($prevChunk['original_content'], -300);

                    // Add character names from previous chunk
                    if (isset($prevChunk['previous_characters'])) {
                        $chunkContext['previous_characters'] = $prevChunk['previous_characters'];
                    }
                }
                if ($index < count($chunks) - 1) {
                    $nextChunk = $chunks[$index + 1];
                    $chunkContext['next_context'] = mb_substr($nextChunk['original_content'], 0, 300);
                }

                // Add previously translated content for consistency (increased context)
                if (!empty($translatedChunks)) {
                    $chunkContext['previous_translation'] = mb_substr(end($translatedChunks), -200);
                }

                // Add narrative and dialogue context if available
                if (isset($chunk['narrative_context'])) {
                    $chunkContext['narrative_context'] = $chunk['narrative_context'];
                }
                if (isset($chunk['dialogue_context'])) {
                    $chunkContext['dialogue_context'] = $chunk['dialogue_context'];
                }
                if (isset($chunk['content_analysis'])) {
                    $chunkContext['content_analysis'] = $chunk['content_analysis'];
                }

                // Translate chunk content with enhanced context
                $chunkResult = $this->translateText(
                    $chunk['original_content'],
                    $targetLanguage,
                    'auto',
                    $chunkContext
                );

                if (!$chunkResult['success']) {
                    // Mark chunk as error and try adaptive recovery
                    $this->db->update('chapter_chunks',
                        ['translation_status' => 'error'],
                        'id = ?',
                        [$chunk['id']]
                    );

                    // Try to recover with smaller chunk if timeout
                    if (strpos($chunkResult['error'], 'timeout') !== false || strpos($chunkResult['error'], 'too large') !== false) {
                        file_put_contents('debug.log', "TranslationService: Attempting chunk recovery for timeout\n", FILE_APPEND);
                        $recoveryResult = $this->attemptChunkRecovery($chunk, $targetLanguage, $chunkContext);

                        if ($recoveryResult['success']) {
                            $chunkResult = $recoveryResult;
                            file_put_contents('debug.log', "TranslationService: Chunk recovery successful\n", FILE_APPEND);
                        } else {
                            throw new Exception("Chunk {$chunk['chunk_number']} translation failed: " . $chunkResult['error']);
                        }
                    } else {
                        throw new Exception("Chunk {$chunk['chunk_number']} translation failed: " . $chunkResult['error']);
                    }
                }

                // ENHANCED: Extract names from this chunk for incremental name discovery
                file_put_contents('debug.log', "TranslationService: Extracting names from chunk {$chunk['chunk_number']}\n", FILE_APPEND);

                // Extract new names from this chunk's original and translated content
                $furiganaService = new FuriganaService();
                $chunkNameExtractionContent = !empty($chunk['original_content_with_furigana'])
                    ? $furiganaService->removeFurigana($chunk['original_content_with_furigana'])
                    : $chunk['original_content'];

                $chunkNewNames = $this->extractNames($chunkNameExtractionContent, $chunkResult['translated_text']);
                file_put_contents('debug.log', "TranslationService: Found " . count($chunkNewNames) . " new names in chunk {$chunk['chunk_number']}\n", FILE_APPEND);

                // Update name dictionary incrementally with names from this chunk
                if (!empty($chunkNewNames)) {
                    $this->updateNameDictionary($novelId, $chunkNewNames, $chapter['chapter_number']);
                    file_put_contents('debug.log', "TranslationService: Updated name dictionary with chunk {$chunk['chunk_number']} names\n", FILE_APPEND);

                    // Refresh name dictionary for subsequent chunks to include newly discovered names
                    $nameDictionary = $this->getNameDictionary($novelId);
                    file_put_contents('debug.log', "TranslationService: Refreshed name dictionary, now contains " . count($nameDictionary) . " entries\n", FILE_APPEND);
                }

                // Update chunk with translation
                $this->db->update('chapter_chunks', [
                    'translated_content' => $chunkResult['translated_text'],
                    'translation_status' => 'completed',
                    'translation_date' => date('Y-m-d H:i:s')
                ], 'id = ?', [$chunk['id']]);

                $translatedChunks[] = $chunkResult['translated_text'];
                $totalCharacters += $chunkResult['character_count'];
                $totalExecutionTime += $chunkResult['execution_time'];

                // Adaptive delay based on chunk size and performance
                $delay = $this->calculateAdaptiveDelay($chunk, $chunkResult);
                if ($delay > 0) {
                    usleep($delay * 1000000); // Convert to microseconds
                }
            }

            // Reassemble translated content with enhanced formatting preservation and validation
            $reassemblyResult = $this->reassembleChunksWithValidation($translatedChunks, $chunks, $chapterId);

            if (!$reassemblyResult['success']) {
                throw new Exception("Content reassembly failed: " . $reassemblyResult['error']);
            }

            file_put_contents('debug.log', "TranslationService: All chunks translated and reassembled successfully\n", FILE_APPEND);

            return $this->finalizeChapterTranslation($novelId, $chapterId, $chapter, $titleResult, $reassemblyResult['content'], $totalExecutionTime);

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Chunked translation failed: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * Reassemble translated chunks with comprehensive validation
     */
    private function reassembleChunksWithValidation(array $translatedChunks, array $originalChunks, int $chapterId): array {
        try {
            // Pre-reassembly validation
            $preValidation = $this->validateChunksBeforeReassembly($translatedChunks, $originalChunks);
            if (!$preValidation['valid']) {
                return [
                    'success' => false,
                    'error' => 'Pre-reassembly validation failed: ' . $preValidation['error']
                ];
            }

            $debugMode = $this->isDebugModeEnabled();
            $reassembledContent = [];

            file_put_contents('debug.log', "TranslationService: Reassembling " . count($translatedChunks) . " chunks with enhanced validation\n", FILE_APPEND);

            for ($i = 0; $i < count($translatedChunks); $i++) {
                $translatedChunk = $translatedChunks[$i];
                $originalChunk = $originalChunks[$i] ?? null;

                // Add chunk marker if enabled (for all chunks, including the first one)
                if ($debugMode) {
                    $chunkMarker = $this->createChunkBoundaryMarker($i + 1, count($translatedChunks));
                    $reassembledContent[] = $chunkMarker;
                }

                // Enhanced chunk cleaning with validation
                $cleanedChunk = $this->cleanChunkForReassemblyEnhanced($translatedChunk, $originalChunk, $i);

                if (!empty($cleanedChunk)) {
                    $reassembledContent[] = $cleanedChunk;
                } else {
                    file_put_contents('debug.log', "TranslationService: Warning - Chunk " . ($i + 1) . " resulted in empty content after cleaning\n", FILE_APPEND);
                }
            }

            // Join chunks with proper spacing
            $result = implode("\n\n", array_filter($reassembledContent));

            // Enhanced final cleanup with validation
            $result = $this->finalizeContentFormattingEnhanced($result);

            // Post-reassembly validation
            $postValidation = $this->validateReassembledContent($result, $translatedChunks, $originalChunks);
            if (!$postValidation['valid']) {
                return [
                    'success' => false,
                    'error' => 'Post-reassembly validation failed: ' . $postValidation['error'],
                    'validation_details' => $postValidation
                ];
            }

            file_put_contents('debug.log', "TranslationService: Content reassembly complete and validated, final length: " . mb_strlen($result) . " characters\n", FILE_APPEND);

            return [
                'success' => true,
                'content' => $result,
                'validation_details' => [
                    'pre_validation' => $preValidation,
                    'post_validation' => $postValidation
                ]
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Reassembly exception: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Reassemble translated chunks while preserving original formatting (legacy method)
     */
    private function reassembleChunksWithFormatting(array $translatedChunks, array $originalChunks, int $chapterId): string {
        $result = $this->reassembleChunksWithValidation($translatedChunks, $originalChunks, $chapterId);

        if (!$result['success']) {
            // Fallback to basic reassembly if validation fails
            file_put_contents('debug.log', "TranslationService: Validation failed, using fallback reassembly: " . $result['error'] . "\n", FILE_APPEND);
            return $this->basicReassembly($translatedChunks);
        }

        return $result['content'];
    }

    /**
     * Check if debug mode is enabled for chunk markers
     */
    private function isDebugModeEnabled(): bool {
        // Check user preferences or configuration
        try {
            $debugSetting = $this->db->fetchOne(
                "SELECT preference_value FROM user_preferences WHERE preference_key = 'show_chunk_markers'",
                []
            );
            return $debugSetting && $debugSetting['preference_value'] === 'true';
        } catch (Exception $e) {
            // Default to false if preference doesn't exist
            return false;
        }
    }

    /**
     * Create a chunk boundary marker for debug purposes
     */
    private function createChunkBoundaryMarker(int $chunkNumber, int $totalChunks): string {
        return "<!-- CHUNK_BOUNDARY: {$chunkNumber}/{$totalChunks} -->";
    }

    /**
     * Validate chunks before reassembly
     */
    private function validateChunksBeforeReassembly(array $translatedChunks, array $originalChunks): array {
        $errors = [];

        // Check if we have the same number of translated and original chunks
        if (count($translatedChunks) !== count($originalChunks)) {
            $errors[] = "Mismatch in chunk count: " . count($translatedChunks) . " translated vs " . count($originalChunks) . " original";
        }

        // Check for empty translated chunks
        foreach ($translatedChunks as $index => $chunk) {
            if (empty(trim($chunk))) {
                $errors[] = "Translated chunk " . ($index + 1) . " is empty";
            }
        }

        // Check for reasonable length ratios (translated should be within 50%-300% of original)
        for ($i = 0; $i < min(count($translatedChunks), count($originalChunks)); $i++) {
            $originalLength = mb_strlen($originalChunks[$i]['original_content'] ?? '');
            $translatedLength = mb_strlen($translatedChunks[$i]);

            if ($originalLength > 0) {
                $ratio = $translatedLength / $originalLength;
                if ($ratio < 0.3 || $ratio > 4.0) {
                    $errors[] = "Chunk " . ($i + 1) . " has suspicious length ratio: {$ratio} (original: {$originalLength}, translated: {$translatedLength})";
                }
            }
        }

        return [
            'valid' => empty($errors),
            'error' => implode('; ', $errors),
            'chunk_count_original' => count($originalChunks),
            'chunk_count_translated' => count($translatedChunks)
        ];
    }

    /**
     * Enhanced chunk cleaning with validation
     */
    private function cleanChunkForReassemblyEnhanced(string $translatedChunk, ?array $originalChunk, int $chunkIndex): string {
        // Remove any leading/trailing whitespace but preserve internal formatting
        $cleaned = trim($translatedChunk);

        if (empty($cleaned)) {
            file_put_contents('debug.log', "TranslationService: Warning - Chunk {$chunkIndex} is empty after initial trim\n", FILE_APPEND);
            return '';
        }

        // Preserve sound effects markers if they exist
        $soundEffectMarkers = [];
        if (preg_match_all('/【SOUND_EFFECT:SE_\d+】/', $cleaned, $matches)) {
            $soundEffectMarkers = $matches[0];
        }

        // Ensure proper paragraph structure is maintained
        // Replace multiple consecutive newlines with double newlines (paragraph breaks)
        $cleaned = preg_replace('/\n{3,}/', "\n\n", $cleaned);

        // Ensure single newlines within paragraphs are preserved
        // but don't create unnecessary paragraph breaks
        $cleaned = preg_replace('/\n\s*\n/', "\n\n", $cleaned);

        // Remove any translation artifacts or unwanted formatting
        $cleaned = $this->removeTranslationArtifacts($cleaned);

        // Validate that sound effect markers are preserved
        if (!empty($soundEffectMarkers)) {
            foreach ($soundEffectMarkers as $marker) {
                if (strpos($cleaned, $marker) === false) {
                    file_put_contents('debug.log', "TranslationService: Warning - Sound effect marker {$marker} lost in chunk {$chunkIndex}\n", FILE_APPEND);
                }
            }
        }

        return $cleaned;
    }

    /**
     * Remove translation artifacts and unwanted formatting
     */
    private function removeTranslationArtifacts(string $content): string {
        // Remove common translation artifacts
        $artifacts = [
            '/\*\*[^*]*\*\*/', // Bold markdown
            '/\*[^*]*\*/', // Italic markdown
            '/\[Note:.*?\]/i', // Translation notes
            '/\(Note:.*?\)/i', // Translation notes in parentheses
            '/\[TL:.*?\]/i', // Translator notes
            '/\[T\/N:.*?\]/i', // Translator notes
            '/Here are.*?options?:?/i', // Multiple option indicators
            '/More literal:?/i', // Literal translation indicators
            '/Alternative:?/i', // Alternative translation indicators
        ];

        foreach ($artifacts as $pattern) {
            $content = preg_replace($pattern, '', $content);
        }

        // FIXED: Clean up extra whitespace created by artifact removal
        // Only target horizontal whitespace (spaces and tabs), not newlines
        $content = preg_replace('/[ \t]+/', ' ', $content);
        $content = preg_replace('/\n\s*\n\s*\n/', "\n\n", $content);

        return trim($content);
    }

    /**
     * Clean up individual chunk for reassembly (legacy method)
     */
    private function cleanChunkForReassembly(string $translatedChunk, ?array $originalChunk): string {
        return $this->cleanChunkForReassemblyEnhanced($translatedChunk, $originalChunk, 0);
    }

    /**
     * Validate reassembled content
     */
    private function validateReassembledContent(string $reassembledContent, array $translatedChunks, array $originalChunks): array {
        $errors = [];
        $warnings = [];

        // Check for minimum content length
        $contentLength = mb_strlen($reassembledContent);
        if ($contentLength < 100) {
            $errors[] = "Reassembled content too short: {$contentLength} characters";
        }

        // Calculate expected length range based on original chunks
        $totalOriginalLength = 0;
        foreach ($originalChunks as $chunk) {
            $totalOriginalLength += mb_strlen($chunk['original_content'] ?? '');
        }

        // Expected translated length should be within reasonable bounds
        if ($totalOriginalLength > 0) {
            $lengthRatio = $contentLength / $totalOriginalLength;
            if ($lengthRatio < 0.3) {
                $errors[] = "Reassembled content significantly shorter than expected (ratio: {$lengthRatio})";
            } elseif ($lengthRatio > 4.0) {
                $warnings[] = "Reassembled content significantly longer than expected (ratio: {$lengthRatio})";
            }
        }

        // Check for proper paragraph structure
        $paragraphCount = substr_count($reassembledContent, "\n\n") + 1;
        if ($paragraphCount < 2 && $contentLength > 1000) {
            $warnings[] = "Long content with very few paragraphs may indicate formatting issues";
        }

        // Check for sound effect preservation
        $originalSoundEffects = 0;
        $reassembledSoundEffects = 0;

        foreach ($originalChunks as $chunk) {
            $originalSoundEffects += preg_match_all('/【SOUND_EFFECT:SE_\d+】/', $chunk['original_content'] ?? '', $matches);
        }
        $reassembledSoundEffects = preg_match_all('/【SOUND_EFFECT:SE_\d+】/', $reassembledContent, $matches);

        if ($originalSoundEffects > 0 && $reassembledSoundEffects !== $originalSoundEffects) {
            $warnings[] = "Sound effect count mismatch: original {$originalSoundEffects}, reassembled {$reassembledSoundEffects}";
        }

        // Check for chunk boundary artifacts
        if (preg_match('/\[CHUNK_\d+\]/', $reassembledContent)) {
            $errors[] = "Chunk boundary artifacts found in final content";
        }

        return [
            'valid' => empty($errors),
            'error' => implode('; ', $errors),
            'warnings' => $warnings,
            'content_length' => $contentLength,
            'original_length' => $totalOriginalLength,
            'length_ratio' => $totalOriginalLength > 0 ? $contentLength / $totalOriginalLength : 0,
            'paragraph_count' => $paragraphCount
        ];
    }

    /**
     * Enhanced final formatting cleanup with dialogue preservation
     */
    private function finalizeContentFormattingEnhanced(string $content): string {
        // Remove any excessive whitespace at the beginning or end
        $content = trim($content);

        // Detect if content contains dialogue for special handling
        $hasDialogue = $this->detectDialogueContent($content);

        // MINIMAL formatting cleanup to preserve original structure
        // Only remove truly excessive whitespace (4+ consecutive newlines)
        $content = preg_replace('/\n{4,}/', "\n\n\n", $content);

        // Remove trailing spaces at the end of lines only
        $content = preg_replace('/[ \t]+$/m', '', $content);

        // Fix spacing around sound effect markers only
        $content = preg_replace('/\s*(【SOUND_EFFECT:SE_\d+】)\s*/', ' $1 ', $content);

        // VERY conservative cleanup - only remove excessive spaces within lines
        $content = preg_replace('/[ \t]{3,}/', '  ', $content); // Max 2 spaces

        // Preserve all original line breaks and paragraph structure
        // Do NOT modify \n or \n\n patterns - they should remain as translated

        return trim($content);
    }

    /**
     * Detect if content contains dialogue for special formatting handling
     */
    private function detectDialogueContent(string $content): bool {
        // Check for various dialogue indicators
        $dialoguePatterns = [
            '/[「」『』]/',                    // Japanese quotation marks
            '/["\'"][^"\']*["\'"]/',          // Western quotation marks with content
            '/(said|asked|replied|whispered|shouted|muttered|exclaimed)/i', // Dialogue tags
            '/[「"\'"][^「"\']*[」"\'"][^「"\']*[「"\'"]/', // Conversation pattern
        ];

        foreach ($dialoguePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate formatting preservation in translated content
     */
    private function validateFormattingPreservation(string $originalText, string $translatedText): array {
        $issues = [];

        // Check paragraph structure preservation
        $originalParagraphs = count(preg_split('/\n\s*\n/', $originalText));
        $translatedParagraphs = count(preg_split('/\n\s*\n/', $translatedText));

        if (abs($originalParagraphs - $translatedParagraphs) > 2) {
            $issues[] = "Paragraph structure significantly changed: {$originalParagraphs} → {$translatedParagraphs}";
        }

        // Check dialogue preservation (updated to expect preserved Japanese/Chinese punctuation)
        $originalDialogueCount = preg_match_all('/[「」『』"\'"]/', $originalText);
        $translatedDialogueCount = preg_match_all('/[「」『』"\'"]/', $translatedText);

        if ($originalDialogueCount > 0 && $translatedDialogueCount === 0) {
            $issues[] = "Dialogue quotation marks were lost in translation";
        }

        // Check punctuation preservation (more lenient)
        $originalPunctuation = preg_match_all('/[。！？!?]/', $originalText);
        $translatedPunctuation = preg_match_all('/[.!?]/', $translatedText);

        if ($originalPunctuation > 0 && abs($originalPunctuation - $translatedPunctuation) > ($originalPunctuation * 0.5)) {
            $issues[] = "Significant punctuation loss detected";
        }

        // Check line break preservation (approximate)
        $originalLineBreaks = substr_count($originalText, "\n");
        $translatedLineBreaks = substr_count($translatedText, "\n");

        if ($originalLineBreaks > 2 && abs($originalLineBreaks - $translatedLineBreaks) > ($originalLineBreaks * 0.5)) {
            $issues[] = "Line break structure significantly altered";
        }

        return [
            'valid' => empty($issues),
            'issues' => $issues,
            'stats' => [
                'original_paragraphs' => $originalParagraphs,
                'translated_paragraphs' => $translatedParagraphs,
                'original_dialogue_marks' => $originalDialogueCount,
                'translated_dialogue_marks' => $translatedDialogueCount,
                'original_punctuation' => $originalPunctuation,
                'translated_punctuation' => $translatedPunctuation,
                'original_line_breaks' => $originalLineBreaks,
                'translated_line_breaks' => $translatedLineBreaks
            ]
        ];
    }

    /**
     * Basic reassembly fallback method with improved formatting preservation
     */
    public function basicReassembly(array $translatedChunks): string {
        $cleanedChunks = [];

        foreach ($translatedChunks as $chunk) {
            // Only trim excessive whitespace, preserve internal formatting
            $cleaned = preg_replace('/^\s+|\s+$/', '', $chunk); // Trim start/end only
            if (!empty($cleaned)) {
                $cleanedChunks[] = $cleaned;
            }
        }

        // Join chunks with double newlines to maintain paragraph separation
        $result = implode("\n\n", $cleanedChunks);

        // Apply minimal formatting cleanup
        return $this->finalizeContentFormattingEnhanced($result);
    }

    /**
     * Final formatting cleanup for the complete content (legacy method)
     */
    private function finalizeContentFormatting(string $content): string {
        return $this->finalizeContentFormattingEnhanced($content);
    }

    /**
     * Reassemble recovery pieces with proper formatting
     */
    private function reassembleRecoveryPieces(array $translatedPieces): string {
        $cleanedPieces = [];

        foreach ($translatedPieces as $piece) {
            $cleaned = $this->cleanChunkForReassembly($piece, null);
            if (!empty($cleaned)) {
                $cleanedPieces[] = $cleaned;
            }
        }

        $result = implode("\n\n", $cleanedPieces);
        return $this->finalizeContentFormatting($result);
    }

    /**
     * Translate chapter using regular method (for smaller content)
     */
    private function translateChapterRegular(int $novelId, int $chapterId, string $targetLanguage): array {
        // Get chapter data
        $chapter = $this->db->fetchOne(
            "SELECT * FROM chapters WHERE id = ? AND novel_id = ?",
            [$chapterId, $novelId]
        );

        // Process furigana if present
        $furiganaService = new FuriganaService();
        $contentForTranslation = $chapter['original_content'];

        // Check if we have furigana content
        if (!empty($chapter['original_content_with_furigana'])) {
            file_put_contents('debug.log', "TranslationService: Processing furigana content\n", FILE_APPEND);
            $contentForTranslation = $furiganaService->processForTranslation(
                $chapter['original_content_with_furigana'],
                $novelId
            );
        }

        file_put_contents('debug.log', "TranslationService: Chapter found - Title: " . ($chapter['original_title'] ?? 'NULL') . "\n", FILE_APPEND);
        file_put_contents('debug.log', "TranslationService: Chapter content length: " . strlen($chapter['original_content'] ?? '') . "\n", FILE_APPEND);

        if (empty($chapter['original_content'])) {
            throw new Exception("Chapter content is empty. Please save the chapter content first.");
        }

        // Get name dictionary for consistency
        $nameDictionary = $this->getNameDictionary($novelId);
        file_put_contents('debug.log', "TranslationService: Name dictionary entries: " . count($nameDictionary) . "\n", FILE_APPEND);

        // Update chapter status
        $this->db->update('chapters',
            ['translation_status' => 'translating'],
            'id = ?',
            [$chapterId]
        );

        file_put_contents('debug.log', "TranslationService: Starting title translation\n", FILE_APPEND);
        // Translate title
        $titleResult = $this->translateText(
            $chapter['original_title'],
            $targetLanguage,
            'auto',
            ['type' => 'title', 'names' => $nameDictionary]
        );

        file_put_contents('debug.log', "TranslationService: Title result: " . json_encode(['success' => $titleResult['success'], 'error' => $titleResult['error'] ?? 'none']) . "\n", FILE_APPEND);

        file_put_contents('debug.log', "TranslationService: Starting content translation\n", FILE_APPEND);
        // Translate content (use processed furigana content if available)
        $contentResult = $this->translateText(
            $contentForTranslation,
            $targetLanguage,
            'auto',
            ['type' => 'chapter', 'names' => $nameDictionary]
        );

        file_put_contents('debug.log', "TranslationService: Content result: " . json_encode(['success' => $contentResult['success'], 'error' => $contentResult['error'] ?? 'none']) . "\n", FILE_APPEND);

        if (!$titleResult['success'] || !$contentResult['success']) {
            // Create a more user-friendly error message
            $titleError = $titleResult['error'] ?? 'OK';
            $contentError = $contentResult['error'] ?? 'OK';

            // If content translation failed due to timeout/size, suggest re-saving the chapter to create chunks
            if (!$contentResult['success'] && $this->isTimeoutOrSizeError($contentError)) {
                file_put_contents('debug.log', "TranslationService: Content timeout detected - chapter may need to be re-saved to create chunks\n", FILE_APPEND);
            }

            // Update chapter status on error
            $this->db->update('chapters',
                ['translation_status' => 'error'],
                'id = ?',
                [$chapterId]
            );

            // Check for specific error types to provide better messages
            if (strpos($titleError, 'HTTP error: 503') !== false || strpos($contentError, 'HTTP error: 503') !== false) {
                $errorMsg = "Translation service is temporarily overloaded. Please try again in a few minutes.";
            } elseif (strpos($titleError, 'HTTP error: 429') !== false || strpos($contentError, 'HTTP error: 429') !== false) {
                $errorMsg = "Translation service quota exceeded. Please wait and try again later.";
            } elseif ($this->isTimeoutOrSizeError($titleError) || $this->isTimeoutOrSizeError($contentError)) {
                $errorMsg = "Translation request timed out. The chapter content is too large for a single request. The system will automatically split large chapters into smaller chunks on the next attempt.";
            } else {
                // Fallback to detailed error for debugging
                $errorMsg = "Translation failed - Title: " . $titleError . ", Content: " . $contentError;
            }

            file_put_contents('debug.log', "TranslationService: " . $errorMsg . "\n", FILE_APPEND);
            throw new Exception($errorMsg);
        }

        return $this->finalizeChapterTranslation($novelId, $chapterId, $chapter, $titleResult, $contentResult['translated_text'], $contentResult['execution_time']);
    }

    /**
     * Check if error is related to timeout or content size
     */
    private function isTimeoutOrSizeError(string $error): bool {
        $timeoutIndicators = [
            'timeout',
            'timed out',
            'too large',
            'content too large',
            'Operation timed out',
            'cURL error',
            'Request timeout',
            'Content length exceeds'
        ];

        foreach ($timeoutIndicators as $indicator) {
            if (stripos($error, $indicator) !== false) {
                return true;
            }
        }

        return false;
    }



    /**
     * Attempt to recover from chunk translation failure by splitting further
     */
    private function attemptChunkRecovery(array $chunk, string $targetLanguage, array $context): array {
        try {
            file_put_contents('debug.log', "TranslationService: Attempting chunk recovery for chunk {$chunk['chunk_number']}\n", FILE_APPEND);

            // Split the failed chunk into smaller pieces
            $content = $chunk['original_content'];
            $pieces = $this->splitChunkForRecovery($content);

            if (count($pieces) <= 1) {
                return ['success' => false, 'error' => 'Cannot split chunk further'];
            }

            $translatedPieces = [];
            $totalTime = 0;

            foreach ($pieces as $pieceIndex => $piece) {
                $pieceContext = $context;
                $pieceContext['recovery_piece'] = $pieceIndex + 1;
                $pieceContext['total_recovery_pieces'] = count($pieces);

                $pieceResult = $this->translateText($piece, $targetLanguage, 'auto', $pieceContext);

                if (!$pieceResult['success']) {
                    return ['success' => false, 'error' => 'Recovery piece failed: ' . $pieceResult['error']];
                }

                $translatedPieces[] = $pieceResult['translated_text'];
                $totalTime += $pieceResult['execution_time'];

                // Small delay between recovery pieces
                usleep(250000); // 0.25 second
            }

            // Reassemble recovery pieces with proper formatting
            $reassembledText = $this->reassembleRecoveryPieces($translatedPieces);

            return [
                'success' => true,
                'translated_text' => $reassembledText,
                'execution_time' => $totalTime,
                'character_count' => mb_strlen($reassembledText),
                'recovery_pieces' => count($pieces)
            ];

        } catch (Exception $e) {
            return ['success' => false, 'error' => 'Recovery failed: ' . $e->getMessage()];
        }
    }

    /**
     * Split chunk content for recovery attempts
     */
    private function splitChunkForRecovery(string $content): array {
        $maxPieceSize = 5000; // Smaller pieces for recovery
        $pieces = [];

        // Try paragraph splitting first
        $paragraphs = preg_split('/\n\s*\n/', $content, -1, PREG_SPLIT_NO_EMPTY);

        if (count($paragraphs) > 1) {
            $currentPiece = '';
            foreach ($paragraphs as $paragraph) {
                if (mb_strlen($currentPiece . $paragraph) > $maxPieceSize && !empty($currentPiece)) {
                    $pieces[] = trim($currentPiece);
                    $currentPiece = $paragraph;
                } else {
                    $currentPiece .= ($currentPiece ? "\n\n" : '') . $paragraph;
                }
            }
            if (!empty($currentPiece)) {
                $pieces[] = trim($currentPiece);
            }
        } else {
            // Fallback to sentence splitting
            $sentences = preg_split('/(?<=[。！？])\s*/', $content, -1, PREG_SPLIT_NO_EMPTY);
            $currentPiece = '';
            foreach ($sentences as $sentence) {
                if (mb_strlen($currentPiece . $sentence) > $maxPieceSize && !empty($currentPiece)) {
                    $pieces[] = trim($currentPiece);
                    $currentPiece = $sentence;
                } else {
                    $currentPiece .= ($currentPiece ? ' ' : '') . $sentence;
                }
            }
            if (!empty($currentPiece)) {
                $pieces[] = trim($currentPiece);
            }
        }

        return $pieces;
    }

    /**
     * Calculate adaptive delay between chunks based on performance
     */
    private function calculateAdaptiveDelay(array $chunk, array $result): float {
        $baseDelay = 0.5; // Base 0.5 second delay

        // Adjust based on chunk size
        $sizeMultiplier = min(2.0, $chunk['character_count'] / 10000);

        // Adjust based on translation time
        $timeMultiplier = 1.0;
        if ($result['execution_time'] > 120) { // If took more than 2 minutes
            $timeMultiplier = 1.5;
        } elseif ($result['execution_time'] < 30) { // If very fast
            $timeMultiplier = 0.5;
        }

        return $baseDelay * $sizeMultiplier * $timeMultiplier;
    }

    /**
     * Finalize chapter translation (common for both regular and chunked)
     */
    private function finalizeChapterTranslation(int $novelId, int $chapterId, array $chapter, array $titleResult, string $translatedContent, float $executionTime): array {
        try {
            // Check if this chapter was translated using chunks
            $hasChunks = $this->chapterChunker->hasChunks($chapterId);

            if (!$hasChunks) {
                // Only extract names for non-chunked translations (chunked translations already extracted names incrementally)
                file_put_contents('debug.log', "TranslationService: Starting name extraction for non-chunked translation\n", FILE_APPEND);

                // Extract new names from original content (use base content without furigana for name extraction)
                $furiganaService = new FuriganaService();
                $nameExtractionContent = !empty($chapter['original_content_with_furigana'])
                    ? $furiganaService->removeFurigana($chapter['original_content_with_furigana'])
                    : $chapter['original_content'];
                $newNames = $this->extractNames($nameExtractionContent, $translatedContent);
                file_put_contents('debug.log', "TranslationService: Found " . count($newNames) . " new names\n", FILE_APPEND);

                $this->updateNameDictionary($novelId, $newNames, $chapter['chapter_number']);
                file_put_contents('debug.log', "TranslationService: Name dictionary updated\n", FILE_APPEND);
            } else {
                file_put_contents('debug.log', "TranslationService: Skipping name extraction for chunked translation (already done incrementally)\n", FILE_APPEND);
            }

            // Update chapter with translation
            $updateData = [
                'translated_title' => $titleResult['translated_text'],
                'translated_content' => $translatedContent,
                'translation_status' => 'completed',
                'translation_date' => date('Y-m-d H:i:s'),
                'word_count' => str_word_count($translatedContent)
            ];

            file_put_contents('debug.log', "TranslationService: Updating chapter in database\n", FILE_APPEND);
            $this->db->update('chapters', $updateData, 'id = ?', [$chapterId]);
            file_put_contents('debug.log', "TranslationService: Chapter updated successfully\n", FILE_APPEND);

            // Log translation
            file_put_contents('debug.log', "TranslationService: Logging translation\n", FILE_APPEND);
            $this->logTranslation($novelId, $chapterId, 'chapter', [
                'success' => true,
                'translated_text' => $translatedContent,
                'execution_time' => $executionTime,
                'character_count' => mb_strlen($translatedContent),
                'translated_count' => mb_strlen($translatedContent)
            ]);
            file_put_contents('debug.log', "TranslationService: Translation logged successfully\n", FILE_APPEND);

            file_put_contents('debug.log', "TranslationService: Preparing successful return\n", FILE_APPEND);
            return [
                'success' => true,
                'chapter_id' => $chapterId,
                'title_translation' => $titleResult,
                'content_translation' => [
                    'translated_text' => $translatedContent,
                    'execution_time' => $executionTime,
                    'character_count' => mb_strlen($translatedContent)
                ],
                'new_names_found' => $hasChunks ? 'extracted_incrementally' : (isset($newNames) ? count($newNames) : 0),
                'translation_method' => $hasChunks ? 'chunked_with_incremental_names' : 'regular_with_final_names'
            ];

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Finalization error: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }
    
    /**
     * Build translation prompt with context
     */
    private function buildTranslationPrompt(string $text, string $targetLanguage, string $sourceLanguage, array $context): string {
        $prompt = "Translate the following text to {$targetLanguage}";
        
        if ($sourceLanguage !== 'auto') {
            $prompt .= " from {$sourceLanguage}";
        }
        
        $prompt .= ".\n\n";
        
        // Add context-specific instructions
        if (isset($context['type'])) {
            switch ($context['type']) {
                case 'title':
                    $prompt .= "CRITICAL INSTRUCTIONS FOR TITLE TRANSLATION:\n";
                    $prompt .= "- This is a novel title that needs a single, clean translation\n";
                    $prompt .= "- Return ONLY the translated title text, nothing else\n";
                    $prompt .= "- Do NOT provide multiple options or alternatives\n";
                    $prompt .= "- Do NOT include explanations, notes, or commentary\n";
                    $prompt .= "- Do NOT use formatting like asterisks, bullets, or numbers\n";
                    $prompt .= "- Do NOT include phrases like 'Here are options' or 'More literal'\n";
                    $prompt .= "- Do NOT include parenthetical explanations or clarifications\n";
                    $prompt .= "- Your response must contain ONLY the final translated title\n";
                    $prompt .= "- If the title contains subtitles or parts, include them as a single cohesive title\n\n";
                    $prompt .= "EXAMPLE:\n";
                    $prompt .= "Input: 神の力を操る者～能力値「0」で見下されるが、実は世界最強の一人～\n";
                    $prompt .= "Correct output: The One Who Manipulates Divine Power ~Looked Down Upon for Having an Ability Score of '0', But Actually One of the World's Strongest~\n";
                    $prompt .= "Incorrect output: Here are a few possible translations... **More Literal:** ...\n\n";

                    // Add honorific preservation instructions for titles
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions();
                    break;
                case 'chapter':
                    $prompt .= "ENHANCED CHAPTER TRANSLATION INSTRUCTIONS:\n";
                    $prompt .= "- This is a novel chapter that requires careful attention to narrative flow and consistency\n";
                    $prompt .= "- Maintain the original tone, voice, and atmosphere throughout\n";
                    $prompt .= "- Preserve character personality and speech patterns\n";
                    $prompt .= "- Keep sound effects and onomatopoeia in their original romanized form\n";
                    $prompt .= "- Ensure smooth transitions between scenes and dialogue\n";
                    $prompt .= "- Maintain proper paragraph structure and formatting\n";

                    // Add enhanced context information
                    if (isset($context['narrative_context'])) {
                        $narrative = $context['narrative_context'];
                        $prompt .= "\nNARRATIVE CONTEXT:\n";
                        $prompt .= "- Voice: " . $narrative['narrative_voice'] . "\n";
                        if ($narrative['has_scene_transition']) {
                            $prompt .= "- Contains scene transitions - maintain clear breaks\n";
                        }
                        if ($narrative['has_time_transition']) {
                            $prompt .= "- Contains time transitions - preserve temporal flow\n";
                        }
                    }

                    if (isset($context['dialogue_context'])) {
                        $dialogue = $context['dialogue_context'];
                        if ($dialogue['has_dialogue']) {
                            $prompt .= "\nDIALOGUE CONTEXT:\n";
                            $prompt .= "- Contains " . $dialogue['dialogue_count'] . " dialogue instances\n";
                            $prompt .= "- Conversation flow: " . $dialogue['conversation_flow'] . "\n";
                            if (!empty($dialogue['dialogue_speakers'])) {
                                $prompt .= "- Speakers: " . implode(', ', $dialogue['dialogue_speakers']) . "\n";
                            }
                        }
                    }

                    if (isset($context['content_analysis'])) {
                        $analysis = $context['content_analysis'];
                        $prompt .= "\nCONTENT ANALYSIS:\n";
                        $prompt .= "- Primary type: " . $analysis['content_type'] . "\n";
                        if ($analysis['has_sound_effects']) {
                            $prompt .= "- Contains sound effects - preserve in romanized form\n";
                        }
                    }

                    // Sound effects preservation removed - all onomatopoeia will be translated to English

                    // Add honorific preservation instructions
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions();
                    $prompt .= "\n";
                    break;
                case 'synopsis':
                    $prompt .= "This is a novel synopsis/description. Keep it engaging and informative.\n\n";

                    // Add honorific preservation instructions for synopsis
                    $prompt .= $this->honorificService->getHonorificPreservationInstructions();
                    break;
            }
        }
        
        // Add enhanced context for continuity
        if (isset($context['previous_context']) && !empty($context['previous_context'])) {
            $prompt .= "PREVIOUS CONTEXT (for continuity):\n";
            $prompt .= "\"" . mb_substr($context['previous_context'], -200) . "\"\n\n";
        }

        if (isset($context['next_context']) && !empty($context['next_context'])) {
            $prompt .= "UPCOMING CONTEXT (for flow):\n";
            $prompt .= "\"" . mb_substr($context['next_context'], 0, 200) . "\"\n\n";
        }

        if (isset($context['previous_translation']) && !empty($context['previous_translation'])) {
            $prompt .= "PREVIOUS TRANSLATION (for consistency):\n";
            $prompt .= "\"" . mb_substr($context['previous_translation'], -150) . "\"\n\n";
        }

        // Add character context if available
        if (isset($context['previous_characters']) && !empty($context['previous_characters'])) {
            $prompt .= "CHARACTERS IN PREVIOUS SECTION:\n";
            $prompt .= "- " . implode(', ', $context['previous_characters']) . "\n\n";
        }

        // Add name consistency instructions
        if (isset($context['names']) && !empty($context['names'])) {
            $prompt .= "IMPORTANT: Use these consistent name translations throughout the text:\n";
            $nameCount = 0;
            foreach ($context['names'] as $name) {
                $translation = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
                $prompt .= "- {$name['original_name']} → {$translation}\n";
                $nameCount++;
            }
            $prompt .= "Always use these exact name forms for consistency across all translations. ";
            $prompt .= "Found {$nameCount} character names to maintain consistency.\n\n";
        }

        // Add chunk position context for better understanding
        if (isset($context['chunk_position']) && isset($context['total_chunks'])) {
            $prompt .= "POSITION CONTEXT:\n";
            $prompt .= "- This is chunk {$context['chunk_position']} of {$context['total_chunks']}\n";
            if (isset($context['is_first_chunk']) && $context['is_first_chunk']) {
                $prompt .= "- This is the opening of the chapter - set the tone carefully\n";
            }
            if (isset($context['is_last_chunk']) && $context['is_last_chunk']) {
                $prompt .= "- This is the conclusion of the chapter - provide proper closure\n";
            }
            $prompt .= "\n";
        }
        
        $prompt .= "Text to translate:\n{$text}";
        
        return $prompt;
    }
    
    /**
     * Make API request to Google Gemini with retry logic and error handling
     */
    public function makeApiRequest(string $prompt, array $customConfig = []): array {
        $maxRetries = 3;
        $baseDelay = 2; // Base delay in seconds
        $useFallback = false;

        for ($attempt = 1; $attempt <= $maxRetries; $attempt++) {
            $apiUrl = $useFallback ? $this->fallbackApiUrl : $this->apiUrl;
            $modelType = $useFallback ? 'fallback (2.0-flash)' : 'primary (1.5-flash)';

            file_put_contents('debug.log', "TranslationService: Attempt {$attempt} using {$modelType} model\n", FILE_APPEND);

            $result = $this->executeSingleApiRequest($prompt, $customConfig, $attempt, $apiUrl);

            if ($result['success']) {
                if ($useFallback) {
                    error_log("TranslationService: Successfully used fallback model");
                    file_put_contents('debug.log', "TranslationService: SUCCESS with fallback model after primary failed\n", FILE_APPEND);
                }
                return $result;
            }

            // Check if this is a retryable error
            if ($this->isRetryableError($result)) {
                file_put_contents('debug.log', "TranslationService: Retryable error detected: " . $result['error'] . "\n", FILE_APPEND);

                // If primary model is overloaded (503) or rate limited (429), try fallback model immediately
                if (!$useFallback && ($this->isModelOverloadedError($result) || $this->isRateLimitedError($result))) {
                    $errorType = $this->isRateLimitedError($result) ? 'rate limited' : 'overloaded';
                    error_log("TranslationService: Primary model {$errorType}, switching to fallback model");
                    file_put_contents('debug.log', "TranslationService: Switching to fallback model due to {$errorType} error\n", FILE_APPEND);
                    $useFallback = true;
                    continue; // Try immediately with fallback
                }

                if ($attempt < $maxRetries) {
                    $delay = $baseDelay * pow(2, $attempt - 1); // Exponential backoff
                    $modelType = $useFallback ? 'fallback' : 'primary';
                    error_log("TranslationService: Attempt {$attempt} failed with retryable error on {$modelType} model: {$result['error']}. Retrying in {$delay} seconds...");
                    sleep($delay);
                    continue;
                } else {
                    error_log("TranslationService: All {$maxRetries} attempts failed. Final error: {$result['error']}");
                }
            } else {
                error_log("TranslationService: Non-retryable error on attempt {$attempt}: {$result['error']}");
                break;
            }
        }

        return $result;
    }

    /**
     * Check if the error indicates model overload (503 Service Unavailable)
     */
    private function isModelOverloadedError(array $result): bool {
        if (isset($result['details']['http_code']) && $result['details']['http_code'] === 503) {
            return true;
        }

        if (isset($result['details']['type']) && $result['details']['type'] === 'UNAVAILABLE') {
            return true;
        }

        if (isset($result['error']) && strpos($result['error'], 'HTTP error: 503') !== false) {
            return true;
        }

        return false;
    }

    /**
     * Check if the error indicates rate limiting (429 Too Many Requests)
     */
    private function isRateLimitedError(array $result): bool {
        if (isset($result['details']['http_code']) && $result['details']['http_code'] === 429) {
            return true;
        }

        if (isset($result['details']['type']) && $result['details']['type'] === 'RESOURCE_EXHAUSTED') {
            return true;
        }

        if (isset($result['error']) && strpos($result['error'], 'HTTP error: 429') !== false) {
            return true;
        }

        return false;
    }

    /**
     * Execute a single API request attempt
     */
    private function executeSingleApiRequest(string $prompt, array $customConfig, int $attempt, string $apiUrl = null): array {
        $requestUrl = $apiUrl ?: $this->apiUrl;
        $modelType = ($requestUrl === $this->fallbackApiUrl) ? 'fallback' : 'primary';

        // Log the request for debugging
        error_log("TranslationService: Making API request to Gemini {$modelType} model (attempt {$attempt})");
        error_log("TranslationService: API URL: " . $requestUrl);
        error_log("TranslationService: API Key length: " . strlen($this->apiKey));
        error_log("TranslationService: Prompt length: " . strlen($prompt));

        // Default generation config
        $defaultConfig = [
            'temperature' => 0.3,
            'topK' => 40,
            'topP' => 0.95,
            'maxOutputTokens' => 8192
        ];

        // Merge with custom config if provided
        $generationConfig = array_merge($defaultConfig, $customConfig);

        $data = [
            'contents' => [
                [
                    'parts' => [
                        ['text' => $prompt]
                    ]
                ]
            ],
            'generationConfig' => $generationConfig
        ];

        $jsonData = json_encode($data);
        error_log("TranslationService: Request data size: " . strlen($jsonData) . " bytes");

        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $requestUrl . '?key=' . $this->apiKey,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $jsonData,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
            ],
            CURLOPT_TIMEOUT => TRANSLATION_TIMEOUT,
            CURLOPT_SSL_VERIFYPEER => false
        ]);

        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);

        // Log response details
        error_log("TranslationService: HTTP Code: " . $httpCode);
        error_log("TranslationService: Response length: " . strlen($response));

        if ($error) {
            error_log("TranslationService: cURL error: " . $error);
            return ['success' => false, 'error' => "cURL error: {$error}", 'retryable' => true];
        }

        if ($httpCode !== 200) {
            error_log("TranslationService: HTTP error response: " . substr($response, 0, 500));

            // Parse error response for better error handling
            $errorDetails = $this->parseErrorResponse($response, $httpCode);

            return [
                'success' => false,
                'error' => "HTTP error: {$httpCode}",
                'details' => $errorDetails,
                'retryable' => $this->isHttpCodeRetryable($httpCode),
                'response' => $response
            ];
        }

        $decodedResponse = json_decode($response, true);
        if (json_last_error() !== JSON_ERROR_NONE) {
            error_log("TranslationService: JSON decode error: " . json_last_error_msg());
            error_log("TranslationService: Raw response: " . substr($response, 0, 500));
            return ['success' => false, 'error' => 'Invalid JSON response: ' . json_last_error_msg(), 'retryable' => false];
        }

        error_log("TranslationService: API request successful");
        return ['success' => true, 'data' => $decodedResponse];
    }

    /**
     * Check if an error is retryable
     */
    private function isRetryableError(array $result): bool {
        return isset($result['retryable']) && $result['retryable'] === true;
    }

    /**
     * Check if an HTTP code indicates a retryable error
     */
    private function isHttpCodeRetryable(int $httpCode): bool {
        // Retryable HTTP codes
        $retryableCodes = [
            429, // Too Many Requests (rate limiting)
            500, // Internal Server Error
            502, // Bad Gateway
            503, // Service Unavailable
            504, // Gateway Timeout
        ];

        return in_array($httpCode, $retryableCodes);
    }

    /**
     * Parse error response to extract meaningful error information
     */
    private function parseErrorResponse(string $response, int $httpCode): array {
        $errorDetails = [
            'http_code' => $httpCode,
            'message' => 'Unknown error',
            'type' => 'unknown'
        ];

        if (!empty($response)) {
            $decoded = json_decode($response, true);
            if ($decoded && isset($decoded['error'])) {
                $error = $decoded['error'];
                $errorDetails['message'] = $error['message'] ?? 'Unknown error';
                $errorDetails['type'] = $error['status'] ?? 'unknown';
                $errorDetails['code'] = $error['code'] ?? $httpCode;

                // Log specific error types for monitoring
                if (isset($error['status'])) {
                    error_log("TranslationService: API Error Type: " . $error['status']);
                }
            }
        }

        return $errorDetails;
    }
    
    /**
     * Extract translation from API response
     */
    public function extractTranslationFromResponse(array $response): string {
        if (isset($response['candidates'][0]['content']['parts'][0]['text'])) {
            return trim($response['candidates'][0]['content']['parts'][0]['text']);
        }

        throw new Exception('No translation found in response');
    }

    /**
     * Clean up title translation to ensure single, clean result
     */
    private function cleanTitleTranslation(string $translation): string {
        $original = $translation;

        // Log the original response for debugging
        file_put_contents('debug.log', "TranslationService: Original title response: " . substr($translation, 0, 200) . "\n", FILE_APPEND);

        // Step 1: Remove common verbose introduction patterns
        $introPatterns = [
            '/^Here are.*?translations?.*?:/i',
            '/^Here are.*?options?.*?:/i',
            '/^Here are.*?possibilities.*?:/i',
            '/^I can translate.*?:/i',
            '/^The translation.*?:/i',
            '/^Possible translations?.*?:/i',
            '/^Different ways.*?:/i',
            '/^Various translations?.*?:/i',
            '/^Translation options?.*?:/i',
        ];

        $cleaned = $translation;
        foreach ($introPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        // Step 2: Remove section headers and formatting markers
        $sectionPatterns = [
            '/\*\*More Literal:\*\*/i',
            '/\*\*More Natural.*?\*\*/i',
            '/\*\*More Concise.*?\*\*/i',
            '/\*\*More Engaging.*?\*\*/i',
            '/\*\*Alternative.*?\*\*/i',
            '/\*\*Option \d+.*?\*\*/i',
            '/\*\*\d+\.\s*/',           // **1. **2. etc.
            '/\*\s*\*\s*/',             // ** markers
            '/\*\s*/',                  // Single asterisk bullets
            '/^\d+\.\s*/',              // Numbered list items at start
            '/^-\s*/',                  // Dash bullets at start
        ];

        foreach ($sectionPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        // Step 3: Split by lines and take the first substantial line
        $lines = explode("\n", $cleaned);
        $bestLine = '';

        foreach ($lines as $line) {
            $line = trim($line);

            // Skip empty lines
            if (empty($line)) continue;

            // Skip lines that are just formatting or bullets
            if (preg_match('/^[\*\-\d\.\s]+$/', $line)) continue;

            // Skip lines that contain explanation keywords
            if (preg_match('/\b(literal|natural|concise|engaging|option|alternative|depending|nuance|convey)\b/i', $line)) {
                continue;
            }

            // Take the first good line
            $bestLine = $line;
            break;
        }

        // If no good line found, use the first non-empty line
        if (empty($bestLine)) {
            foreach ($lines as $line) {
                $line = trim($line);
                if (!empty($line)) {
                    $bestLine = $line;
                    break;
                }
            }
        }

        $cleaned = $bestLine ?: $cleaned;

        // Step 4: Remove remaining unwanted patterns
        $cleanupPatterns = [
            '/\([^)]*\)/',              // Remove parenthetical explanations
            '/\[[^\]]*\]/',             // Remove bracketed explanations
            '/\*+/',                    // Remove any remaining asterisks
            '/^["\'"]*/',               // Remove leading quotes
            '/["\'"]*$/',               // Remove trailing quotes
        ];

        foreach ($cleanupPatterns as $pattern) {
            $cleaned = preg_replace($pattern, '', $cleaned);
        }

        // Step 5: Final cleanup
        $cleaned = preg_replace('/\s+/', ' ', $cleaned);  // Normalize whitespace
        $cleaned = trim($cleaned);

        // Step 6: Validate result
        if (empty($cleaned) || strlen($cleaned) < 3) {
            // Fallback: try to extract any title-like text from original
            if (preg_match('/([A-Za-z][A-Za-z0-9\s\-~\[\]]+)/', $original, $matches)) {
                $cleaned = trim($matches[1]);
            } else {
                // Last resort: use first 100 chars of original, cleaned
                $cleaned = trim(substr(preg_replace('/[^\w\s\-~\[\]]/u', '', $original), 0, 100));
            }
        }

        // Log the cleaned result
        file_put_contents('debug.log', "TranslationService: Cleaned title result: " . $cleaned . "\n", FILE_APPEND);

        return $cleaned;
    }

    /**
     * Validate title translation quality
     */
    private function validateTitleTranslation(string $translation): array {
        $issues = [];

        // Check for verbose response patterns
        $verbosePatterns = [
            '/Here are.*?options/i' => 'Contains "Here are options" pattern',
            '/More literal/i' => 'Contains "More literal" pattern',
            '/More natural/i' => 'Contains "More natural" pattern',
            '/\*\*\d+\./i' => 'Contains numbered list formatting',
            '/\*\s*[A-Z]/i' => 'Contains bullet point formatting',
            '/depending on/i' => 'Contains explanation text',
            '/alternative/i' => 'Contains alternative options',
            '/\n.*\n/s' => 'Contains multiple lines'
        ];

        foreach ($verbosePatterns as $pattern => $description) {
            if (preg_match($pattern, $translation)) {
                $issues[] = $description;
            }
        }

        // Check length constraints
        if (strlen($translation) > 200) {
            $issues[] = 'Translation too long (' . strlen($translation) . ' chars)';
        }

        if (strlen(trim($translation)) < 3) {
            $issues[] = 'Translation too short or empty';
        }

        return [
            'is_valid' => empty($issues),
            'issues' => $issues,
            'length' => strlen($translation)
        ];
    }

    /**
     * Get name dictionary for a novel
     */
    public function getNameDictionary(int $novelId): array {
        $names = $this->db->fetchAll(
            "SELECT original_name, romanization, translation, name_type, frequency
             FROM name_dictionary
             WHERE novel_id = ? AND original_name IS NOT NULL AND TRIM(original_name) != ''
             ORDER BY frequency DESC, name_type ASC",
            [$novelId]
        );

        // Filter out entries with empty or whitespace-only fields
        $filteredNames = [];
        foreach ($names as $name) {
            $original = isset($name['original_name']) ? trim($name['original_name']) : '';
            $translation = isset($name['translation']) ? trim($name['translation']) : '';
            $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';

            // Skip entries with empty original names
            if (empty($original)) {
                continue;
            }

            // Skip entries where all target fields are empty
            if (empty($translation) && empty($romanization)) {
                continue;
            }

            $filteredNames[] = $name;
        }

        // Log the query result for debugging
        file_put_contents('debug.log', "TranslationService: getNameDictionary query returned " . count($names) . " entries, filtered to " . count($filteredNames) . " valid entries for novel {$novelId}\n", FILE_APPEND);

        return $filteredNames;
    }
    
    /**
     * Romanize text using Google Gemini AI
     */
    public function romanizeText(string $text): array {
        $startTime = microtime(true);

        try {
            // Build romanization prompt
            $prompt = $this->buildRomanizationPrompt($text);

            // Make API request
            $response = $this->makeApiRequest($prompt);

            if (!$response['success']) {
                throw new Exception($response['error']);
            }

            $romanizedText = $this->extractTranslationFromResponse($response['data']);
            $executionTime = microtime(true) - $startTime;

            return [
                'success' => true,
                'original_text' => $text,
                'romanized_text' => trim($romanizedText),
                'execution_time' => round($executionTime, 2)
            ];

        } catch (Exception $e) {
            $executionTime = microtime(true) - $startTime;

            return [
                'success' => false,
                'error' => $e->getMessage(),
                'execution_time' => round($executionTime, 2),
                'original_text' => $text
            ];
        }
    }

    /**
     * Build romanization prompt for AI
     */
    private function buildRomanizationPrompt(string $text): string {
        return "Please romanize the following text to Latin alphabet. " .
               "For Japanese text, use Hepburn romanization. " .
               "For Chinese text, use Pinyin romanization. " .
               "For Korean text, use Revised Romanization. " .
               "Only return the romanized text, nothing else.\n\n" .
               "Text to romanize: " . $text;
    }

    /**
     * Build AI name detection prompt
     */
    private function buildNameDetectionPrompt(string $text): string {
        // Truncate text if too long to avoid API limits
        $maxLength = 3000;
        $analyzedText = mb_strlen($text) > $maxLength ? mb_substr($text, 0, $maxLength) . "..." : $text;

        return "Analyze this text and identify ONLY actual names and proper nouns that are likely to be important for translation consistency. " .
               "Be SELECTIVE and PRECISE - avoid common words, verbs, adjectives, and generic terms.\n\n" .

               "LOOK FOR (with HIGH CONFIDENCE only):\n\n" .

               "CHARACTER NAMES:\n" .
               "- Names with honorifics (田中さん, 山田くん, 佐藤ちゃん, 李明师兄)\n" .
               "- Full personal names (first + last name combinations)\n" .
               "- Unique character nicknames or titles\n" .
               "- Names that appear multiple times in the text\n\n" .

               "SPECIFIC ITEM NAMES:\n" .
               "- Named weapons or artifacts (炎の剣, 雷神の槌)\n" .
               "- Unique skills or techniques with specific names\n" .
               "- Special abilities or spells with proper names\n" .
               "- Avoid generic terms like '剣' (sword) or '魔法' (magic)\n\n" .

               "LOCATION NAMES:\n" .
               "- Specific place names (東京, 北京, 青山学院)\n" .
               "- Named buildings, schools, or institutions\n" .
               "- Fictional locations with unique names\n" .
               "- Avoid generic terms like '学校' (school) or '会社' (company)\n\n" .

               "ORGANIZATION NAMES:\n" .
               "- Named companies, guilds, or groups\n" .
               "- Specific sects, academies, or organizations\n" .
               "- Avoid generic organizational terms\n\n" .

               "AVOID THESE (common false positives):\n" .
               "- Common verbs, adjectives, adverbs\n" .
               "- Generic nouns (時間, 場所, 人, 事, 物)\n" .
               "- Sentence fragments or partial phrases\n" .
               "- Numbers, dates, or time expressions\n" .
               "- Common expressions or idioms\n" .
               "- Single characters unless clearly names\n\n" .

               "Return results as a JSON array with confidence scores:\n" .
               "[\n" .
               "  {\"name\": \"夏川レイ\", \"type\": \"character\", \"confidence\": 0.95},\n" .
               "  {\"name\": \"炎の剣\", \"type\": \"item\", \"confidence\": 0.85}\n" .
               "]\n\n" .

               "STRICT REQUIREMENTS:\n" .
               "- Return ONLY the JSON array, no explanations\n" .
               "- Maximum 8 names (quality over quantity)\n" .
               "- Only include names with confidence ≥ 0.7\n" .
               "- Use exact text from the source\n" .
               "- Prioritize names that appear multiple times\n" .
               "- Each name must be a distinct, meaningful proper noun\n\n" .

               "Text to analyze:\n" . $analyzedText;
    }

    /**
     * Parse AI response for name detection
     */
    private function parseAINameResponse(string $response): array {
        $names = [];

        try {
            // Clean up the response (remove any markdown formatting)
            $cleanResponse = preg_replace('/```json\s*|\s*```/', '', trim($response));

            // Try to extract JSON array from response
            if (preg_match('/\[.*\]/s', $cleanResponse, $matches)) {
                $cleanResponse = $matches[0];
            }

            $data = json_decode($cleanResponse, true);

            if (json_last_error() !== JSON_ERROR_NONE) {
                file_put_contents('debug.log', "TranslationService: JSON decode error: " . json_last_error_msg() . "\n", FILE_APPEND);
                file_put_contents('debug.log', "TranslationService: Cleaned response: " . $cleanResponse . "\n", FILE_APPEND);
                return [];
            }

            // Handle both array format and object format
            if (is_array($data)) {
                // Direct array format
                foreach ($data as $nameData) {
                    if (isset($nameData['name']) && isset($nameData['type'])) {
                        // Check confidence score if provided
                        $confidence = $nameData['confidence'] ?? 1.0;

                        // Only include names with sufficient confidence
                        if ($confidence >= 0.7) {
                            $names[] = [
                                'original_name' => $nameData['name'],
                                'romanization' => null, // Will be generated separately
                                'name_type' => $nameData['type'],
                                'confidence' => $confidence
                            ];
                        } else {
                            file_put_contents('debug.log', "TranslationService: Filtered out low confidence name: {$nameData['name']} (confidence: {$confidence})\n", FILE_APPEND);
                        }
                    }
                }
            } elseif (isset($data['names']) && is_array($data['names'])) {
                // Object with names array format
                foreach ($data['names'] as $nameData) {
                    if (isset($nameData['original']) && isset($nameData['type'])) {
                        $confidence = $nameData['confidence'] ?? 1.0;

                        if ($confidence >= 0.7) {
                            $names[] = [
                                'original_name' => $nameData['original'],
                                'romanization' => $nameData['romanization'] ?? null,
                                'name_type' => $nameData['type'],
                                'confidence' => $confidence
                            ];
                        } else {
                            file_put_contents('debug.log', "TranslationService: Filtered out low confidence name: {$nameData['original']} (confidence: {$confidence})\n", FILE_APPEND);
                        }
                    }
                }
            }

            // Apply additional filtering to remove obvious false positives
            $names = $this->filterAIDetectedNames($names);

            file_put_contents('debug.log', "TranslationService: AI parsed " . count($names) . " high-quality names after filtering\n", FILE_APPEND);

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Error parsing AI name response: " . $e->getMessage() . "\n", FILE_APPEND);
            file_put_contents('debug.log', "TranslationService: Raw AI response: " . substr($response, 0, 500) . "\n", FILE_APPEND);
        }

        return $names;
    }

    /**
     * Filter AI-detected names to remove obvious false positives
     */
    private function filterAIDetectedNames(array $names): array {
        $filteredNames = [];
        $falsePositivePatterns = $this->getFalsePositivePatterns();

        foreach ($names as $nameData) {
            $name = $nameData['original_name'];
            $isValid = true;

            // Check against false positive patterns
            foreach ($falsePositivePatterns as $pattern) {
                if (preg_match($pattern, $name)) {
                    file_put_contents('debug.log', "TranslationService: Filtered out false positive: '{$name}' (matched pattern: {$pattern})\n", FILE_APPEND);
                    $isValid = false;
                    break;
                }
            }

            // Additional validation checks
            if ($isValid) {
                // Check minimum length (but allow single katakana characters for names)
                if (mb_strlen($name) < 2 && !preg_match('/[\p{Katakana}]/u', $name)) {
                    file_put_contents('debug.log', "TranslationService: Filtered out too short name: '{$name}'\n", FILE_APPEND);
                    $isValid = false;
                }

                // Check for obvious non-names (numbers, punctuation only, etc.)
                if (preg_match('/^[\d\p{P}\s]+$/u', $name)) {
                    file_put_contents('debug.log', "TranslationService: Filtered out non-name: '{$name}'\n", FILE_APPEND);
                    $isValid = false;
                }

                // Check against expanded common words list
                if (in_array($name, $this->getCommonWords())) {
                    file_put_contents('debug.log', "TranslationService: Filtered out common word: '{$name}'\n", FILE_APPEND);
                    $isValid = false;
                }
            }

            if ($isValid) {
                $filteredNames[] = $nameData;
            }
        }

        return $filteredNames;
    }

    /**
     * Get patterns for common false positives in name detection
     */
    private function getFalsePositivePatterns(): array {
        return [
            // Common Japanese verbs and adjectives
            '/^(する|した|される|できる|いる|ある|なる|思う|言う|見る|聞く|来る|行く)$/',
            '/^(いい|悪い|大きい|小さい|新しい|古い|高い|安い|早い|遅い)$/',

            // Common particles and grammar elements
            '/^(です|ます|だった|でした|という|として|について|において)$/',

            // Time and quantity expressions
            '/^(今日|明日|昨日|時間|分|秒|年|月|日|週|回|度|番)$/',
            '/^\d+[年月日時分秒回度番]$/',

            // Generic nouns that are often false positives
            '/^(人|物|事|所|場所|時|方法|理由|結果|問題|答え)$/',
            '/^(会社|学校|病院|店|家|部屋|車|電車|バス|道)$/',

            // Common Chinese false positives
            '/^(的|了|在|是|我|有|他|这|个|们|到|说|她|你|出|就|起|还|把|从|给|对|都)$/',
            '/^(可以|没有|什么|知道|时候|地方|东西|问题|工作|朋友)$/',

            // Sentence fragments and incomplete words
            '/^[。！？、，]/',
            '/[。！？、，]$/',

            // Single hiragana (usually particles or grammar)
            '/^[\p{Hiragana}]$/u',
        ];
    }

    /**
     * Filter existing names for AI-detected names
     */
    private function filterExistingNamesAI(int $novelId, array $names): array {
        $newNames = [];

        foreach ($names as $nameData) {
            $existing = $this->db->fetchOne(
                "SELECT id FROM name_dictionary WHERE novel_id = ? AND original_name = ?",
                [$novelId, $nameData['original_name']]
            );

            if (!$existing) {
                $newNames[] = $nameData;
            }
        }

        return $newNames;
    }

    /**
     * Remove duplicate names from combined AI and pattern results
     */
    private function deduplicateNames(array $names): array {
        $unique = [];
        $seen = [];

        foreach ($names as $name) {
            $key = $name['original_name'];
            if (!isset($seen[$key])) {
                $unique[] = $name;
                $seen[$key] = true;
            }
        }

        return $unique;
    }

    /**
     * Apply selective filtering to pattern-based results to match AI selectivity
     */
    private function applySelectiveFiltering(array $names): array {
        $filtered = [];

        foreach ($names as $nameData) {
            $name = $nameData['text'] ?? $nameData['original_name'];

            // Apply the same strict filtering as AI results
            $isValid = true;

            // Check against false positive patterns
            foreach ($this->getFalsePositivePatterns() as $pattern) {
                if (preg_match($pattern, $name)) {
                    $isValid = false;
                    break;
                }
            }

            // Additional checks for pattern-based results
            if ($isValid) {
                // Must have at least one non-hiragana character for character names
                if ($nameData['type'] === 'character' && preg_match('/^[\p{Hiragana}]+$/u', $name)) {
                    $isValid = false;
                }

                // Skills should be in brackets or have specific patterns
                if ($nameData['type'] === 'skill' && !preg_match('/【.*】|術$|法$|技$/', $name)) {
                    $isValid = false;
                }

                // Locations should have location-specific suffixes or be known places
                if ($nameData['type'] === 'location' && mb_strlen($name) < 3 && !preg_match('/市$|県$|国$|町$|村$|区$/', $name)) {
                    $isValid = false;
                }
            }

            if ($isValid) {
                // Convert to consistent format
                $filtered[] = [
                    'original_name' => $name,
                    'romanization' => null,
                    'name_type' => $nameData['type'],
                    'confidence' => 0.6 // Lower confidence for pattern-based detection
                ];
            }
        }

        file_put_contents('debug.log', "TranslationService: Selective filtering reduced pattern names from " . count($names) . " to " . count($filtered) . "\n", FILE_APPEND);

        return $filtered;
    }

    /**
     * Process romanization for AI-detected names
     */
    private function processAINameRomanization(array $names): array {
        $processedNames = [];

        foreach ($names as $nameData) {
            try {
                // Add small delay between API calls
                if (count($processedNames) > 0) {
                    usleep(500000); // 0.5 second delay
                }

                // Generate romanization if not provided
                $romanization = null;
                if (empty($nameData['romanization'])) {
                    $romanizationResult = $this->romanizeText($nameData['original_name']);
                    if ($romanizationResult['success']) {
                        $romanization = $romanizationResult['romanized_text'];
                    }
                } else {
                    $romanization = $nameData['romanization'];
                }

                $processedNames[] = [
                    'original_name' => $nameData['original_name'],
                    'romanization' => $romanization,
                    'name_type' => $nameData['name_type']
                ];

            } catch (Exception $e) {
                file_put_contents('debug.log', "TranslationService: Error processing AI name '{$nameData['original_name']}': " . $e->getMessage() . "\n", FILE_APPEND);
                // Continue with next name instead of failing completely
                continue;
            }
        }

        return $processedNames;
    }

    /**
     * Extract names from text using AI-based detection and improved pattern matching
     */
    private function extractNames(string $originalText, string $translatedText): array {
        file_put_contents('debug.log', "TranslationService: Starting AI-based name extraction from text length: " . strlen($originalText) . "\n", FILE_APPEND);

        $names = [];

        try {
            // Try AI-based name detection first (more accurate and selective)
            $aiNames = $this->extractNamesWithAI($originalText);

            // Only use pattern matching as backup if AI completely fails (not just low count)
            if (empty($aiNames)) {
                file_put_contents('debug.log', "TranslationService: AI detection failed completely, using pattern matching as backup\n", FILE_APPEND);

                $patternNames = $this->extractNamesWithPatterns($originalText);

                // Apply additional filtering to pattern results to match AI selectivity
                $patternNames = $this->applySelectiveFiltering($patternNames);

                // Merge AI and pattern results, prioritizing AI results
                $allNames = array_merge($aiNames, $patternNames);
                $names = $this->deduplicateNames($allNames);
            } else {
                // AI detection succeeded, use those results
                $names = $aiNames;
                file_put_contents('debug.log', "TranslationService: Using AI-detected names only: " . count($names) . " names found\n", FILE_APPEND);
            }

            // ENHANCED: Add translation extraction from the translated text for better name consistency
            if (!empty($names) && !empty($translatedText)) {
                $names = $this->enhanceNamesWithTranslations($names, $originalText, $translatedText);
            }

            file_put_contents('debug.log', "TranslationService: Successfully extracted " . count($names) . " names with translations\n", FILE_APPEND);

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Name extraction error: " . $e->getMessage() . "\n", FILE_APPEND);
            // Return empty array on error to prevent translation failure
            return [];
        }

        return $names;
    }

    /**
     * Enhance extracted names with translations from the translated text
     */
    private function enhanceNamesWithTranslations(array $names, string $originalText, string $translatedText): array {
        file_put_contents('debug.log', "TranslationService: Enhancing " . count($names) . " names with translations\n", FILE_APPEND);

        $enhancedNames = [];

        foreach ($names as $name) {
            $enhancedName = $name;

            try {
                // Try to find the translation of this name in the translated text
                $translation = $this->findNameTranslationInText($name['original_name'], $originalText, $translatedText);

                if ($translation) {
                    $enhancedName['translation'] = $translation;
                    file_put_contents('debug.log', "TranslationService: Found translation for '{$name['original_name']}': '$translation'\n", FILE_APPEND);
                } else {
                    // Fallback: use romanization as translation if no specific translation found
                    $enhancedName['translation'] = $name['romanization'] ?? $name['original_name'];
                    file_put_contents('debug.log', "TranslationService: Using romanization as translation for '{$name['original_name']}': '{$enhancedName['translation']}'\n", FILE_APPEND);
                }

            } catch (Exception $e) {
                // Fallback: use romanization as translation
                $enhancedName['translation'] = $name['romanization'] ?? $name['original_name'];
                file_put_contents('debug.log', "TranslationService: Error finding translation for '{$name['original_name']}', using fallback: " . $e->getMessage() . "\n", FILE_APPEND);
            }

            $enhancedNames[] = $enhancedName;
        }

        return $enhancedNames;
    }

    /**
     * Find the translation of a name in the translated text
     */
    private function findNameTranslationInText(string $originalName, string $originalText, string $translatedText): ?string {
        try {
            // Split both texts into sentences for better matching
            $originalSentences = preg_split('/[。！？]/', $originalText);
            $translatedSentences = preg_split('/[.!?]/', $translatedText);

            // Find sentences containing the original name
            foreach ($originalSentences as $index => $originalSentence) {
                if (strpos($originalSentence, $originalName) !== false && isset($translatedSentences[$index])) {
                    $translatedSentence = $translatedSentences[$index];

                    // Look for capitalized words that could be name translations
                    if (preg_match_all('/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/', $translatedSentence, $matches)) {
                        // Return the first capitalized word/phrase found
                        foreach ($matches[0] as $match) {
                            // Skip common words that aren't names
                            if (!in_array(strtolower($match), ['the', 'and', 'but', 'for', 'with', 'from', 'this', 'that', 'they', 'there', 'then'])) {
                                return trim($match);
                            }
                        }
                    }
                }
            }

            return null;

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Error in findNameTranslationInText: " . $e->getMessage() . "\n", FILE_APPEND);
            return null;
        }
    }

    /**
     * Extract names using AI analysis (primary method)
     */
    private function extractNamesWithAI(string $text): array {
        file_put_contents('debug.log', "TranslationService: Starting AI-based name detection\n", FILE_APPEND);

        try {
            // Build AI name detection prompt
            $prompt = $this->buildNameDetectionPrompt($text);

            // Make API request
            $response = $this->makeApiRequest($prompt);

            if (!$response['success']) {
                throw new Exception($response['error']);
            }

            $aiResponse = $this->extractTranslationFromResponse($response['data']);

            // Parse AI response to extract names
            $rawNames = $this->parseAINameResponse($aiResponse);

            // Filter out names that already exist in database
            $novelId = $this->getCurrentNovelId();
            if ($novelId) {
                $rawNames = $this->filterExistingNamesAI($novelId, $rawNames);
            }

            // Sort by confidence and limit to highest quality names
            usort($rawNames, function($a, $b) {
                $confA = $a['confidence'] ?? 0.5;
                $confB = $b['confidence'] ?? 0.5;
                return $confB <=> $confA; // Sort descending by confidence
            });

            // Limit to prevent API overload, prioritizing high-confidence names
            $rawNames = array_slice($rawNames, 0, 6); // Reduced from 8 to 6 for better quality

            // Process romanization for names that don't have it
            $names = $this->processAINameRomanization($rawNames);

            file_put_contents('debug.log', "TranslationService: AI detected " . count($names) . " names\n", FILE_APPEND);

            return $names;

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: AI name detection failed: " . $e->getMessage() . "\n", FILE_APPEND);
            return [];
        }
    }

    /**
     * Extract names using pattern matching (fallback method)
     */
    private function extractNamesWithPatterns(string $originalText): array {
        // Get platform-specific patterns
        $patterns = $this->getNamePatterns($originalText);

        // Extract potential names using multiple patterns
        $potentialNames = $this->extractPotentialNames($originalText, $patterns);

        // Filter out common words and invalid names
        $filteredNames = $this->filterValidNames($potentialNames);

        // Filter out names that already exist in database
        $novelId = $this->getCurrentNovelId();
        if ($novelId) {
            $filteredNames = $this->filterExistingNames($novelId, $filteredNames);
        }

        // Limit to prevent API overload (process max 10 names per chapter)
        $limitedNames = array_slice($filteredNames, 0, 10);

        // Process names in batches to avoid timeouts
        if (!empty($limitedNames)) {
            return $this->processNamesBatch($limitedNames);
        }

        return [];
    }

    /**
     * Get name patterns based on detected language
     */
    private function getNamePatterns(string $text): array {
        $patterns = [];

        // Detect if text contains Japanese characters
        if (preg_match('/[\p{Hiragana}\p{Katakana}\p{Han}]/u', $text)) {
            $patterns = array_merge($patterns, NAME_PATTERNS['japanese']);
        }

        // Detect if text contains Chinese characters (Han only, no Hiragana/Katakana)
        if (preg_match('/[\p{Han}]/u', $text) && !preg_match('/[\p{Hiragana}\p{Katakana}]/u', $text)) {
            $patterns = array_merge($patterns, NAME_PATTERNS['chinese']);
        }

        return $patterns;
    }

    /**
     * Extract potential names using pattern matching and context analysis
     */
    private function extractPotentialNames(string $text, array $patterns): array {
        $potentialNames = [];

        // Use more precise patterns for Japanese names
        if (preg_match('/[\p{Hiragana}\p{Katakana}]/u', $text)) {

            // 1. Look for names with honorifics (most reliable)
            if (preg_match_all('/([^\s\p{P}]{2,6})(?=さん|くん|ちゃん|様|殿|君|氏|先生)/u', $text, $matches)) {
                foreach ($matches[1] as $match) {
                    if (preg_match('/[\p{Hiragana}\p{Katakana}\p{Han}]/', $match)) {
                        $potentialNames[] = [
                            'text' => $match,
                            'type' => 'character'
                        ];
                    }
                }
            }

            // 2. Look for pure katakana names (often foreign names or character names)
            if (preg_match_all('/[\p{Katakana}]{2,6}(?![ー])/u', $text, $matches)) {
                foreach ($matches[0] as $match) {
                    // Skip common katakana words
                    if (!in_array($match, ['キカワ', 'トラス', 'ネジ', 'バス', 'ドア', 'コンロ', 'スマホ', 'パック'])) {
                        $potentialNames[] = [
                            'text' => $match,
                            'type' => 'character'
                        ];
                    }
                }
            }

            // 3. Look for kanji+katakana combinations (common in Japanese names)
            if (preg_match_all('/[\p{Han}]{1,3}[\p{Katakana}]{1,4}/u', $text, $matches)) {
                foreach ($matches[0] as $match) {
                    if (mb_strlen($match) >= 2 && mb_strlen($match) <= 6) {
                        $potentialNames[] = [
                            'text' => $match,
                            'type' => 'character'
                        ];
                    }
                }
            }

            // 4. Look for skills in brackets
            if (preg_match_all('/【[^】]+】/u', $text, $matches)) {
                foreach ($matches[0] as $match) {
                    $potentialNames[] = [
                        'text' => $match,
                        'type' => 'skill'
                    ];
                }
            }

            // 5. Look for organization names
            if (preg_match_all('/([^\s\p{P}]{2,8})(?=会社|企業|工業|産業|金属|組織|団体)/u', $text, $matches)) {
                foreach ($matches[1] as $match) {
                    if (preg_match('/[\p{Han}]/', $match)) {
                        $potentialNames[] = [
                            'text' => $match,
                            'type' => 'organization'
                        ];
                    }
                }
            }
        }

        // Chinese character names (only if no Japanese characters detected)
        if (preg_match('/[\p{Han}]/u', $text) && !preg_match('/[\p{Hiragana}\p{Katakana}]/u', $text)) {
            // Enhanced Chinese name patterns with comprehensive honorifics
            $chineseHonorifics = [
                '先生', '小姐', '公子', '少爷', '大人', '前辈', '师父', '师兄', '师姐', '师弟', '师妹',
                '师伯', '师叔', '师姑', '师公', '师爷', '宗主', '长老', '掌门', '老祖', '王爷', '殿下',
                '陛下', '夫人', '千金', '哥哥', '姐姐', '弟弟', '妹妹', '大哥', '大姐', '二哥', '二姐',
                '三哥', '三姐', '四哥', '四姐', '五哥', '五姐', '老师', '同学', '朋友'
            ];

            $honorificPattern = implode('|', array_map('preg_quote', $chineseHonorifics));
            if (preg_match_all('/([^\s\p{P}]{2,4})(?=' . $honorificPattern . ')/u', $text, $matches)) {
                foreach ($matches[1] as $match) {
                    $potentialNames[] = [
                        'text' => $match,
                        'type' => 'character'
                    ];
                }
            }

            // Look for names with Chinese family terms
            if (preg_match_all('/([^\s\p{P}]{1,3})(?=哥|姐|弟|妹|叔|伯|姑|舅|姨|爷|奶|公|婆)/u', $text, $matches)) {
                foreach ($matches[1] as $match) {
                    if (mb_strlen($match) >= 2) {
                        $potentialNames[] = [
                            'text' => $match,
                            'type' => 'character'
                        ];
                    }
                }
            }
        }

        return $potentialNames;
    }

    /**
     * Filter out common words and invalid names
     */
    private function filterValidNames(array $potentialNames): array {
        $validNames = [];
        $commonWords = $this->getCommonWords();

        foreach ($potentialNames as $nameData) {
            $name = $nameData['text'];

            // Skip if too short or too long
            if (mb_strlen($name) < 2 || mb_strlen($name) > 8) {
                continue;
            }

            // Skip common words
            if (in_array($name, $commonWords)) {
                continue;
            }

            // Skip names that start or end with punctuation
            if (preg_match('/^[\p{P}]|[\p{P}]$/u', $name)) {
                continue;
            }

            // For character names, be more selective
            if ($nameData['type'] === 'character') {
                // Must contain at least one katakana or be a known name pattern
                if (!preg_match('/[\p{Katakana}]|夏川レイ|レイ/u', $name)) {
                    continue;
                }

                // Skip single character names unless they're katakana
                if (mb_strlen($name) === 1 && !preg_match('/[\p{Katakana}]/u', $name)) {
                    continue;
                }
            }

            // Skip if already processed in this batch
            $exists = false;
            foreach ($validNames as $existing) {
                if ($existing['text'] === $name) {
                    $exists = true;
                    break;
                }
            }

            if (!$exists) {
                $validNames[] = $nameData;
            }
        }

        return $validNames;
    }

    /**
     * Check if names already exist in database to avoid reprocessing
     */
    private function filterExistingNames(int $novelId, array $names): array {
        $newNames = [];

        foreach ($names as $nameData) {
            $existing = $this->db->fetchOne(
                "SELECT id FROM name_dictionary WHERE novel_id = ? AND original_name = ?",
                [$novelId, $nameData['text']]
            );

            if (!$existing) {
                $newNames[] = $nameData;
            }
        }

        return $newNames;
    }

    /**
     * Get current novel ID for name extraction
     */
    private function getCurrentNovelId(): ?int {
        return $this->currentNovelId;
    }

    /**
     * Get list of common words to filter out
     */
    private function getCommonWords(): array {
        return [
            // Japanese common words and fragments
            'です', 'ます', 'した', 'する', 'ある', 'いる', 'この', 'その', 'あの', 'どの',
            'こと', 'もの', 'とき', 'ところ', 'ひと', 'みんな', 'だけ', 'まで', 'から', 'より',
            'について', 'として', 'による', 'によって', 'において', 'に対して', 'に関して',
            '今日', '明日', '昨日', '今年', '来年', '去年', '午前', '午後', '夜中', '朝',
            '時間', '分間', '秒間', '年間', '月間', '週間', '日間', '事務員', '社長', '部長',
            '工場', '会社', '企業', '組織', '団体', '学校', '大学', '高校', '中学', '小学',
            'という', 'である', 'として', 'について', 'において', 'に対して', 'によって',

            // Additional common Japanese words that are often false positives
            '人', '物', '事', '所', '場所', '方法', '理由', '結果', '問題', '答え', '意味',
            '気持ち', '気分', '感じ', '様子', '状態', '状況', '関係', '影響', '効果',
            '部分', '全部', '一部', '最初', '最後', '途中', '前', '後', '上', '下', '中',
            '右', '左', '内', '外', '近く', '遠く', '隣', '向こう', 'こちら', 'そちら',
            '普通', '特別', '大切', '重要', '必要', '不要', '可能', '不可能', '簡単', '複雑',
            '新しい', '古い', '若い', '年寄り', '元気', '病気', '健康', '安全', '危険',

            // Common verbs that shouldn't be names
            '見る', '聞く', '話す', '読む', '書く', '食べる', '飲む', '歩く', '走る', '立つ',
            '座る', '寝る', '起きる', '来る', '行く', '帰る', '出る', '入る', '開く', '閉じる',
            '始める', '終わる', '続ける', '止める', '忘れる', '覚える', '知る', '分かる',

            // Specific fragments from the text that aren't names
            '全く関係', '「レイ', 'は保護司', 'さんはおじ', '。自殺未遂で', '判断された後', '時、レイ',
            '造り', '洋風だ', '日本人また', 'さんとの面談', 'だが今', '「さて、今日', 'は丸菱工業',
            '嫌な予感', '「あり', '、世間の景気', 'がいい時', 'はいいのだ', '世の中', '予感というの',
            '予感で', '仕方や時期', 'は経営状態', '乗る。目的地', '黒いモヤ', '世界に', '瘴気こそない',
            'まがま', '「こんにち', '撮りな', '。自分の身体', '思う', '来るで', '白な光', 'が広',
            '、悪い気', '白い光', '開く音', '知っていた', 'がない時', '床に', '練炭コンロ',
            'それから', '数分で救急車', '同乗しなくて', '思っていた', '作りな', '心で', '言えここ',
            '、フィクショ', 'ファンタジー', 'イケ', 'レイ。', 'ニヤ', '、レイ', 'トラス', 'ネジ',
            'トートバッグ', '、パタパタ', 'バス', 'シャッター', 'モヤ', 'スマホ', 'ハンカチ',
            'ドア', 'ドアノブ', 'カチャリ', 'ソファ', '「チッ', 'スッ', 'コンロ', 'ガムテープ',
            'ビッ', '、カラカラ', 'サッシ', 'ソファー', '。「', 'パック', '。スマホ', '、パトカー',
            'ー、', 'グウ',

            // Chinese common words
            '的', '了', '在', '是', '我', '有', '他', '这', '个', '们', '到', '说', '她', '你',
            '出', '就', '起', '还', '把', '从', '给', '对', '都', '可以', '没有', '什么', '知道',
            '时候', '地方', '东西', '问题', '工作', '学校', '家里', '朋友', '老师', '学生',
            '学是他', '他在北京',

            // Additional Chinese common words
            '一个', '两个', '三个', '很多', '一些', '所有', '每个', '任何', '别的', '其他',
            '现在', '以前', '以后', '刚才', '马上', '立刻', '突然', '慢慢', '快点', '赶紧',
            '应该', '必须', '可能', '也许', '当然', '肯定', '一定', '绝对', '完全', '非常',

            // Korean common words (for future expansion)
            '이', '그', '저', '것', '수', '때', '곳', '사람', '일', '말', '생각', '마음',
            '하다', '되다', '있다', '없다', '오다', '가다', '보다', '듣다', '말하다', '알다'
        ];
    }

    /**
     * Process names in batch to avoid API timeouts
     */
    private function processNamesBatch(array $names): array {
        $processedNames = [];
        $batchSize = 3; // Process 3 names at a time

        $batches = array_chunk($names, $batchSize);

        foreach ($batches as $batch) {
            foreach ($batch as $nameData) {
                try {
                    // Add small delay between API calls
                    if (count($processedNames) > 0) {
                        usleep(500000); // 0.5 second delay
                    }

                    $romanizationResult = $this->romanizeText($nameData['text']);

                    $processedNames[] = [
                        'original_name' => $nameData['text'],
                        'romanization' => $romanizationResult['success'] ? $romanizationResult['romanized_text'] : null,
                        'name_type' => $nameData['type']
                    ];

                } catch (Exception $e) {
                    file_put_contents('debug.log', "TranslationService: Error processing name '{$nameData['text']}': " . $e->getMessage() . "\n", FILE_APPEND);
                    // Continue with next name instead of failing completely
                    continue;
                }
            }

            // Add longer delay between batches
            if (count($batches) > 1) {
                sleep(1); // 1 second delay between batches
            }
        }

        return $processedNames;
    }
    
    /**
     * Extract names from a single chunk (public method for NovelManager)
     */
    public function extractNamesFromChunk(string $originalContent, string $translatedContent): array {
        try {
            // Use the same name extraction logic as the main method
            $furiganaService = new FuriganaService();
            $nameExtractionContent = $originalContent; // Chunk content should already be clean

            $newNames = $this->extractNames($nameExtractionContent, $translatedContent);

            file_put_contents('debug.log', "TranslationService: Extracted " . count($newNames) . " names from individual chunk\n", FILE_APPEND);

            return $newNames;

        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Error extracting names from chunk: " . $e->getMessage() . "\n", FILE_APPEND);
            return [];
        }
    }

    /**
     * Update name dictionary from chunk translation (public method for NovelManager)
     */
    public function updateNameDictionaryFromChunk(int $novelId, array $names, int $chapterNumber): void {
        try {
            $this->updateNameDictionary($novelId, $names, $chapterNumber);
            file_put_contents('debug.log', "TranslationService: Updated name dictionary from chunk with " . count($names) . " names\n", FILE_APPEND);
        } catch (Exception $e) {
            file_put_contents('debug.log', "TranslationService: Error updating name dictionary from chunk: " . $e->getMessage() . "\n", FILE_APPEND);
            throw $e;
        }
    }

    /**
     * Update name dictionary with new names
     */
    private function updateNameDictionary(int $novelId, array $names, int $chapterNumber): void {
        // First verify that the novel exists
        $novelExists = $this->db->fetchOne(
            "SELECT id FROM novels WHERE id = ?",
            [$novelId]
        );

        if (!$novelExists) {
            file_put_contents('debug.log', "TranslationService: Novel ID {$novelId} does not exist, skipping name dictionary update\n", FILE_APPEND);
            return;
        }

        foreach ($names as $name) {
            try {
                $existing = $this->db->fetchOne(
                    "SELECT id, frequency FROM name_dictionary WHERE novel_id = ? AND original_name = ?",
                    [$novelId, $name['original_name']]
                );

                if ($existing) {
                    // Update frequency
                    $this->db->update('name_dictionary',
                        ['frequency' => $existing['frequency'] + 1],
                        'id = ?',
                        [$existing['id']]
                    );
                } else {
                    // Insert new name with romanization
                    $insertData = [
                        'novel_id' => $novelId,
                        'original_name' => $name['original_name'],
                        'name_type' => $name['name_type'],
                        'frequency' => 1,
                        'first_appearance_chapter' => $chapterNumber
                    ];

                    // Add romanization if available
                    if (isset($name['romanization']) && !empty($name['romanization'])) {
                        $insertData['romanization'] = $name['romanization'];
                    }

                    $this->db->insert('name_dictionary', $insertData);
                }
            } catch (Exception $e) {
                file_put_contents('debug.log', "TranslationService: Failed to update name dictionary for '{$name['original_name']}': " . $e->getMessage() . "\n", FILE_APPEND);
                // Continue with other names instead of failing the entire translation
            }
        }
    }
    
    /**
     * Log translation for tracking
     */
    private function logTranslation(int $novelId, int $chapterId, string $type, array $result): void {
        // Determine status based on available data
        $status = 'success';
        if (isset($result['success'])) {
            $status = $result['success'] ? 'success' : 'error';
        } elseif (isset($result['error']) && !empty($result['error'])) {
            $status = 'error';
        }

        $logData = [
            'novel_id' => $novelId,
            'chapter_id' => $chapterId,
            'translation_type' => $type,
            'original_text_length' => $result['character_count'] ?? 0,
            'translated_text_length' => $result['translated_count'] ?? 0,
            'api_tokens_used' => $result['total_tokens'] ?? 0,
            'translation_time_seconds' => $result['execution_time'] ?? 0,
            'status' => $status,
            'error_message' => $result['error'] ?? null,
            'api_used' => $result['api_used'] ?? 'deepseek',
            'cache_hit_tokens' => $result['cache_hit_tokens'] ?? 0,
            'cache_miss_tokens' => $result['cache_miss_tokens'] ?? 0,
            'total_tokens' => $result['total_tokens'] ?? 0,
            'cache_hit_rate' => $result['cache_hit_rate'] ?? 0.0,
            'estimated_cost_savings' => $result['estimated_cost_savings'] ?? 0.0
        ];

        $this->db->insert('translation_logs', $logData);

        // Log cache performance summary for monitoring
        if ($status === 'success' && isset($result['api_used']) && $result['api_used'] === 'deepseek') {
            $cacheRate = $result['cache_hit_rate'] ?? 0;
            $savings = $result['estimated_cost_savings'] ?? 0;
            file_put_contents('debug.log', "TranslationService: Cache Performance - Type: {$type}, Hit Rate: {$cacheRate}%, Cost Savings: \${$savings}\n", FILE_APPEND);
        }
    }
}
