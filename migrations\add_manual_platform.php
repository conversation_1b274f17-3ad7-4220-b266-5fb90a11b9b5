<?php
/**
 * Migration: Add Manual Platform Support
 * Adds 'manual' as a platform option for manually entered novels
 */

require_once __DIR__ . '/../config/config.php';

echo "Migration: Adding Manual Platform Support\n";
echo "========================================\n\n";

try {
    $db = Database::getInstance();
    
    echo "Checking current platform enum values...\n";
    
    // Check if manual platform already exists
    $result = $db->query("SHOW COLUMNS FROM novels LIKE 'platform'");
    $platformColumn = $result->fetch(PDO::FETCH_ASSOC);
    
    if ($platformColumn && strpos($platformColumn['Type'], 'manual') !== false) {
        echo "✓ Manual platform already exists in novels table\n";
    } else {
        echo "Adding 'manual' to platform enum...\n";
        
        // Update the platform enum to include 'manual'
        $alterSQL = "ALTER TABLE novels MODIFY COLUMN platform ENUM('kakuyomu', 'syosetu', 'shuba69', 'manual') NOT NULL";
        $db->query($alterSQL);
        
        echo "✓ Successfully added 'manual' platform to novels table\n";
    }
    
    echo "\nMigration completed successfully!\n";
    echo "Manual novel entry feature is now supported in the database.\n";
    
} catch (Exception $e) {
    echo "✗ Migration failed: " . $e->getMessage() . "\n";
    echo "Please check your database connection and try again.\n";
    exit(1);
}
?>
