# Name Detection Logic Improvements

## Overview
This document outlines the improvements made to the name detection system in the novel translation application to address issues with overly aggressive detection and false positives.

## Problems Identified

### 1. Overly Broad AI Prompt
- **Issue**: The original prompt asked for "ALL types of names and proper nouns" and "Include ALL proper nouns, not just obvious names"
- **Result**: Too many false positives including common words, verbs, and sentence fragments

### 2. Insufficient Filtering
- **Issue**: AI detection bypassed much of the pattern-based filtering logic
- **Result**: Common words and obvious non-names were being detected as names

### 3. Low Fallback Threshold
- **Issue**: System fell back to pattern matching when AI returned fewer than 3 names
- **Result**: Added more false positives through less accurate pattern matching

### 4. Lack of Confidence Assessment
- **Issue**: No confidence scoring or quality assessment of detected names
- **Result**: Low-quality detections were treated equally with high-quality ones

## Improvements Implemented

### 1. Refined AI Detection Prompt
**File**: `classes/TranslationService.php` - `buildNameDetectionPrompt()`

**Changes**:
- Changed from "identify ALL types of names" to "identify ONLY actual names"
- Added "Be SELECTIVE and PRECISE" instruction
- Added specific examples of what to avoid (common false positives)
- Introduced confidence scoring requirement (≥ 0.7)
- Reduced maximum names from 12 to 8 (quality over quantity)
- Added negative examples to prevent common mistakes

**New Prompt Structure**:
```
Analyze this text and identify ONLY actual names and proper nouns that are likely to be important for translation consistency.
Be SELECTIVE and PRECISE - avoid common words, verbs, adjectives, and generic terms.

LOOK FOR (with HIGH CONFIDENCE only):
- Character names with honorifics
- Specific item names (not generic terms)
- Named locations (not generic places)
- Named organizations

AVOID THESE (common false positives):
- Common verbs, adjectives, adverbs
- Generic nouns
- Sentence fragments
- Time expressions
- Common expressions
```

### 2. Enhanced Response Parsing with Confidence Filtering
**File**: `classes/TranslationService.php` - `parseAINameResponse()`

**Changes**:
- Added confidence score parsing from AI responses
- Filter out names with confidence < 0.7
- Added post-AI filtering with `filterAIDetectedNames()`
- Better logging for debugging filtered names

### 3. New False Positive Filtering System
**File**: `classes/TranslationService.php` - `filterAIDetectedNames()` and `getFalsePositivePatterns()`

**Features**:
- Pattern-based filtering for common false positives
- Language-specific filtering (Japanese, Chinese, Korean)
- Grammar element detection (particles, verbs, adjectives)
- Sentence fragment detection
- Minimum length validation with exceptions for valid single characters

**Pattern Examples**:
```php
// Common Japanese verbs and adjectives
'/^(する|した|される|できる|いる|ある|なる|思う|言う|見る|聞く|来る|行く)$/',

// Time and quantity expressions  
'/^\d+[年月日時分秒回度番]$/',

// Generic nouns that are often false positives
'/^(人|物|事|所|場所|時|方法|理由|結果|問題|答え)$/',
```

### 4. Conservative Fallback Logic
**File**: `classes/TranslationService.php` - `extractNames()`

**Changes**:
- Only use pattern matching when AI completely fails (not just low count)
- Apply selective filtering to pattern results to match AI selectivity
- Added `applySelectiveFiltering()` method for pattern-based results
- Better logging to track which method is being used

### 5. Improved Common Words List
**File**: `classes/TranslationService.php` - `getCommonWords()`

**Additions**:
- Extended Japanese common words (verbs, adjectives, nouns)
- Added Chinese common words and expressions
- Added Korean common words for future expansion
- Specific false positives identified from actual usage

### 6. Quality-Based Name Prioritization
**File**: `classes/TranslationService.php` - `extractNamesWithAI()`

**Changes**:
- Sort detected names by confidence score
- Prioritize high-confidence names
- Reduced limit from 8 to 6 names for better quality
- Better API usage optimization

## Name Dictionary Integration Verification

The name dictionary integration remains robust with proper fallback logic:

1. **Translation Priority**: `translation` → `romanization` → `original_name`
2. **Consistent Application**: Names are applied consistently across all chapters
3. **Proper Context**: Name dictionary is passed to DeepSeek API with clear instructions

## Testing

### Test Script
Created `test_improved_name_detection.php` to verify improvements:

**Test Cases**:
1. Japanese text with mixed content
2. Chinese cultivation novel text  
3. Text with common false positives
4. Mixed content with skills and locations

**Validation Metrics**:
- Accuracy: Percentage of expected names found
- False Positives: Count of unwanted names detected
- Clean Detection: Whether filtering successfully removed false positives

### Expected Results
- **Reduced False Positives**: Common words should no longer be detected as names
- **Maintained Accuracy**: Important names should still be detected
- **Better Quality**: Higher confidence in detected names
- **Consistent Translation**: Name dictionary integration should work seamlessly

## Usage Instructions

### Running Tests
```bash
# Test the improved name detection
php test_improved_name_detection.php
```

### Monitoring Performance
- Check `debug.log` for detailed filtering information
- Monitor confidence scores in name detection logs
- Verify name dictionary usage in translation logs

### Adjusting Thresholds
If needed, adjust these parameters in `TranslationService.php`:
- Confidence threshold: Currently 0.7 (line in `parseAINameResponse()`)
- Maximum names: Currently 6 (line in `extractNamesWithAI()`)
- Pattern filtering strictness: Modify `getFalsePositivePatterns()`

## Benefits

1. **Reduced False Positives**: Significantly fewer common words detected as names
2. **Improved Translation Quality**: More consistent character names across chapters
3. **Better Performance**: Less API usage for processing invalid names
4. **Easier Maintenance**: Cleaner name dictionary with fewer false entries
5. **Enhanced User Experience**: Less manual cleanup required in name dictionary

## Future Enhancements

1. **Machine Learning**: Train a model on validated name detection results
2. **Context Analysis**: Consider surrounding context for better name detection
3. **Frequency Analysis**: Weight names by appearance frequency in text
4. **User Feedback**: Allow users to mark false positives for system learning
