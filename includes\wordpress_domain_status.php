<?php
/**
 * WordPress Domain Status Component
 * Shows posting status across multiple domains for a novel
 */

if (!isset($novelId)) {
    return;
}

$wordpressService = new WordPressService();
$isConfigured = $wordpressService->isConfigured();

if (!$isConfigured) {
    return;
}

// Get posting status for this novel
$status = $wordpressService->getNovelPostingStatus($novelId);
$currentDomain = $status['current_domain'] ?? '';
$allDomains = $status['all_domains'] ?? [];

// Get all chapters for this novel
$chapters = $db->fetchAll(
    "SELECT id, chapter_number, translated_title, original_title, translation_status 
     FROM chapters 
     WHERE novel_id = ? 
     ORDER BY chapter_number",
    [$novelId]
);

// Get chapter posting status across all domains
$chapterDomainStatus = [];
foreach ($chapters as $chapter) {
    $chapterStatus = $wordpressService->getChapterPostingStatus($chapter['id']);
    $chapterDomainStatus[$chapter['id']] = $chapterStatus['all_domains'] ?? [];
}
?>

<?php if (!empty($allDomains)): ?>
<div class="card mt-3">
    <div class="card-header">
        <h6 class="mb-0">
            <i class="fab fa-wordpress me-2"></i>
            WordPress Multi-Domain Status
        </h6>
    </div>
    <div class="card-body">
        <div class="row">
            <div class="col-md-6">
                <h6>Current Configuration</h6>
                <p class="mb-2">
                    <strong>Domain:</strong> 
                    <span class="badge bg-primary"><?= htmlspecialchars($currentDomain) ?></span>
                </p>
                <p class="mb-3">
                    <strong>Novel Status:</strong>
                    <?php if ($status['novel_posted']): ?>
                        <span class="badge bg-success">Posted</span>
                        <a href="<?= htmlspecialchars($status['novel_post']['wordpress_url']) ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-2">
                            <i class="fas fa-external-link-alt"></i> View
                        </a>
                    <?php else: ?>
                        <span class="badge bg-warning">Not Posted</span>
                    <?php endif; ?>
                </p>
                <p class="mb-0">
                    <strong>Chapters Posted:</strong> 
                    <span class="badge bg-info"><?= $status['chapters_posted'] ?></span>
                </p>
            </div>
            
            <div class="col-md-6">
                <h6>All Domains</h6>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>Domain</th>
                                <th>Posts</th>
                                <th>Status</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($allDomains as $domainInfo): ?>
                            <tr>
                                <td>
                                    <?= htmlspecialchars($domainInfo['wordpress_domain']) ?>
                                    <?php if ($domainInfo['wordpress_domain'] === $currentDomain): ?>
                                        <span class="badge bg-primary ms-1">Current</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= $domainInfo['post_count'] ?></td>
                                <td>
                                    <span class="badge bg-success">Active</span>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <?php if (!empty($chapters)): ?>
        <hr>
        <h6>Chapter Distribution</h6>
        <div class="table-responsive">
            <table class="table table-sm">
                <thead>
                    <tr>
                        <th>Chapter</th>
                        <th>Title</th>
                        <?php 
                        $uniqueDomains = [];
                        foreach ($allDomains as $domain) {
                            $uniqueDomains[] = $domain['wordpress_domain'];
                        }
                        $uniqueDomains = array_unique($uniqueDomains);
                        foreach ($uniqueDomains as $domain): 
                        ?>
                        <th><?= htmlspecialchars($domain) ?></th>
                        <?php endforeach; ?>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($chapters as $chapter): ?>
                    <tr>
                        <td><?= $chapter['chapter_number'] ?></td>
                        <td>
                            <?= htmlspecialchars($chapter['translated_title'] ?: $chapter['original_title'] ?: 'Untitled') ?>
                            <?php if ($chapter['translation_status'] !== 'completed'): ?>
                                <small class="text-muted">(<?= ucfirst($chapter['translation_status']) ?>)</small>
                            <?php endif; ?>
                        </td>
                        <?php foreach ($uniqueDomains as $domain): ?>
                        <td>
                            <?php 
                            $posted = false;
                            $url = '';
                            foreach ($chapterDomainStatus[$chapter['id']] as $domainPost) {
                                if ($domainPost['wordpress_domain'] === $domain) {
                                    $posted = true;
                                    $url = $domainPost['wordpress_url'];
                                    break;
                                }
                            }
                            ?>
                            <?php if ($posted): ?>
                                <span class="badge bg-success">✓</span>
                                <?php if ($url): ?>
                                    <a href="<?= htmlspecialchars($url) ?>" target="_blank" class="btn btn-sm btn-outline-primary ms-1">
                                        <i class="fas fa-external-link-alt"></i>
                                    </a>
                                <?php endif; ?>
                            <?php else: ?>
                                <span class="badge bg-secondary">-</span>
                            <?php endif; ?>
                        </td>
                        <?php endforeach; ?>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
        <?php endif; ?>
        
        <div class="mt-3">
            <small class="text-muted">
                <i class="fas fa-info-circle me-1"></i>
                To post to a different domain, change the WordPress Site URL in Settings and post again.
                The same content can be posted to multiple domains.
            </small>
        </div>
    </div>
</div>
<?php endif; ?>

<?php if (empty($allDomains) && $isConfigured): ?>
<div class="alert alert-info mt-3">
    <h6><i class="fab fa-wordpress me-2"></i>WordPress Integration Ready</h6>
    <p class="mb-2">This novel hasn't been posted to WordPress yet.</p>
    <p class="mb-0">
        <strong>Current domain:</strong> <?= htmlspecialchars($currentDomain) ?>
    </p>
</div>
<?php endif; ?>
