<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API Debug Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .result { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .error { background: #ffebee; color: #c62828; }
        .success { background: #e8f5e8; color: #2e7d32; }
        button { padding: 10px 20px; margin: 5px; }
        pre { white-space: pre-wrap; word-wrap: break-word; }
    </style>
</head>
<body>
    <h1>API Debug Test</h1>
    
    <div class="test-section">
        <h2>Test 1: Basic API Request</h2>
        <button onclick="testBasicAPI()">Test Basic API</button>
        <div id="basic-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 2: Chapter Save API</h2>
        <button onclick="testChapterSave()">Test Chapter Save</button>
        <div id="chapter-result" class="result"></div>
    </div>
    
    <div class="test-section">
        <h2>Test 3: Raw Fetch Request</h2>
        <button onclick="testRawFetch()">Test Raw Fetch</button>
        <div id="raw-result" class="result"></div>
    </div>

    <script>
        async function testBasicAPI() {
            const resultDiv = document.getElementById('basic-result');
            resultDiv.innerHTML = 'Testing...';
            
            try {
                const response = await fetch('/wc/api/chapters.php', {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                
                const text = await response.text();
                resultDiv.innerHTML = `
                    <div class="success">
                        <strong>Status:</strong> ${response.status}<br>
                        <strong>Headers:</strong> ${JSON.stringify(Object.fromEntries(response.headers))}<br>
                        <strong>Response:</strong><br>
                        <pre>${text}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testChapterSave() {
            const resultDiv = document.getElementById('chapter-result');
            resultDiv.innerHTML = 'Testing...';
            
            const testData = {
                novel_id: 1,
                chapter_number: 2
            };
            
            try {
                const response = await fetch('/wc/api/chapters.php', {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    body: JSON.stringify(testData)
                });
                
                const text = await response.text();
                
                // Log raw response for debugging
                console.log('Raw response:', text);
                console.log('Response length:', text.length);
                console.log('First 100 chars:', text.substring(0, 100));
                
                // Try to parse as JSON
                let jsonData;
                try {
                    jsonData = JSON.parse(text);
                } catch (jsonError) {
                    resultDiv.innerHTML = `
                        <div class="error">
                            <strong>JSON Parse Error:</strong> ${jsonError.message}<br>
                            <strong>Status:</strong> ${response.status}<br>
                            <strong>Raw Response:</strong><br>
                            <pre>${text}</pre>
                        </div>
                    `;
                    return;
                }
                
                resultDiv.innerHTML = `
                    <div class="success">
                        <strong>Status:</strong> ${response.status}<br>
                        <strong>Success:</strong> ${jsonData.success}<br>
                        <strong>Response:</strong><br>
                        <pre>${JSON.stringify(jsonData, null, 2)}</pre>
                    </div>
                `;
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
        
        async function testRawFetch() {
            const resultDiv = document.getElementById('raw-result');
            resultDiv.innerHTML = 'Testing...';
            
            const testData = {
                novel_id: 1,
                chapter_number: 2
            };
            
            try {
                // Use XMLHttpRequest for more detailed debugging
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/wc/api/chapters.php', true);
                xhr.setRequestHeader('Content-Type', 'application/json');
                xhr.setRequestHeader('Accept', 'application/json');
                xhr.setRequestHeader('X-Requested-With', 'XMLHttpRequest');
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        const response = xhr.responseText;
                        
                        console.log('XHR Response:', response);
                        console.log('XHR Status:', xhr.status);
                        console.log('XHR Headers:', xhr.getAllResponseHeaders());
                        
                        resultDiv.innerHTML = `
                            <div class="${xhr.status === 200 ? 'success' : 'error'}">
                                <strong>Status:</strong> ${xhr.status}<br>
                                <strong>Headers:</strong><br>
                                <pre>${xhr.getAllResponseHeaders()}</pre>
                                <strong>Response Length:</strong> ${response.length}<br>
                                <strong>First 200 chars:</strong><br>
                                <pre>${response.substring(0, 200)}</pre>
                                <strong>Full Response:</strong><br>
                                <pre>${response}</pre>
                            </div>
                        `;
                    }
                };
                
                xhr.send(JSON.stringify(testData));
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">Error: ${error.message}</div>`;
            }
        }
    </script>
</body>
</html>
