<?php
/**
 * Common Header Component
 */

function renderHeader($pageTitle = 'Dashboard', $additionalCSS = []) {
    $appName = defined('APP_NAME') ? APP_NAME : 'Novel Translator';
    $fullTitle = $pageTitle === 'Dashboard' ? $appName : $pageTitle . ' - ' . $appName;
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title><?= htmlspecialchars($fullTitle) ?></title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
        <link href="assets/css/style.css" rel="stylesheet">
        <?php foreach ($additionalCSS as $css): ?>
            <link href="<?= htmlspecialchars($css) ?>" rel="stylesheet">
        <?php endforeach; ?>
    </head>
    <body>
    <?php
}

function renderFooter($additionalJS = []) {
    // Generate cache busting parameter
    $cacheVersion = time();
    ?>
        <!-- Toast Container -->
        <div class="toast-container position-fixed bottom-0 end-0 p-3">
            <div id="toast" class="toast" role="alert">
                <div class="toast-header">
                    <strong class="me-auto">Notification</strong>
                    <button type="button" class="btn-close" data-bs-dismiss="toast"></button>
                </div>
                <div class="toast-body"></div>
            </div>
        </div>

        <!-- Loading Overlay -->
        <div id="loading-overlay" class="loading-overlay" style="display: none;">
            <div class="loading-content">
                <div class="spinner-border text-light" role="status">
                    <span class="visually-hidden">Loading...</span>
                </div>
                <p class="text-light mt-3">Processing...</p>
            </div>
        </div>

        <!-- Scripts -->
        <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
        <script src="assets/js/common.js?v=<?= $cacheVersion ?>"></script>
        <?php foreach ($additionalJS as $js): ?>
            <script src="<?= htmlspecialchars($js) ?>?v=<?= $cacheVersion ?>"></script>
        <?php endforeach; ?>

        <script>
        // Debug: Log cache version to verify cache busting is working
        console.log('JavaScript cache version:', '<?= $cacheVersion ?>');
        </script>
    </body>
    </html>
    <?php
}
?>
