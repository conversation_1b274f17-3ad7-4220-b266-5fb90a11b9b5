<?php
/**
 * Migration: Add WordPress Profiles System
 * Creates wordpress_profiles table and updates wordpress_posts for profile-based posting
 */

require_once '../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Starting WordPress profiles migration...\n";
    
    // Create wordpress_profiles table
    $profilesTableExists = $db->fetchOne("SHOW TABLES LIKE 'wordpress_profiles'");
    
    if (!$profilesTableExists) {
        echo "Creating wordpress_profiles table...\n";
        
        $createProfilesSQL = "
        CREATE TABLE wordpress_profiles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            profile_name VARCHAR(100) NOT NULL UNIQUE,
            site_url VARCHAR(500) NOT NULL,
            username VARCHAR(100) NOT NULL,
            app_password VARCHAR(255) NOT NULL,
            novel_post_type ENUM('page', 'post', 'custom') DEFAULT 'page',
            chapter_post_type ENUM('page', 'post', 'custom') DEFAULT 'post',
            novel_custom_post_type VARCHAR(50) DEFAULT '',
            chapter_custom_post_type VARCHAR(50) DEFAULT '',
            use_custom_post_types BOOLEAN DEFAULT FALSE,
            default_category VARCHAR(100) DEFAULT '',
            auto_publish BOOLEAN DEFAULT TRUE,
            include_original_title BOOLEAN DEFAULT TRUE,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_profile_name (profile_name),
            INDEX idx_is_active (is_active)
        )";
        
        $db->query($createProfilesSQL);
        echo "✓ wordpress_profiles table created successfully\n";
    } else {
        echo "✓ wordpress_profiles table already exists\n";
    }
    
    // Check if profile_id column exists in wordpress_posts
    $columns = $db->fetchAll("SHOW COLUMNS FROM wordpress_posts LIKE 'profile_id'");
    
    if (empty($columns)) {
        echo "Adding profile_id column to wordpress_posts...\n";
        
        // Add profile_id column
        $db->query("ALTER TABLE wordpress_posts ADD COLUMN profile_id INT NULL");
        
        // Add foreign key constraint
        $db->query("ALTER TABLE wordpress_posts ADD CONSTRAINT fk_wordpress_posts_profile 
                   FOREIGN KEY (profile_id) REFERENCES wordpress_profiles(id) ON DELETE SET NULL");
        
        // Add index
        $db->query("ALTER TABLE wordpress_posts ADD INDEX idx_profile_id (profile_id)");
        
        echo "✓ profile_id column added successfully\n";
    } else {
        echo "✓ profile_id column already exists\n";
    }
    
    // Migrate existing WordPress settings to default profile
    echo "Migrating existing WordPress settings...\n";
    
    $existingSettings = $db->fetchAll(
        "SELECT preference_key, preference_value FROM user_preferences 
         WHERE preference_key LIKE 'wordpress_%'"
    );
    
    $settings = [];
    foreach ($existingSettings as $setting) {
        $settings[$setting['preference_key']] = $setting['preference_value'];
    }
    
    // Check if we have existing WordPress configuration
    if (!empty($settings['wordpress_site_url']) && !empty($settings['wordpress_username'])) {
        // Check if default profile already exists
        $defaultProfile = $db->fetchOne(
            "SELECT id FROM wordpress_profiles WHERE profile_name = 'Default Profile'"
        );
        
        if (!$defaultProfile) {
            echo "Creating default profile from existing settings...\n";
            
            $profileData = [
                'profile_name' => 'Default Profile',
                'site_url' => $settings['wordpress_site_url'] ?? '',
                'username' => $settings['wordpress_username'] ?? '',
                'app_password' => $settings['wordpress_app_password'] ?? '',
                'novel_post_type' => $settings['wordpress_novel_post_type'] ?? 'page',
                'chapter_post_type' => $settings['wordpress_chapter_post_type'] ?? 'post',
                'novel_custom_post_type' => $settings['wordpress_novel_custom_post_type'] ?? '',
                'chapter_custom_post_type' => $settings['wordpress_chapter_custom_post_type'] ?? '',
                'use_custom_post_types' => ($settings['wordpress_use_custom_post_types'] ?? 'false') === 'true',
                'default_category' => $settings['wordpress_default_category'] ?? '',
                'auto_publish' => ($settings['wordpress_auto_publish'] ?? 'true') === 'true',
                'include_original_title' => ($settings['wordpress_include_original_title'] ?? 'true') === 'true',
                'is_active' => true
            ];
            
            $profileId = $db->insert('wordpress_profiles', $profileData);
            
            // Update existing wordpress_posts to use this profile
            if ($profileId) {
                $db->query("UPDATE wordpress_posts SET profile_id = ? WHERE profile_id IS NULL", [$profileId]);
                echo "✓ Default profile created and existing posts linked\n";
            }
        } else {
            echo "✓ Default profile already exists\n";
        }
    }
    
    echo "\n✅ WordPress profiles migration completed successfully!\n";
    echo "\nWhat this enables:\n";
    echo "- Create and manage multiple WordPress domain profiles\n";
    echo "- Select which profile to use when posting content\n";
    echo "- Post same content to multiple WordPress domains\n";
    echo "- Track which profile was used for each post\n";
    echo "- Streamlined multi-domain workflow\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
