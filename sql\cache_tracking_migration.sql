-- Migration to add DeepSeek prompt caching tracking to translation_logs
-- Run this script to add cache performance tracking fields

-- Add cache tracking fields to translation_logs table (with IF NOT EXISTS checks)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND column_name = 'api_used') = 0,
    'ALTER TABLE translation_logs ADD COLUMN api_used ENUM(''deepseek'', ''gemini'') DEFAULT ''deepseek'' AFTER api_tokens_used',
    'SELECT ''Column api_used already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND column_name = 'cache_hit_tokens') = 0,
    'ALTER TABLE translation_logs ADD COLUMN cache_hit_tokens INT DEFAULT 0 AFTER api_used',
    'SELECT ''Column cache_hit_tokens already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND column_name = 'cache_miss_tokens') = 0,
    'ALTER TABLE translation_logs ADD COLUMN cache_miss_tokens INT DEFAULT 0 AFTER cache_hit_tokens',
    'SELECT ''Column cache_miss_tokens already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND column_name = 'total_tokens') = 0,
    'ALTER TABLE translation_logs ADD COLUMN total_tokens INT DEFAULT 0 AFTER cache_miss_tokens',
    'SELECT ''Column total_tokens already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND column_name = 'cache_hit_rate') = 0,
    'ALTER TABLE translation_logs ADD COLUMN cache_hit_rate DECIMAL(5,2) DEFAULT 0.00 AFTER total_tokens',
    'SELECT ''Column cache_hit_rate already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND column_name = 'estimated_cost_savings') = 0,
    'ALTER TABLE translation_logs ADD COLUMN estimated_cost_savings DECIMAL(10,6) DEFAULT 0.000000 AFTER cache_hit_rate',
    'SELECT ''Column estimated_cost_savings already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add indexes for cache performance analysis (with IF NOT EXISTS checks)
SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND index_name = 'idx_api_used') = 0,
    'ALTER TABLE translation_logs ADD INDEX idx_api_used (api_used)',
    'SELECT ''Index idx_api_used already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND index_name = 'idx_cache_performance') = 0,
    'ALTER TABLE translation_logs ADD INDEX idx_cache_performance (cache_hit_rate, created_at)',
    'SELECT ''Index idx_cache_performance already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

SET @sql = (SELECT IF(
    (SELECT COUNT(*) FROM INFORMATION_SCHEMA.STATISTICS
     WHERE table_name = 'translation_logs'
     AND table_schema = DATABASE()
     AND index_name = 'idx_cost_savings') = 0,
    'ALTER TABLE translation_logs ADD INDEX idx_cost_savings (estimated_cost_savings)',
    'SELECT ''Index idx_cost_savings already exists'' as message'
));
PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Create a view for cache performance analytics
CREATE OR REPLACE VIEW cache_performance_summary AS
SELECT 
    DATE(created_at) as date,
    api_used,
    COUNT(*) as total_requests,
    SUM(cache_hit_tokens) as total_cache_hits,
    SUM(cache_miss_tokens) as total_cache_misses,
    SUM(total_tokens) as total_tokens_used,
    ROUND(AVG(cache_hit_rate), 2) as avg_cache_hit_rate,
    SUM(estimated_cost_savings) as total_cost_savings,
    SUM(translation_time_seconds) as total_translation_time
FROM translation_logs 
WHERE api_used = 'deepseek' 
  AND status = 'success'
  AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), api_used
ORDER BY date DESC;

-- Create a view for novel-specific cache performance
CREATE OR REPLACE VIEW novel_cache_performance AS
SELECT 
    n.id as novel_id,
    n.original_title,
    n.translated_title,
    COUNT(tl.id) as total_translations,
    SUM(tl.cache_hit_tokens) as total_cache_hits,
    SUM(tl.cache_miss_tokens) as total_cache_misses,
    ROUND(AVG(tl.cache_hit_rate), 2) as avg_cache_hit_rate,
    SUM(tl.estimated_cost_savings) as total_cost_savings,
    SUM(tl.translation_time_seconds) as total_translation_time
FROM novels n
LEFT JOIN translation_logs tl ON n.id = tl.novel_id
WHERE tl.api_used = 'deepseek' 
  AND tl.status = 'success'
  AND tl.created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY n.id, n.original_title, n.translated_title
HAVING total_translations > 0
ORDER BY total_cost_savings DESC;

-- Update existing records to set default values
UPDATE translation_logs 
SET api_used = 'deepseek',
    cache_hit_tokens = 0,
    cache_miss_tokens = 0,
    total_tokens = COALESCE(api_tokens_used, 0),
    cache_hit_rate = 0.00,
    estimated_cost_savings = 0.000000
WHERE api_used IS NULL;

-- Add a configuration table for cache optimization settings
CREATE TABLE IF NOT EXISTS cache_optimization_config (
    id INT AUTO_INCREMENT PRIMARY KEY,
    config_key VARCHAR(100) NOT NULL UNIQUE,
    config_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default cache optimization settings
INSERT INTO cache_optimization_config (config_key, config_value, description) VALUES
('enable_prompt_optimization', 'true', 'Enable prompt structure optimization for better cache hits'),
('cache_friendly_system_prompt', 'true', 'Use consistent system prompts to improve cache efficiency'),
('context_reordering', 'true', 'Reorder context elements to maximize cache prefix matching'),
('cache_performance_logging', 'true', 'Log detailed cache performance metrics'),
('min_cache_hit_rate_threshold', '20.0', 'Minimum cache hit rate to consider optimization successful'),
('cost_savings_alert_threshold', '0.01', 'Alert when daily cost savings exceed this amount (USD)')
ON DUPLICATE KEY UPDATE 
    config_value = VALUES(config_value),
    description = VALUES(description),
    updated_at = CURRENT_TIMESTAMP;
