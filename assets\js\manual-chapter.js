/**
 * Manual Chapter Entry JavaScript
 * Handles manual chapter creation form functionality
 */

class ManualChapterEntry {
    constructor() {
        this.form = document.getElementById('manual-chapter-form');
        this.messageContainer = document.getElementById('message-container');
        this.saveAndAddAnotherMode = false;
        
        this.initializeEventListeners();
        this.updateContentStats();
    }

    initializeEventListeners() {
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.saveAndAddAnotherMode = false;
                this.submitChapter();
            });
        }

        // Save and add another button
        const saveAndAddBtn = document.getElementById('save-and-add-another');
        if (saveAndAddBtn) {
            saveAndAddBtn.addEventListener('click', () => {
                this.saveAndAddAnotherMode = true;
                this.submitChapter();
            });
        }

        // Content statistics and chunking info
        const originalContent = document.getElementById('original-content');
        if (originalContent) {
            originalContent.addEventListener('input', () => {
                this.updateContentStats();
            });
        }

        // Auto-translate title if original is provided
        const originalTitle = document.getElementById('original-title');
        const translatedTitle = document.getElementById('translated-title');
        
        if (originalTitle && translatedTitle) {
            let titleTranslateTimeout;
            originalTitle.addEventListener('input', () => {
                clearTimeout(titleTranslateTimeout);
                titleTranslateTimeout = setTimeout(() => {
                    this.suggestTitleTranslation();
                }, 1500);
            });
        }

        // Auto-increment chapter number
        this.setupChapterNumbering();
    }

    setupChapterNumbering() {
        const chapterNumber = document.getElementById('chapter-number');
        if (chapterNumber) {
            // Validate chapter number doesn't already exist
            chapterNumber.addEventListener('change', () => {
                this.validateChapterNumber();
            });
        }
    }

    async validateChapterNumber() {
        const novelId = document.getElementById('novel-id').value;
        const chapterNumber = document.getElementById('chapter-number').value;
        
        if (!chapterNumber || !novelId) return;

        try {
            const response = await utils.makeApiRequest(`api/chapters.php?novel_id=${novelId}&chapter_number=${chapterNumber}`);
            
            if (response.success && response.data) {
                // Chapter exists
                this.showWarning(`Chapter ${chapterNumber} already exists for this novel.`);
            }
        } catch (error) {
            // Chapter doesn't exist (404 error is expected)
            this.clearWarnings();
        }
    }

    updateContentStats() {
        const originalContent = document.getElementById('original-content');
        const contentStats = document.getElementById('content-stats');
        const chunkingInfo = document.getElementById('chunking-info');
        
        if (!originalContent || !contentStats) return;

        const content = originalContent.value;
        const charCount = content.length;
        const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
        
        contentStats.textContent = `Characters: ${charCount.toLocaleString()} | Words: ${wordCount.toLocaleString()}`;
        
        // Show chunking information
        if (chunkingInfo) {
            if (charCount > 5000) {
                const estimatedChunks = Math.ceil(charCount / 5000);
                chunkingInfo.innerHTML = `<span class="text-warning"><i class="fas fa-cut me-1"></i>Will be chunked into ~${estimatedChunks} parts</span>`;
            } else {
                chunkingInfo.innerHTML = `<span class="text-success"><i class="fas fa-check me-1"></i>No chunking needed</span>`;
            }
        }
    }

    async submitChapter() {
        const submitBtn = this.form.querySelector('button[type="submit"]');
        const saveAndAddBtn = document.getElementById('save-and-add-another');
        const originalSubmitText = submitBtn.innerHTML;
        const originalSaveAddText = saveAndAddBtn.innerHTML;
        
        try {
            // Show loading state
            submitBtn.disabled = true;
            saveAndAddBtn.disabled = true;
            
            if (this.saveAndAddAnotherMode) {
                saveAndAddBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
            } else {
                submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving Chapter...';
            }
            
            // Collect form data
            const formData = {
                novel_id: parseInt(document.getElementById('novel-id').value),
                chapter_number: parseInt(document.getElementById('chapter-number').value),
                original_title: document.getElementById('original-title').value.trim(),
                translated_title: document.getElementById('translated-title').value.trim() || null,
                original_content: document.getElementById('original-content').value.trim(),
                translated_content: document.getElementById('translated-content').value.trim() || null
            };

            // Validate required fields
            if (!formData.original_title) {
                throw new Error('Original chapter title is required');
            }
            if (!formData.original_content) {
                throw new Error('Original chapter content is required');
            }

            // Submit to API
            const response = await utils.makeApiRequest('api/chapters.php', {
                method: 'POST',
                body: JSON.stringify(formData)
            });

            if (response.success) {
                const chunkingMsg = response.data.chunking_result && response.data.chunking_result.success 
                    ? ` (${response.data.chunking_result.chunks_created} chunks created)`
                    : '';
                
                this.showMessage('success', 'Chapter Saved!', 
                    `Chapter ${formData.chapter_number} has been saved successfully${chunkingMsg}.`);
                
                if (this.saveAndAddAnotherMode) {
                    // Reset form for next chapter
                    this.resetFormForNext();
                } else {
                    // Redirect to novel details
                    setTimeout(() => {
                        window.location.href = `novel-details.php?id=${formData.novel_id}`;
                    }, 2000);
                }
            } else {
                throw new Error(response.error || 'Failed to save chapter');
            }

        } catch (error) {
            console.error('Manual chapter save error:', error);
            this.showMessage('error', 'Save Failed', error.message);
        } finally {
            // Restore button states
            submitBtn.disabled = false;
            saveAndAddBtn.disabled = false;
            submitBtn.innerHTML = originalSubmitText;
            saveAndAddBtn.innerHTML = originalSaveAddText;
        }
    }

    resetFormForNext() {
        // Increment chapter number
        const chapterNumber = document.getElementById('chapter-number');
        chapterNumber.value = parseInt(chapterNumber.value) + 1;
        
        // Clear form fields except novel ID
        document.getElementById('original-title').value = '';
        document.getElementById('translated-title').value = '';
        document.getElementById('original-content').value = '';
        document.getElementById('translated-content').value = '';
        
        // Update stats
        this.updateContentStats();
        
        // Focus on title field
        document.getElementById('original-title').focus();
    }

    async suggestTitleTranslation() {
        const originalTitle = document.getElementById('original-title').value.trim();
        const translatedTitle = document.getElementById('translated-title');
        
        if (!originalTitle || translatedTitle.value.trim()) {
            return; // Don't translate if original is empty or translated already has content
        }

        try {
            const response = await utils.makeApiRequest('api/translate.php', {
                method: 'POST',
                body: JSON.stringify({
                    text: originalTitle,
                    target_language: 'en',
                    type: 'title'
                })
            });

            if (response.success && response.translated_text) {
                translatedTitle.value = response.translated_text;
                translatedTitle.style.backgroundColor = '#e8f5e8'; // Light green to indicate auto-filled
                setTimeout(() => {
                    translatedTitle.style.backgroundColor = '';
                }, 2000);
            }
        } catch (error) {
            console.log('Title translation suggestion failed:', error.message);
            // Fail silently for suggestions
        }
    }

    showMessage(type, title, message) {
        const alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
        const icon = type === 'success' ? 'check-circle' : 'exclamation-triangle';
        
        this.messageContainer.innerHTML = `
            <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                <i class="fas fa-${icon} me-2"></i>
                <strong>${title}</strong> ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        `;
        
        this.messageContainer.style.display = 'block';
        this.messageContainer.scrollIntoView({ behavior: 'smooth', block: 'center' });
    }

    showWarning(message) {
        const chapterNumber = document.getElementById('chapter-number');
        let warningDiv = document.getElementById('chapter-warning');
        
        if (!warningDiv) {
            warningDiv = document.createElement('div');
            warningDiv.id = 'chapter-warning';
            warningDiv.className = 'alert alert-warning mt-2';
            chapterNumber.parentNode.appendChild(warningDiv);
        }
        
        warningDiv.innerHTML = `<i class="fas fa-exclamation-triangle me-2"></i>${message}`;
        warningDiv.style.display = 'block';
    }

    clearWarnings() {
        const warningDiv = document.getElementById('chapter-warning');
        if (warningDiv) {
            warningDiv.style.display = 'none';
        }
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new ManualChapterEntry();
});
