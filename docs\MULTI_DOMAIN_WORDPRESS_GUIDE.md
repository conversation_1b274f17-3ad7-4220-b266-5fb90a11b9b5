# Multi-Domain WordPress Integration Guide

This guide explains how to manage multiple WordPress domains and multiple novels in your novel translation application.

## Overview

The enhanced WordPress integration now supports:

✅ **Multiple novels per WordPress site** - Organize different novels on the same domain  
✅ **Multiple WordPress domains** - Post different novels to different websites  
✅ **Domain tracking** - Track which content was posted where  
✅ **Duplicate prevention** - Prevent posting the same content twice to the same domain  
✅ **Cross-domain posting** - Post the same novel to multiple domains if needed  

## How It Works

### **Domain Tracking System**
- Each WordPress post is now tracked with its destination domain
- The same novel/chapter can be posted to multiple domains
- Duplicate prevention works per-domain (not globally)
- Posting history shows which domains have your content

### **Database Changes**
The `wordpress_posts` table now includes:
- `wordpress_domain` - Tracks which domain the post was sent to
- Updated unique constraints to allow same content on different domains
- Enhanced status tracking per domain

## Setup Instructions

### **1. Run the Migration**
Before using multi-domain features, run the migration:

```bash
cd migrations
php add_wordpress_domain_tracking.php
```

This will:
- Add domain tracking to existing posts
- Update database constraints
- Preserve your existing WordPress posts

### **2. Configure Your First Domain**
1. Go to **Settings** page
2. Configure WordPress integration for your first domain:
   - **Site URL**: `https://domain1.com`
   - **Username**: Your WordPress username
   - **App Password**: Your WordPress application password
   - Configure post types and other settings
3. Test the connection
4. Post your novels and chapters

### **3. Add Additional Domains**
To post to additional domains:

1. **Change WordPress settings** for the new domain:
   - Update **Site URL** to `https://domain2.com`
   - Update **Username** and **App Password** for the new domain
   - Adjust post types if different
2. **Post content** - The system will recognize this as a new domain
3. **Repeat** for each additional domain

## Multi-Domain Workflows

### **Scenario 1: Different Novels on Different Domains**

**Goal**: Novel A on domain1.com, Novel B on domain2.com

**Steps**:
1. Configure settings for domain1.com
2. Post Novel A and its chapters
3. Change settings to domain2.com  
4. Post Novel B and its chapters

### **Scenario 2: Same Novel on Multiple Domains**

**Goal**: Novel A on both domain1.com and domain2.com

**Steps**:
1. Configure settings for domain1.com
2. Post Novel A and its chapters
3. Change settings to domain2.com
4. Post Novel A and its chapters again (system allows this)

### **Scenario 3: Mixed Content Distribution**

**Goal**: Some novels on multiple domains, others on single domains

**Steps**:
1. Use the workflow above for each novel
2. Check the **Multi-Domain Statistics** in Settings to track distribution
3. Switch between domains as needed

## Settings Page Features

### **Multi-Domain Statistics Dashboard**
The Settings page now shows:
- **Current Domain**: Which domain you're currently configured for
- **Posting Statistics**: How many novels/chapters posted to each domain
- **Domain List**: All domains you've posted content to

### **Configuration Switching**
- Change the **Site URL** to switch between domains
- Update credentials for each domain
- Test connection before posting

## Best Practices

### **Organization Tips**
1. **Document your domains**: Keep a list of which novels go to which domains
2. **Use consistent naming**: Use similar post types across domains for consistency
3. **Test connections**: Always test WordPress connection after changing domains
4. **Check statistics**: Use the statistics dashboard to track your content distribution

### **Content Management**
1. **Post novels first**: Always post the novel page before chapters
2. **Batch posting**: Post all chapters for a domain at once to minimize configuration switching
3. **Verify posts**: Check the WordPress sites to ensure content posted correctly

### **Security Considerations**
1. **Separate credentials**: Use different WordPress users for different domains if possible
2. **Application passwords**: Use WordPress Application Passwords (not regular passwords)
3. **Regular updates**: Keep WordPress sites updated for security

## Troubleshooting

### **Common Issues**

**"Novel already posted to WordPress domain"**
- This means the novel is already posted to the current domain
- Switch to a different domain to post there
- Or this is expected behavior preventing duplicates

**"Failed to post novel first"**
- The novel must be posted before chapters
- Post the novel first, then post chapters

**Connection errors**
- Verify the Site URL is correct for the current domain
- Check username and application password
- Ensure the WordPress site is accessible

### **Migration Issues**

**Missing wordpress_domain column**
- Run the migration script: `php migrations/add_wordpress_domain_tracking.php`
- This adds domain tracking to existing installations

**Constraint errors**
- The migration updates database constraints
- Backup your database before running migrations
- Contact support if issues persist

## Advanced Features

### **Bulk Domain Operations**
For advanced users, you can:
- Query the database to see all domain distributions
- Export content lists per domain
- Batch update domain assignments (advanced SQL required)

### **API Integration**
The WordPress API endpoints now support domain-aware operations:
- Status checks return domain-specific information
- Posting operations track domains automatically
- Enhanced error messages include domain context

## Future Enhancements

Planned features for future versions:
- **Domain profiles**: Save multiple WordPress configurations
- **Automatic domain switching**: Assign novels to specific domains
- **Bulk posting**: Post to multiple domains simultaneously
- **Content synchronization**: Keep content updated across domains

## Support

For issues with multi-domain setup:
1. Check this guide first
2. Verify your WordPress configurations
3. Test with a single domain before adding multiple domains
4. Check the application logs for detailed error messages

---

**Note**: This enhanced system maintains backward compatibility. Existing single-domain setups continue to work without changes.
