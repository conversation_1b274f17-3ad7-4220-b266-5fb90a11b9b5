/**
 * Dashboard JavaScript
 * Handles novel preview and dashboard functionality
 */

class Dashboard {
    constructor() {
        this.currentNovelData = null;
        this.initializeEventListeners();
        this.loadNovels();
    }

    initializeEventListeners() {
        // Preview form submission
        const previewForm = document.getElementById('preview-form');
        if (previewForm) {
            previewForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.previewNovel();
            });
        }

        // Save novel button
        const saveNovelBtn = document.getElementById('save-novel-btn');
        if (saveNovelBtn) {
            saveNovelBtn.addEventListener('click', () => this.saveNovel());
        }

        // Cancel preview button
        const cancelPreviewBtn = document.getElementById('cancel-preview-btn');
        if (cancelPreviewBtn) {
            cancelPreviewBtn.addEventListener('click', () => this.cancelPreview());
        }

        // Refresh novels button
        const refreshBtn = document.getElementById('refresh-novels-btn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadNovels());
        }
    }

    async previewNovel() {
        const url = document.getElementById('novel-url').value.trim();
        
        if (!url) {
            utils.showToast('Please enter a novel URL', 'warning');
            return;
        }

        utils.showLoading(true);

        try {
            const result = await utils.makeApiRequest(`api/preview.php?url=${encodeURIComponent(url)}`);

            if (result.success && result.data) {
                // Validate the response structure
                if (!result.data.novel_info) {
                    console.error('Dashboard: Invalid API response structure:', result);
                    utils.showToast('Invalid response from server - missing novel information', 'error');
                    return;
                }

                this.currentNovelData = result.data;
                this.displayPreview(result.data);
                utils.showToast('Novel preview loaded successfully', 'success');
            } else {
                const errorMsg = result.error || 'Failed to preview novel';
                console.error('Dashboard: Preview failed:', result);

                // Provide more user-friendly error messages
                let userFriendlyMsg = errorMsg;
                if (errorMsg.includes('Access denied') || errorMsg.includes('403')) {
                    userFriendlyMsg = 'Access denied by the novel site. This may be due to anti-bot protection. Please try again later or check if the URL is correct.';
                } else if (errorMsg.includes('not found') || errorMsg.includes('404')) {
                    userFriendlyMsg = 'Novel not found. Please check if the URL is correct and the novel still exists on the site.';
                } else if (errorMsg.includes('blocking automated requests')) {
                    userFriendlyMsg = 'The site is currently blocking automated requests. Please try again in a few minutes.';
                } else if (errorMsg.includes('timeout')) {
                    userFriendlyMsg = 'Request timeout. The novel site may be slow or temporarily unavailable. Please try again.';
                }

                utils.showToast(userFriendlyMsg, 'error');
            }
        } catch (error) {
            console.error('Dashboard: Preview error:', error);
            // Provide more specific error messages based on error type
            if (error.message.includes('fetch')) {
                utils.showToast('Network connection error - please check your internet connection', 'error');
            } else if (error.message.includes('JSON')) {
                utils.showToast('Server response error - invalid data format', 'error');
            } else {
                utils.showToast('An unexpected error occurred while previewing the novel', 'error');
            }
        } finally {
            utils.showLoading(false);
        }
    }

    displayPreview(data) {
        // Fix data structure mismatch - API returns novel_info, not novel
        const { novel_info: novel, chapters } = data;

        // Validate required data exists
        if (!novel) {
            console.error('Dashboard: No novel data in preview response:', data);
            utils.showToast('Invalid preview data received', 'error');
            return;
        }

        if (!Array.isArray(chapters)) {
            console.error('Dashboard: No chapters data in preview response:', data);
            utils.showToast('Invalid chapters data received', 'error');
            return;
        }

        // Ensure required novel properties exist with fallbacks
        const novelTitle = novel.original_title || 'Unknown Title';
        const novelAuthor = novel.author || 'Unknown Author';
        const novelPlatform = novel.platform || 'unknown';
        const novelDate = novel.publication_date || null;

        const infoHtml = `
            <div class="novel-preview">
                <div class="novel-header">
                    <div class="novel-title">${utils.escapeHtml(novelTitle)}</div>
                    <div class="novel-meta">
                        <div class="novel-meta-item">
                            <i class="fas fa-user"></i>
                            <span>${utils.escapeHtml(novelAuthor)}</span>
                        </div>
                        <div class="novel-meta-item">
                            <i class="fas fa-globe"></i>
                            <span class="platform-badge platform-${novelPlatform}">
                                ${utils.getPlatformName(novelPlatform)}
                            </span>
                        </div>
                        <div class="novel-meta-item">
                            <i class="fas fa-calendar"></i>
                            <span>${utils.formatDate(novelDate)}</span>
                        </div>
                        <div class="novel-meta-item">
                            <i class="fas fa-list"></i>
                            <span>${chapters.length} chapters</span>
                        </div>
                    </div>
                </div>

                <div class="novel-description">
                    <h6><i class="fas fa-info-circle me-2"></i>Description</h6>
                    <p>${utils.escapeHtml(novel.original_synopsis || novel.translated_synopsis || 'No description available')}</p>
                </div>

                <div class="chapter-preview">
                    <div class="p-3 bg-light border-bottom">
                        <h6 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            Chapter List (${chapters.length} chapters${data.total_chapters_limited ? ` - showing ${data.chapters_shown} of ${data.total_chapters_available} total` : ''})
                        </h6>
                        ${data.total_chapters_limited ? `
                            <small class="text-warning">
                                <i class="fas fa-info-circle me-1"></i>
                                Preview limited to ${data.chapters_shown} chapters. All chapters will be available after saving.
                            </small>
                        ` : ''}
                    </div>
                    <div class="chapter-preview-list">
                        ${chapters.slice(0, 20).map(chapter => {
                            // Ensure chapter has required properties with fallbacks
                            const chapterNumber = chapter.chapter_number || 'N/A';
                            const chapterTitle = chapter.original_title || chapter.translated_title || 'Untitled Chapter';

                            return `
                                <div class="chapter-preview-item">
                                    <div class="chapter-number">Ch. ${chapterNumber}</div>
                                    <div class="chapter-title">${utils.escapeHtml(chapterTitle)}</div>
                                    <div class="chapter-status status-preview">Preview</div>
                                </div>
                            `;
                        }).join('')}
                        ${chapters.length > 20 ? `
                            <div class="chapter-preview-item text-muted">
                                <div class="text-center">
                                    <i class="fas fa-ellipsis-h"></i>
                                    <span>And ${chapters.length - 20} more chapters...</span>
                                </div>
                            </div>
                        ` : ''}
                    </div>
                </div>
            </div>
        `;

        document.getElementById('novel-info').innerHTML = infoHtml;
        document.getElementById('preview-results').style.display = 'block';
        
        // Scroll to results
        utils.scrollToElement('preview-results', 100);
    }

    async saveNovel() {
        if (!this.currentNovelData) {
            utils.showToast('No novel data to save', 'warning');
            return;
        }

        // Validate that we have the required data structure
        if (!this.currentNovelData.novel_info) {
            console.error('Dashboard: Invalid novel data structure for saving:', this.currentNovelData);
            utils.showToast('Invalid novel data - cannot save', 'error');
            return;
        }

        utils.showLoading(true);

        try {
            // Transform the data structure to match what the API expects
            // The preview API returns { novel_info: {...}, chapters: [...] }
            // But the save API expects a flat structure with novel properties at the top level
            const { novel_info, chapters } = this.currentNovelData;

            const saveData = {
                // Required fields
                url: novel_info.url,
                platform: novel_info.platform,
                original_title: novel_info.original_title,

                // Optional fields
                translated_title: novel_info.translated_title || null,
                author: novel_info.author || null,
                publication_date: novel_info.publication_date || null,
                total_chapters: chapters ? chapters.length : 0,
                original_synopsis: novel_info.original_synopsis || null,
                translated_synopsis: novel_info.translated_synopsis || null,
                chapters: chapters || []
            };

            console.log('Dashboard: Sending save data:', saveData);

            const result = await utils.makeApiRequest('api/novels.php', {
                method: 'POST',
                body: JSON.stringify(saveData)
            });

            if (result.success) {
                utils.showToast('Novel saved successfully', 'success');
                this.cancelPreview();
                this.loadNovels(); // Refresh the novels list
            } else {
                utils.showToast(result.error || 'Failed to save novel', 'error');
            }
        } catch (error) {
            console.error('Dashboard: Save error:', error);
            if (error.message.includes('fetch')) {
                utils.showToast('Network connection error - please check your internet connection', 'error');
            } else if (error.message.includes('JSON')) {
                utils.showToast('Server response error - invalid data format', 'error');
            } else {
                utils.showToast('An unexpected error occurred while saving the novel', 'error');
            }
        } finally {
            utils.showLoading(false);
        }
    }

    cancelPreview() {
        document.getElementById('preview-results').style.display = 'none';
        document.getElementById('novel-url').value = '';
        this.currentNovelData = null;
        utils.scrollToElement('preview-section', 100);
    }

    async loadNovels() {
        const novelsContainer = document.getElementById('novels-list');
        if (!novelsContainer) return;

        try {
            const result = await utils.makeApiRequest('api/novels.php');
            console.log('Dashboard API Response:', result); // Debug log

            if (result.success && result.data) {
                console.log('Dashboard Data structure:', result.data); // Debug log

                // Extract novels array safely - handle different possible structures
                let novels = [];
                if (Array.isArray(result.data.novels)) {
                    novels = result.data.novels;
                } else if (Array.isArray(result.data)) {
                    // Fallback: if data is directly an array
                    novels = result.data;
                } else {
                    console.warn('Dashboard: Unexpected data structure, using empty array');
                }

                console.log('Dashboard Extracted novels:', novels); // Debug log
                this.displayNovels(novels);
            } else {
                console.error('Dashboard Invalid API response:', result);
                novelsContainer.innerHTML = `
                    <div class="text-center text-danger">
                        <i class="fas fa-exclamation-triangle fa-2x mb-3"></i>
                        <p>Failed to load novels</p>
                    </div>
                `;
            }
        } catch (error) {
            console.error('Load novels error:', error);
            novelsContainer.innerHTML = `
                <div class="text-center text-danger">
                    <i class="fas fa-wifi fa-2x mb-3"></i>
                    <p>Network error occurred</p>
                </div>
            `;
        }
    }

    displayNovels(novels) {
        const novelsContainer = document.getElementById('novels-list');

        // Ensure novels is an array
        if (!Array.isArray(novels)) {
            console.error('Dashboard displayNovels: novels is not an array:', novels);
            novels = [];
        }

        if (novels.length === 0) {
            novelsContainer.innerHTML = `
                <div class="text-center text-muted py-4">
                    <i class="fas fa-book fa-3x mb-3"></i>
                    <h5>No novels saved yet</h5>
                    <p>Start by previewing and saving a novel from the section above.</p>
                </div>
            `;
            return;
        }

        // Limit to first 5 novels for dashboard display
        const displayNovels = novels.slice(0, 5);

        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-striped novels-table">
                    <thead>
                        <tr>
                            <th>Novel</th>
                            <th>Author</th>
                            <th>Platform</th>
                            <th>Progress</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${displayNovels.map(novel => {
                            const progress = utils.calculateProgress(novel.translated_chapters, novel.total_chapters);

                            return `
                                <tr>
                                    <td>
                                        <div class="novel-title-cell">
                                            <div class="novel-title">${utils.escapeHtml(novel.translated_title || novel.original_title)}</div>
                                            ${(novel.translated_synopsis || novel.original_synopsis) ? `
                                                <div class="novel-synopsis-preview">
                                                    ${utils.escapeHtml((novel.translated_synopsis || novel.original_synopsis).substring(0, 80))}${(novel.translated_synopsis || novel.original_synopsis).length > 80 ? '...' : ''}
                                                </div>
                                            ` : ''}
                                        </div>
                                    </td>
                                    <td>${utils.escapeHtml(novel.author || 'Unknown')}</td>
                                    <td>
                                        <span class="platform-badge platform-${novel.platform}">
                                            ${utils.getPlatformName(novel.platform)}
                                        </span>
                                    </td>
                                    <td>
                                        <div class="progress-cell">
                                            <div class="progress-info">
                                                <small>${novel.translated_chapters}/${novel.total_chapters} (${progress}%)</small>
                                            </div>
                                            <div class="progress" style="height: 4px;">
                                                <div class="progress-bar" style="width: ${progress}%"></div>
                                            </div>
                                        </div>
                                    </td>
                                    <td>
                                        <a href="novel-details.php?id=${novel.id}" class="btn btn-primary btn-sm">View</a>
                                    </td>
                                </tr>
                            `;
                        }).join('')}
                    </tbody>
                </table>
            </div>
            ${novels.length > 5 ? `
                <div class="text-center mt-2">
                    <a href="novels.php" class="btn btn-outline-primary btn-sm">View All ${novels.length} Novels</a>
                </div>
            ` : ''}
        `;

        novelsContainer.innerHTML = tableHtml;
    }
}

// Initialize dashboard when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new Dashboard();
});
