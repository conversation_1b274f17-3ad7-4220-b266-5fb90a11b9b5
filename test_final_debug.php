<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "=== Final Debug Test ===\n";
echo "URL: $chapterUrl\n\n";

try {
    $chapterData = $crawler->getChapterContent($chapterUrl);
    $content = $chapterData['original_content'];
    
    echo "Extracted content:\n";
    echo "Length: " . strlen($content) . " bytes\n";
    echo "Content:\n";
    echo "---\n";
    echo $content . "\n";
    echo "---\n";
    
    echo "\nHex dump of content:\n";
    echo bin2hex($content) . "\n";
    
    // Now let's manually extract the content to see what should be there
    echo "\n=== Manual Extraction ===\n";
    
    // Get raw HTML and convert encoding
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $chapterUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    
    $rawHtml = curl_exec($ch);
    curl_close($ch);
    
    // Convert from GBK to UTF-8
    $html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
    $html = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $html);
    
    // Parse with DOMDocument
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
    libxml_clear_errors();
    
    $allText = $dom->textContent;
    
    // Try to find the actual story content using a simple approach
    // Look for content that comes after the chapter title and date
    if (preg_match('/第1章\s+怒气.*?\d{4}-\d{1,2}-\d{1,2}.*?第1章\s+怒气(.+?)(?:上一章|下一章|目录|$)/s', $allText, $matches)) {
        $storyContent = trim($matches[1]);
        
        // Remove JavaScript and navigation
        $storyContent = preg_replace('/var\s+bookinfo[^}]+}/s', '', $storyContent);
        $storyContent = preg_replace('/\n{3,}/', "\n\n", $storyContent);
        $storyContent = trim($storyContent);
        
        echo "Manual extraction successful!\n";
        echo "Length: " . strlen($storyContent) . " bytes\n";
        echo "Chinese chars: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $storyContent) . "\n";
        echo "First 1000 characters:\n";
        echo "---\n";
        echo mb_substr($storyContent, 0, 1000, 'UTF-8') . "\n";
        echo "---\n";
    } else {
        echo "Manual extraction pattern failed\n";
        echo "Looking for simpler patterns...\n";
        
        // Try a simpler pattern
        if (preg_match('/怒气(.{500,})/s', $allText, $matches)) {
            $content = trim($matches[1]);
            echo "Found content after '怒气':\n";
            echo "Length: " . strlen($content) . "\n";
            echo "First 500 chars:\n";
            echo substr($content, 0, 500) . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
