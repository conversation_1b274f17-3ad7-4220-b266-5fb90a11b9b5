<?php
// Test raw HTML extraction and encoding conversion

$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "Testing raw HTML extraction and encoding conversion\n";
echo "URL: $chapterUrl\n\n";

// Get raw HTML
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $chapterUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_ENCODING, '');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding: gzip, deflate, br',
    'Referer: https://69shuba.cx/book/44425.htm'
]);

$rawHtml = curl_exec($ch);
curl_close($ch);

echo "Raw HTML length: " . strlen($rawHtml) . "\n";
echo "Raw HTML encoding: " . mb_detect_encoding($rawHtml) . "\n";

// Check for charset in meta tag
if (preg_match('/<meta[^>]*charset[^>]*=[\'"]*([^\'"\s>]+)/i', $rawHtml, $matches)) {
    $metaCharset = trim($matches[1]);
    echo "Meta charset: $metaCharset\n";
} else {
    echo "No meta charset found\n";
}

// Convert from GBK to UTF-8
echo "\nConverting from GBK to UTF-8...\n";
$convertedHtml = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
echo "Converted HTML length: " . strlen($convertedHtml) . "\n";
echo "Converted HTML encoding: " . mb_detect_encoding($convertedHtml) . "\n";
echo "Valid UTF-8: " . (mb_check_encoding($convertedHtml, 'UTF-8') ? 'Yes' : 'No') . "\n";

// Update meta charset
$convertedHtml = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $convertedHtml);

// Parse with DOMDocument
echo "\nParsing with DOMDocument...\n";
$dom = new DOMDocument();
libxml_use_internal_errors(true);
$success = $dom->loadHTML($convertedHtml, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
libxml_clear_errors();

if ($success) {
    echo "DOM parsing successful\n";
    
    // Extract text content
    $textContent = $dom->textContent;
    echo "Text content length: " . strlen($textContent) . "\n";
    echo "Text content encoding: " . mb_detect_encoding($textContent) . "\n";
    echo "Valid UTF-8: " . (mb_check_encoding($textContent, 'UTF-8') ? 'Yes' : 'No') . "\n";
    
    $chineseCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $textContent);
    echo "Chinese character count: $chineseCount\n";
    
    if ($chineseCount > 0) {
        echo "\nFirst 500 characters of text content:\n";
        echo "---\n";
        echo mb_substr($textContent, 0, 500, 'UTF-8') . "\n";
        echo "---\n";
    }
} else {
    echo "DOM parsing failed\n";
}
?>
