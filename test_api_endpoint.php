<?php
/**
 * Test the actual API endpoint to reproduce the JSON error
 */

// Simulate the exact API request
$testData = [
    'novel_id' => 1,
    'chapter_number' => 2
];

$jsonData = json_encode($testData);

// Create a temporary file to simulate php://input
$tempFile = tempnam(sys_get_temp_dir(), 'api_test');
file_put_contents($tempFile, $jsonData);

// Set up environment to simulate the API request
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// Capture all output
ob_start();

// Override php://input for this test
$originalInput = 'php://input';

// We need to test the API file directly
// But first, let's capture any errors or unexpected output

echo "=== API Endpoint Test ===\n";
echo "Testing with data: " . $jsonData . "\n\n";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

try {
    // Include the API file and capture its output
    echo "Including API file...\n";
    
    // We need to simulate the POST data
    // Create a stream context to override php://input
    $context = stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => 'Content-Type: application/json',
            'content' => $jsonData
        ]
    ]);
    
    // Capture the output from the API
    ob_start();
    
    // We'll test the handleSaveChapter function directly instead
    require_once 'config/config.php';
    
    // Test the function that the API calls
    echo "Testing handleSaveChapter function...\n";
    
    // Create a mock input
    $GLOBALS['mock_input'] = $jsonData;
    
    // Override file_get_contents for php://input
    function mock_file_get_contents($filename) {
        if ($filename === 'php://input') {
            return $GLOBALS['mock_input'];
        }
        return file_get_contents($filename);
    }
    
    // Test the API logic
    $novelManager = new NovelManager();
    
    // Simulate the exact logic from handleSaveChapter
    $input = json_decode($jsonData, true);
    
    if (!$input) {
        echo "ERROR: Invalid JSON input\n";
        exit(1);
    }
    
    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        echo "ERROR: Valid novel_id is required\n";
        exit(1);
    }
    
    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        echo "ERROR: Valid chapter_number is required\n";
        exit(1);
    }
    
    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];
    
    echo "Processing save for novel {$novelId}, chapter {$chapterNumber}\n";
    
    // Check if this is a manual chapter creation
    if (isset($input['original_content']) && !empty(trim($input['original_content']))) {
        echo "Manual chapter creation\n";
        $result = $novelManager->addManualChapter($novelId, $input);
    } else {
        echo "Regular chapter save (crawled content)\n";
        $result = $novelManager->saveChapter($novelId, $chapterNumber);
    }
    
    echo "Save result received\n";
    
    // Test JSON encoding
    $jsonResponse = json_encode([
        'success' => $result['success'],
        'data' => $result
    ]);
    
    if ($jsonResponse === false) {
        echo "ERROR: Failed to encode JSON response\n";
        echo "JSON error: " . json_last_error_msg() . "\n";
        echo "Result data: " . print_r($result, true) . "\n";
    } else {
        echo "JSON response generated successfully\n";
        echo "Response length: " . strlen($jsonResponse) . " characters\n";
        echo "Response preview: " . substr($jsonResponse, 0, 200) . "...\n";
    }
    
} catch (Exception $e) {
    echo "EXCEPTION: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "FATAL ERROR: " . $e->getMessage() . "\n";
    echo "Stack trace: " . $e->getTraceAsString() . "\n";
}

$output = ob_get_clean();
echo $output;

echo "\n=== Test Complete ===\n";
?>
