<?php
/**
 * Test script to verify name dictionary integration in DeepSeek API translation
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';
require_once 'classes/DeepSeekTranslationService.php';

// Test configuration
$novelId = 6; // Use an existing novel with name dictionary entries
$testText = "赵立看着朱辞夏，然后对林游说话。"; // Simple test text with character names

echo "=== Name Dictionary Integration Test ===\n\n";

try {
    $db = Database::getInstance();
    $translationService = new TranslationService();
    
    // Get name dictionary for the novel
    $nameDictionary = $translationService->getNameDictionary($novelId);
    echo "Name Dictionary Entries: " . count($nameDictionary) . "\n";
    
    if (empty($nameDictionary)) {
        echo "ERROR: No name dictionary entries found for novel {$novelId}\n";
        exit(1);
    }
    
    // Display first few entries
    echo "\nFirst 5 name dictionary entries:\n";
    for ($i = 0; $i < min(5, count($nameDictionary)); $i++) {
        $name = $nameDictionary[$i];
        $targetName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
        $source = $name['translation'] ? 'translation' : ($name['romanization'] ? 'romanization' : 'original');
        echo "  - {$name['original_name']} → {$targetName} (using {$source})\n";
    }
    
    // Test DeepSeek translation with name dictionary
    echo "\n=== Testing DeepSeek Translation ===\n";
    echo "Test text: {$testText}\n\n";
    
    $deepSeekService = new DeepSeekTranslationService();
    
    $context = [
        'type' => 'chapter',
        'names' => $nameDictionary
    ];
    
    $result = $deepSeekService->translateText($testText, 'en', 'auto', $context);
    
    if ($result['success']) {
        echo "Translation successful!\n";
        echo "Original: {$result['original_text']}\n";
        echo "Translated: {$result['translated_text']}\n";
        echo "Execution time: {$result['execution_time']} seconds\n";
        
        // Check if character names from dictionary were used
        echo "\n=== Name Dictionary Usage Analysis ===\n";
        $translatedText = $result['translated_text'];
        $namesUsed = 0;
        $namesFound = [];
        
        foreach ($nameDictionary as $name) {
            $originalName = $name['original_name'];
            $targetName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
            
            // Check if original name appears in test text
            if (strpos($testText, $originalName) !== false) {
                echo "Original name '{$originalName}' found in test text\n";
                
                // Check if target name appears in translation
                if (strpos($translatedText, $targetName) !== false) {
                    echo "  ✓ Target name '{$targetName}' found in translation\n";
                    $namesUsed++;
                    $namesFound[] = $targetName;
                } else {
                    echo "  ✗ Target name '{$targetName}' NOT found in translation\n";
                    
                    // Check if original name was left untranslated
                    if (strpos($translatedText, $originalName) !== false) {
                        echo "    ⚠️  Original name '{$originalName}' left untranslated\n";
                    }
                }
            }
        }
        
        echo "\nSummary:\n";
        echo "- Names used correctly: {$namesUsed}\n";
        echo "- Names found in translation: " . implode(', ', $namesFound) . "\n";
        
        if ($namesUsed > 0) {
            echo "✓ Name dictionary integration appears to be working!\n";
        } else {
            echo "✗ Name dictionary integration may have issues\n";
        }
        
    } else {
        echo "Translation failed: {$result['error']}\n";
    }

    // Test with a novel that might have empty entries
    echo "\n=== Testing Empty Entry Filtering ===\n";

    // Check if any entries have empty translations/romanizations
    $emptyEntries = 0;
    $validEntries = 0;

    foreach ($nameDictionary as $name) {
        $translation = isset($name['translation']) ? trim($name['translation']) : '';
        $romanization = isset($name['romanization']) ? trim($name['romanization']) : '';
        $original = isset($name['original_name']) ? trim($name['original_name']) : '';

        if (empty($original)) {
            echo "Found entry with empty original name\n";
            $emptyEntries++;
        } elseif (empty($translation) && empty($romanization)) {
            echo "Found entry with empty translation and romanization: '{$original}'\n";
            $emptyEntries++;
        } else {
            $validEntries++;
        }
    }

    echo "Valid entries: {$validEntries}\n";
    echo "Empty/invalid entries: {$emptyEntries}\n";

    if ($emptyEntries === 0) {
        echo "✓ All entries have valid data!\n";
    } else {
        echo "⚠️  Found {$emptyEntries} entries with missing data\n";
    }

} catch (Exception $e) {
    echo "Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
