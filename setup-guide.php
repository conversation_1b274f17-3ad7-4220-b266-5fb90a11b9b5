<?php
/**
 * Setup Guide - Helps users choose the right setup method
 */
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup Guide - Novel Translator</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-10">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-compass me-2"></i>
                            Novel Translator Setup Guide
                        </h4>
                    </div>
                    <div class="card-body">
                        <div class="mb-4">
                            <h5>Welcome to Novel Translator!</h5>
                            <p class="text-muted">
                                Choose the appropriate setup method based on your situation.
                            </p>
                        </div>

                        <div class="row">
                            <!-- Regular Setup -->
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-success">
                                    <div class="card-header bg-success text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-cog me-2"></i>
                                            Regular Setup
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">
                                            Standard setup process with full progress tracking and error handling.
                                        </p>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Real-time progress updates</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Detailed error messages</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Application integration test</li>
                                        </ul>
                                        <div class="mt-auto">
                                            <a href="setup.php" class="btn btn-success">
                                                <i class="fas fa-play me-2"></i>
                                                Start Regular Setup
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Database Verification -->
                            <div class="col-md-6 mb-4">
                                <div class="card h-100 border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-database me-2"></i>
                                            Database Verification
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">
                                            Verify database connection and check existing setup status.
                                        </p>
                                        <ul class="list-unstyled">
                                            <li><i class="fas fa-check text-success me-2"></i>Connection testing</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Table verification</li>
                                            <li><i class="fas fa-check text-success me-2"></i>Status reporting</li>
                                        </ul>
                                        <div class="mt-auto">
                                            <a href="verify-db.php" class="btn btn-info">
                                                <i class="fas fa-database me-2"></i>
                                                Verify Database
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Diagnostic Tools -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-info">
                                    <div class="card-header bg-info text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-stethoscope me-2"></i>
                                            Diagnostic Tools
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <p class="card-text">
                                            Use these tools to check your system and troubleshoot issues.
                                        </p>
                                        <div class="d-grid gap-2 d-md-flex">
                                            <a href="verify-db.php" class="btn btn-info me-md-2">
                                                <i class="fas fa-database me-2"></i>
                                                Verify Database
                                            </a>
                                            <a href="index.php" class="btn btn-outline-info">
                                                <i class="fas fa-home me-2"></i>
                                                Go to Application
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Prerequisites -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <h6><i class="fas fa-info-circle me-2"></i>Prerequisites</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li>MySQL server running</li>
                                                <li>PHP 8.2 or higher</li>
                                                <li>PDO MySQL extension</li>
                                            </ul>
                                        </div>
                                        <div class="col-md-6">
                                            <ul class="mb-0">
                                                <li>cURL extension</li>
                                                <li>DOM extension</li>
                                                <li>Write permissions for logs directory</li>
                                            </ul>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Troubleshooting -->
                        <div class="row">
                            <div class="col-12">
                                <div class="alert alert-warning">
                                    <h6><i class="fas fa-exclamation-triangle me-2"></i>Troubleshooting</h6>
                                    <p class="mb-2">If you encounter issues:</p>
                                    <ol class="mb-0">
                                        <li>Use <strong>Verify Database</strong> to check database status</li>
                                        <li>Check MySQL server is running and accessible</li>
                                        <li>Ensure proper file permissions for logs directory</li>
                                        <li>Verify PHP extensions are installed</li>
                                        <li>Check API key configuration in config/config.php</li>
                                    </ol>
                                </div>
                            </div>
                        </div>

                        <!-- Quick Status Check -->
                        <div class="row">
                            <div class="col-12">
                                <div class="card border-secondary">
                                    <div class="card-header bg-secondary text-white">
                                        <h6 class="mb-0">
                                            <i class="fas fa-tachometer-alt me-2"></i>
                                            Quick Status Check
                                        </h6>
                                    </div>
                                    <div class="card-body">
                                        <?php
                                        // Quick status check
                                        $status = [];
                                        
                                        // Check PHP version
                                        $status['php'] = version_compare(PHP_VERSION, '8.2.0', '>=');
                                        
                                        // Check extensions
                                        $status['pdo'] = extension_loaded('pdo_mysql');
                                        $status['curl'] = extension_loaded('curl');
                                        $status['dom'] = extension_loaded('dom');
                                        
                                        // Check MySQL connection
                                        $status['mysql'] = false;
                                        try {
                                            $pdo = new PDO("mysql:host=localhost", 'root', '', [PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION]);
                                            $status['mysql'] = true;
                                        } catch (Exception $e) {
                                            // MySQL not accessible
                                        }
                                        
                                        // Check if database exists
                                        $status['database'] = false;
                                        if ($status['mysql']) {
                                            try {
                                                $result = $pdo->query("SHOW DATABASES LIKE 'novel_translator'")->fetchAll();
                                                $status['database'] = !empty($result);
                                            } catch (Exception $e) {
                                                // Database check failed
                                            }
                                        }
                                        
                                        echo '<div class="row">';
                                        echo '<div class="col-md-6">';
                                        echo '<ul class="list-unstyled">';
                                        echo '<li><i class="fas fa-' . ($status['php'] ? 'check text-success' : 'times text-danger') . ' me-2"></i>PHP 8.2+</li>';
                                        echo '<li><i class="fas fa-' . ($status['pdo'] ? 'check text-success' : 'times text-danger') . ' me-2"></i>PDO MySQL</li>';
                                        echo '<li><i class="fas fa-' . ($status['curl'] ? 'check text-success' : 'times text-danger') . ' me-2"></i>cURL Extension</li>';
                                        echo '</ul>';
                                        echo '</div>';
                                        echo '<div class="col-md-6">';
                                        echo '<ul class="list-unstyled">';
                                        echo '<li><i class="fas fa-' . ($status['dom'] ? 'check text-success' : 'times text-danger') . ' me-2"></i>DOM Extension</li>';
                                        echo '<li><i class="fas fa-' . ($status['mysql'] ? 'check text-success' : 'times text-danger') . ' me-2"></i>MySQL Connection</li>';
                                        echo '<li><i class="fas fa-' . ($status['database'] ? 'check text-success' : 'times text-warning') . ' me-2"></i>Database Exists</li>';
                                        echo '</ul>';
                                        echo '</div>';
                                        echo '</div>';
                                        
                                        $allGood = $status['php'] && $status['pdo'] && $status['curl'] && $status['dom'] && $status['mysql'];
                                        
                                        if ($allGood && $status['database']) {
                                            echo '<div class="alert alert-success mb-0">';
                                            echo '<i class="fas fa-check-circle me-2"></i>System ready! Database already exists.';
                                            echo ' <a href="index.php" class="btn btn-success btn-sm ms-2">Go to Application</a>';
                                            echo '</div>';
                                        } elseif ($allGood) {
                                            echo '<div class="alert alert-info mb-0">';
                                            echo '<i class="fas fa-info-circle me-2"></i>System ready for setup!';
                                            echo ' <a href="setup.php" class="btn btn-primary btn-sm ms-2">Start Setup</a>';
                                            echo '</div>';
                                        } else {
                                            echo '<div class="alert alert-warning mb-0">';
                                            echo '<i class="fas fa-exclamation-triangle me-2"></i>Some requirements not met.';
                                            echo ' <a href="verify-db.php" class="btn btn-warning btn-sm ms-2">Check Database</a>';
                                            echo '</div>';
                                        }
                                        ?>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
