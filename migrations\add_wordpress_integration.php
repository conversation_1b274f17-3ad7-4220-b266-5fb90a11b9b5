<?php
/**
 * WordPress Integration Migration
 * Adds WordPress posting functionality to the novel translation application
 */

require_once __DIR__ . '/../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Adding WordPress integration support...\n";
    
    // Check if wordpress_posts table exists
    $tableExists = $db->fetchOne(
        "SELECT COUNT(*) as count FROM information_schema.tables 
         WHERE table_schema = DATABASE() AND table_name = 'wordpress_posts'"
    );
    
    if (!$tableExists['count']) {
        echo "Creating wordpress_posts table...\n";
        
        $createTableSQL = "
        CREATE TABLE wordpress_posts (
            id INT AUTO_INCREMENT PRIMARY KEY,
            novel_id INT,
            chapter_id INT,
            wordpress_post_id BIGINT NOT NULL,
            post_type ENUM('novel', 'chapter') NOT NULL,
            wordpress_url VARCHAR(500),
            post_status ENUM('draft', 'published', 'private') DEFAULT 'published',
            last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
            FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
            UNIQUE KEY unique_novel_post (novel_id, post_type),
            UNIQUE KEY unique_chapter_post (chapter_id),
            INDEX idx_wordpress_post_id (wordpress_post_id),
            INDEX idx_post_type (post_type)
        )";
        
        $db->query($createTableSQL);
        echo "✓ wordpress_posts table created successfully\n";
    } else {
        echo "✓ wordpress_posts table already exists\n";
    }
    
    // Add WordPress preferences if they don't exist
    $wordpressPreferences = [
        'wordpress_site_url' => '',
        'wordpress_username' => '',
        'wordpress_app_password' => '',
        'wordpress_novel_post_type' => 'page',
        'wordpress_chapter_post_type' => 'post',
        'wordpress_novel_custom_post_type' => '',
        'wordpress_chapter_custom_post_type' => '',
        'wordpress_use_custom_post_types' => 'false',
        'wordpress_default_category' => '',
        'wordpress_auto_publish' => 'true',
        'wordpress_include_original_title' => 'true'
    ];
    
    foreach ($wordpressPreferences as $key => $value) {
        $existing = $db->fetchOne(
            "SELECT id FROM user_preferences WHERE preference_key = ?",
            [$key]
        );
        
        if (!$existing) {
            $db->insert('user_preferences', [
                'preference_key' => $key,
                'preference_value' => $value
            ]);
            echo "✓ Added WordPress preference: {$key} = {$value}\n";
        } else {
            echo "✓ WordPress preference already exists: {$key}\n";
        }
    }
    
    echo "\n✅ WordPress integration migration completed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Configure WordPress settings in the Settings page\n";
    echo "2. Set up WordPress Application Password for authentication\n";
    echo "3. Test posting functionality with a translated chapter\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}
?>
