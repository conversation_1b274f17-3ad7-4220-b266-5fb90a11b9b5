<?php
/**
 * Cache Optimization Service for DeepSeek API
 * Optimizes prompt structure to maximize cache hits and reduce token costs
 */

class CacheOptimizationService {
    private $db;
    private $config;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadConfig();
    }

    /**
     * Load cache optimization configuration
     */
    private function loadConfig(): void {
        $this->config = [
            'enable_prompt_optimization' => true,
            'cache_friendly_system_prompt' => true,
            'context_reordering' => true,
            'cache_performance_logging' => true,
            'min_cache_hit_rate_threshold' => 20.0,
            'cost_savings_alert_threshold' => 0.01
        ];

        // Load from database if available
        try {
            $dbConfig = $this->db->fetchAll(
                "SELECT config_key, config_value FROM cache_optimization_config"
            );
            
            foreach ($dbConfig as $item) {
                $value = $item['config_value'];
                // Convert string booleans to actual booleans
                if ($value === 'true') $value = true;
                elseif ($value === 'false') $value = false;
                elseif (is_numeric($value)) $value = (float)$value;
                
                $this->config[$item['config_key']] = $value;
            }
        } catch (Exception $e) {
            error_log("CacheOptimizationService: Could not load config from database: " . $e->getMessage());
        }
    }

    /**
     * Optimize prompt structure for better cache hits
     */
    public function optimizePromptForCaching(string $basePrompt, array $context): string {
        if (!$this->config['enable_prompt_optimization']) {
            return $basePrompt;
        }

        // Build cache-friendly prompt structure
        $optimizedPrompt = $this->buildCacheFriendlyPrompt($basePrompt, $context);
        
        return $optimizedPrompt;
    }

    /**
     * Build cache-friendly prompt with consistent structure
     */
    private function buildCacheFriendlyPrompt(string $basePrompt, array $context): string {
        $prompt = '';

        // 1. Start with consistent base instructions (most likely to be cached)
        $prompt .= $this->getStandardTranslationInstructions($context);

        // 2. Add context-specific instructions in consistent order
        if ($this->config['context_reordering']) {
            $prompt .= $this->getOrderedContextInstructions($context);
        }

        // 3. Add the actual content to translate at the end
        $prompt .= "\n" . $basePrompt;

        return $prompt;
    }

    /**
     * Get standard translation instructions that are likely to be cached
     */
    private function getStandardTranslationInstructions(array $context): string {
        $instructions = "You are a professional translator specializing in Japanese to English translation for novels and literature.\n\n";

        // Add type-specific instructions in consistent order
        if (isset($context['type'])) {
            switch ($context['type']) {
                case 'title':
                    $instructions .= "TRANSLATION TYPE: Novel Title\n";
                    $instructions .= "REQUIREMENTS: Provide a single, clean translation without explanations or alternatives.\n\n";
                    break;
                case 'chapter':
                case 'content':
                    $instructions .= "TRANSLATION TYPE: Novel Content\n";
                    $instructions .= "REQUIREMENTS: Maintain narrative style, preserve character names, and ensure consistency.\n\n";
                    break;
                case 'synopsis':
                    $instructions .= "TRANSLATION TYPE: Novel Synopsis\n";
                    $instructions .= "REQUIREMENTS: Keep engaging and informative while maintaining accuracy.\n\n";
                    break;
            }
        }

        return $instructions;
    }

    /**
     * Get context instructions in consistent order for better caching
     */
    private function getOrderedContextInstructions(array $context): string {
        $instructions = '';

        // 1. Character names (most stable context)
        if (isset($context['names']) && !empty($context['names'])) {
            $instructions .= "CHARACTER NAME TRANSLATIONS:\n";
            // Sort names for consistent ordering
            $sortedNames = $context['names'];
            usort($sortedNames, function($a, $b) {
                return strcmp($a['original_name'], $b['original_name']);
            });
            
            foreach ($sortedNames as $name) {
                $instructions .= "- {$name['original_name']} → {$name['translation']}\n";
            }
            $instructions .= "\n";
        }

        // 2. Sound effects preservation (if applicable)
        if (isset($context['sound_effects']) && !empty($context['sound_effects'])) {
            $instructions .= "SOUND EFFECTS: Preserve original romanized form without translation.\n\n";
        }

        // 3. Chunk context (if applicable)
        if (isset($context['chunk_number'])) {
            $instructions .= "CHUNK CONTEXT: This is chunk {$context['chunk_number']}";
            if (isset($context['total_chunks'])) {
                $instructions .= " of {$context['total_chunks']}";
            }
            $instructions .= " from a larger chapter.\n\n";
        }

        return $instructions;
    }

    /**
     * Analyze cache performance for a novel
     */
    public function analyzeCachePerformance(int $novelId, int $days = 7): array {
        $analysis = [
            'novel_id' => $novelId,
            'period_days' => $days,
            'total_requests' => 0,
            'cache_hit_rate' => 0.0,
            'total_cost_savings' => 0.0,
            'recommendations' => []
        ];

        try {
            $stats = $this->db->fetchOne(
                "SELECT 
                    COUNT(*) as total_requests,
                    AVG(cache_hit_rate) as avg_cache_hit_rate,
                    SUM(estimated_cost_savings) as total_savings,
                    SUM(cache_hit_tokens) as total_cache_hits,
                    SUM(cache_miss_tokens) as total_cache_misses
                 FROM translation_logs 
                 WHERE novel_id = ? 
                   AND api_used = 'deepseek' 
                   AND status = 'success'
                   AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
                [$novelId, $days]
            );

            if ($stats && $stats['total_requests'] > 0) {
                $analysis['total_requests'] = (int)$stats['total_requests'];
                $analysis['cache_hit_rate'] = round((float)$stats['avg_cache_hit_rate'], 2);
                $analysis['total_cost_savings'] = round((float)$stats['total_savings'], 6);

                // Generate recommendations
                $analysis['recommendations'] = $this->generateOptimizationRecommendations($stats);
            }
        } catch (Exception $e) {
            error_log("CacheOptimizationService: Error analyzing cache performance: " . $e->getMessage());
        }

        return $analysis;
    }

    /**
     * Generate optimization recommendations based on cache performance
     */
    private function generateOptimizationRecommendations(array $stats): array {
        $recommendations = [];
        $cacheHitRate = (float)$stats['avg_cache_hit_rate'];

        if ($cacheHitRate < $this->config['min_cache_hit_rate_threshold']) {
            $recommendations[] = [
                'type' => 'low_cache_hit_rate',
                'message' => "Cache hit rate ({$cacheHitRate}%) is below threshold. Consider optimizing prompt structure.",
                'action' => 'enable_prompt_optimization'
            ];
        }

        if ($cacheHitRate > 50) {
            $recommendations[] = [
                'type' => 'good_performance',
                'message' => "Excellent cache performance ({$cacheHitRate}%). Current optimization is working well.",
                'action' => 'maintain_current_settings'
            ];
        }

        $totalSavings = (float)$stats['total_savings'];
        if ($totalSavings > $this->config['cost_savings_alert_threshold']) {
            $recommendations[] = [
                'type' => 'significant_savings',
                'message' => "Significant cost savings achieved (\${$totalSavings}). Cache optimization is effective.",
                'action' => 'continue_monitoring'
            ];
        }

        return $recommendations;
    }

    /**
     * Get cache performance summary for dashboard
     */
    public function getCachePerformanceSummary(int $days = 30): array {
        try {
            $summary = $this->db->fetchOne(
                "SELECT 
                    COUNT(*) as total_requests,
                    AVG(cache_hit_rate) as avg_cache_hit_rate,
                    SUM(estimated_cost_savings) as total_cost_savings,
                    SUM(cache_hit_tokens) as total_cache_hits,
                    SUM(cache_miss_tokens) as total_cache_misses,
                    AVG(translation_time_seconds) as avg_translation_time
                 FROM translation_logs 
                 WHERE api_used = 'deepseek' 
                   AND status = 'success'
                   AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)",
                [$days]
            );

            if (!$summary) {
                return ['error' => 'No data available for the specified period'];
            }

            return [
                'period_days' => $days,
                'total_requests' => (int)$summary['total_requests'],
                'avg_cache_hit_rate' => round((float)$summary['avg_cache_hit_rate'], 2),
                'total_cost_savings' => round((float)$summary['total_cost_savings'], 6),
                'total_cache_hits' => (int)$summary['total_cache_hits'],
                'total_cache_misses' => (int)$summary['total_cache_misses'],
                'avg_translation_time' => round((float)$summary['avg_translation_time'], 2)
            ];
        } catch (Exception $e) {
            error_log("CacheOptimizationService: Error getting performance summary: " . $e->getMessage());
            return ['error' => $e->getMessage()];
        }
    }

    /**
     * Update cache optimization configuration
     */
    public function updateConfig(string $key, $value): bool {
        try {
            $this->db->query(
                "INSERT INTO cache_optimization_config (config_key, config_value) 
                 VALUES (?, ?) 
                 ON DUPLICATE KEY UPDATE config_value = VALUES(config_value), updated_at = CURRENT_TIMESTAMP",
                [$key, (string)$value]
            );
            
            $this->config[$key] = $value;
            return true;
        } catch (Exception $e) {
            error_log("CacheOptimizationService: Error updating config: " . $e->getMessage());
            return false;
        }
    }
}
