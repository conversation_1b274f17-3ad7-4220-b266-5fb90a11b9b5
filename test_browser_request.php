<?php
/**
 * Test script to simulate the exact browser request
 */

// Test data
$testData = [
    'novel_id' => 1,
    'chapter_number' => 2
];

$jsonData = json_encode($testData);

// Make HTTP request to the API with browser-like headers
$url = 'http://localhost/wc/api/chapters.php';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Accept: application/json, text/plain, */*',
    'User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
    'X-Requested-With: XMLHttpRequest',
    'Referer: http://localhost/wc/novel-details.php?id=1',
    'Content-Length: ' . strlen($jsonData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true); // Include headers in output
curl_setopt($ch, CURLOPT_COOKIEJAR, sys_get_temp_dir() . '/test_cookies.txt');
curl_setopt($ch, CURLOPT_COOKIEFILE, sys_get_temp_dir() . '/test_cookies.txt');

echo "=== Browser-like HTTP API Test ===\n";
echo "Request URL: $url\n";
echo "Request Data: $jsonData\n\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);

if (curl_error($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
} else {
    echo "HTTP Status Code: $httpCode\n";
    
    // Separate headers and body
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    echo "\n=== Response Headers ===\n";
    echo $headers;
    
    echo "\n=== Response Body Analysis ===\n";
    echo "Body Length: " . strlen($body) . " characters\n";
    
    // Check for any non-printable characters at the beginning
    $firstBytes = substr($body, 0, 20);
    echo "First 20 bytes (hex): " . bin2hex($firstBytes) . "\n";
    echo "First 20 bytes (raw): '" . $firstBytes . "'\n";
    
    // Check if body starts with valid JSON
    $trimmedBody = ltrim($body);
    $firstChar = substr($trimmedBody, 0, 1);
    echo "First character after trim: '$firstChar'\n";
    
    if ($firstChar !== '{' && $firstChar !== '[') {
        echo "ERROR: Response does not start with JSON!\n";
        echo "First 200 characters of response:\n";
        echo "'" . substr($body, 0, 200) . "'\n";
        
        // Look for any non-JSON content before the JSON
        $jsonStart = strpos($body, '{');
        if ($jsonStart > 0) {
            echo "\nNon-JSON content before JSON (length: $jsonStart):\n";
            $beforeJson = substr($body, 0, $jsonStart);
            echo "Hex: " . bin2hex($beforeJson) . "\n";
            echo "Raw: '" . $beforeJson . "'\n";
        }
        
        // Look for the actual JSON part
        if ($jsonStart !== false) {
            echo "\nJSON part starts at position: $jsonStart\n";
            $jsonPart = substr($body, $jsonStart);
            echo "JSON part (first 200 chars): " . substr($jsonPart, 0, 200) . "\n";
            
            // Try to decode the JSON part
            $decoded = json_decode($jsonPart, true);
            if ($decoded !== null) {
                echo "JSON part decodes successfully!\n";
                echo "Success: " . ($decoded['success'] ? 'true' : 'false') . "\n";
            } else {
                echo "JSON part decode error: " . json_last_error_msg() . "\n";
            }
        }
    } else {
        echo "Response appears to be valid JSON\n";
        
        // Try to decode JSON
        $decoded = json_decode($body, true);
        if ($decoded === null) {
            echo "JSON decode error: " . json_last_error_msg() . "\n";
            echo "First 200 characters:\n";
            echo substr($body, 0, 200) . "\n";
        } else {
            echo "JSON decoded successfully\n";
            echo "Success: " . ($decoded['success'] ? 'true' : 'false') . "\n";
            if (isset($decoded['data']['chapter']['id'])) {
                echo "Chapter ID: " . $decoded['data']['chapter']['id'] . "\n";
            }
        }
    }
}

curl_close($ch);

echo "\n=== Test Complete ===\n";

// Also check the error log for any messages
$errorLogFile = 'error.log';
if (file_exists($errorLogFile)) {
    echo "\n=== Recent Error Log Entries ===\n";
    $errorLog = file_get_contents($errorLogFile);
    $lines = explode("\n", $errorLog);
    $recentLines = array_slice($lines, -10); // Last 10 lines
    foreach ($recentLines as $line) {
        if (!empty(trim($line))) {
            echo $line . "\n";
        }
    }
}
?>
