<?php
/**
 * Test script to simulate the exact API call that's failing
 */

// Enable error reporting and output buffering
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

try {
    // Include config
    require_once 'config/config.php';
    
    echo "=== Chapter API Test ===\n";
    
    // Test 1: Basic initialization
    echo "1. Initializing NovelManager...\n";
    $novelManager = new NovelManager();
    echo "   ✓ NovelManager initialized\n";
    
    // Test 2: Find a test novel and chapter
    echo "\n2. Finding test data...\n";
    $db = Database::getInstance();
    
    // Look for a novel with chapters that don't have content
    $testData = $db->fetchOne(
        "SELECT c.novel_id, c.chapter_number, n.platform, n.original_title 
         FROM chapters c 
         JOIN novels n ON c.novel_id = n.id 
         WHERE c.original_content IS NULL 
         AND c.chapter_url IS NOT NULL 
         LIMIT 1"
    );
    
    if (!$testData) {
        echo "   ! No test data found - all chapters have content\n";
        echo "   Creating a mock test...\n";
        
        // Use any existing novel for testing
        $testData = $db->fetchOne(
            "SELECT c.novel_id, c.chapter_number, n.platform, n.original_title 
             FROM chapters c 
             JOIN novels n ON c.novel_id = n.id 
             LIMIT 1"
        );
    }
    
    if ($testData) {
        echo "   Found test data:\n";
        echo "   - Novel ID: {$testData['novel_id']}\n";
        echo "   - Chapter: {$testData['chapter_number']}\n";
        echo "   - Platform: {$testData['platform']}\n";
        echo "   - Title: {$testData['original_title']}\n";
        
        // Test 3: Simulate the exact API request
        echo "\n3. Simulating API request...\n";
        
        // Create the exact JSON that would be sent
        $requestData = [
            'novel_id' => $testData['novel_id'],
            'chapter_number' => $testData['chapter_number']
        ];
        
        echo "   Request data: " . json_encode($requestData) . "\n";
        
        // Test the handleSaveChapter function directly
        echo "   Calling handleSaveChapter...\n";
        
        // Simulate the POST data
        $_SERVER['REQUEST_METHOD'] = 'POST';
        
        // Create a temporary file with the JSON data
        $tempFile = tempnam(sys_get_temp_dir(), 'test_input');
        file_put_contents($tempFile, json_encode($requestData));
        
        // Override php://input for testing
        $originalInput = file_get_contents('php://input');
        
        // Capture output
        ob_start();
        
        try {
            // We need to include the API file and test it
            // But first let's test the NovelManager method directly
            echo "   Testing NovelManager::saveChapter directly...\n";
            
            $result = $novelManager->saveChapter($testData['novel_id'], $testData['chapter_number']);
            
            echo "   Result: " . json_encode($result, JSON_PRETTY_PRINT) . "\n";
            
            if ($result['success']) {
                echo "   ✓ saveChapter method works correctly\n";
            } else {
                echo "   ✗ saveChapter method failed: " . $result['error'] . "\n";
            }
            
        } catch (Exception $e) {
            echo "   ✗ Exception in saveChapter: " . $e->getMessage() . "\n";
            echo "   Stack trace: " . $e->getTraceAsString() . "\n";
        }
        
        $output = ob_get_clean();
        echo $output;
        
    } else {
        echo "   ✗ No test data available\n";
    }
    
    echo "\n=== Test Complete ===\n";
    
} catch (Exception $e) {
    echo "\n✗ Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "\n✗ Fatal PHP error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Get any unexpected output
$output = ob_get_clean();
echo $output;
?>
