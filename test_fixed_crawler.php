<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$url = 'https://69shuba.cx/book/44425.htm';

try {
    echo "Testing fixed 69shuba crawler with URL: $url\n";
    
    // Get chapter list
    $chapters = $crawler->getChapterList($url);
    
    if (!empty($chapters)) {
        echo "Found " . count($chapters) . " chapters\n";
        
        // Test the first chapter
        $firstChapter = $chapters[0];
        echo "Testing first chapter: " . $firstChapter['original_title'] . "\n";
        echo "Chapter URL: " . $firstChapter['chapter_url'] . "\n";
        
        $chapterData = $crawler->getChapterContent($firstChapter['chapter_url']);
        $content = $chapterData['original_content'];

        echo "\n=== RESULTS ===\n";
        echo "Content length: " . strlen($content) . " bytes\n";
        echo "Content length (UTF-8): " . mb_strlen($content, 'UTF-8') . " characters\n";
        echo "Character encoding: " . mb_detect_encoding($content) . "\n";
        echo "Is valid UTF-8: " . (mb_check_encoding($content, 'UTF-8') ? 'Yes' : 'No') . "\n";

        echo "\nFirst 1000 characters:\n";
        echo "---\n";
        echo mb_substr($content, 0, 1000, 'UTF-8') . "\n";
        echo "---\n";

        // Check for garbled characters
        $hasQuestionMarks = strpos($content, '?') !== false;
        $hasGarbledChars = preg_match('/[^\x20-\x7E\x{4E00}-\x{9FFF}\x{3000}-\x{303F}\x{FF00}-\x{FFEF}\s]/u', $content);

        echo "\nQuality check:\n";
        echo "Contains question marks: " . ($hasQuestionMarks ? 'Yes' : 'No') . "\n";
        echo "Contains garbled characters: " . ($hasGarbledChars ? 'Yes' : 'No') . "\n";

        // Check for Chinese characters
        $chineseCharCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content);
        echo "Chinese character count: " . $chineseCharCount . "\n";
        
        if ($chineseCharCount > 0) {
            echo "✓ Chinese characters detected - extraction successful!\n";
        } else {
            echo "✗ No Chinese characters found - extraction may have failed\n";
        }
        
    } else {
        echo "No chapters found\n";
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
