<?php
/**
 * Chapter Chunker - Intelligent chapter content splitting for translation
 * Novel Translation Application
 * Enhanced with dynamic sizing and better context preservation
 */

class ChapterChunker {
    private $db;
    private $chunkSizeLimit;
    private $overlapSize;
    private $enableSmartChunking;
    private $dynamicSizing;
    private $minChunkSize;
    private $maxChunkSize;
    private $contextPreservationSize;

    public function __construct() {
        $this->db = Database::getInstance();
        $this->loadConfiguration();
    }

    /**
     * Get current chunk size limit
     */
    public function getChunkSizeLimit(): int {
        return $this->chunkSizeLimit;
    }

    /**
     * Set chunk size limit
     */
    public function setChunkSizeLimit(int $size): void {
        $this->chunkSizeLimit = max(5000, min(25000, $size));
    }

    /**
     * Load chunking configuration from user preferences
     */
    private function loadConfiguration(): void {
        $preferences = $this->db->fetchAll(
            "SELECT preference_key, preference_value FROM user_preferences
             WHERE preference_key IN ('chunk_size_limit', 'chunk_overlap_size', 'enable_smart_chunking', 'dynamic_chunk_sizing')"
        );

        // Set enhanced defaults for better timeout prevention (more conservative)
        $this->chunkSizeLimit = 12000; // Further reduced for better timeout prevention
        $this->overlapSize = 300; // Reduced overlap for efficiency
        $this->enableSmartChunking = true;
        $this->dynamicSizing = true;
        $this->minChunkSize = 5000; // Minimum chunk size
        $this->maxChunkSize = 15000; // Reduced maximum chunk size
        $this->contextPreservationSize = 200; // Characters to preserve for context

        // Apply user preferences
        foreach ($preferences as $pref) {
            switch ($pref['preference_key']) {
                case 'chunk_size_limit':
                    $this->chunkSizeLimit = max(5000, min(20000, (int)$pref['preference_value']));
                    break;
                case 'chunk_overlap_size':
                    $this->overlapSize = max(100, min(1000, (int)$pref['preference_value']));
                    break;
                case 'enable_smart_chunking':
                    $this->enableSmartChunking = $pref['preference_value'] === 'true';
                    break;
                case 'dynamic_chunk_sizing':
                    $this->dynamicSizing = $pref['preference_value'] === 'true';
                    break;
            }
        }
    }

    /**
     * Calculate content statistics with complexity analysis
     */
    public function calculateContentStats(string $content): array {
        try {
            $characterCount = mb_strlen($content);
            $wordCount = str_word_count(strip_tags($content));
            $complexity = $this->analyzeContentComplexity($content);
            $estimatedChunks = $this->estimateChunkCount($content, $complexity);

            // Use more conservative chunking threshold to prevent timeouts
            $needsChunking = $this->needsChunking($content);
        } catch (Exception $e) {
            error_log("ChapterChunker: Error in calculateContentStats: " . $e->getMessage());
            // Return safe fallback values
            return [
                'character_count' => strlen($content),
                'word_count' => str_word_count($content),
                'needs_chunking' => false,
                'estimated_chunks' => 1,
                'complexity' => ['score' => 1.0, 'factors' => []]
            ];
        }

        return [
            'character_count' => $characterCount,
            'word_count' => $wordCount,
            'complexity_score' => $complexity['score'],
            'complexity_factors' => $complexity['factors'],
            'needs_chunking' => $needsChunking,
            'estimated_chunks' => $estimatedChunks,
            'recommended_chunk_size' => $this->getRecommendedChunkSize($complexity)
        ];
    }

    /**
     * Analyze content complexity for better chunk sizing
     */
    private function analyzeContentComplexity(string $content): array {
        $factors = [];
        $score = 1.0;

        try {
            // Check for dialogue density (more complex to translate)
            $dialogueMatches = preg_match_all('/「[^」]*」|"[^"]*"/', $content);
            $dialogueDensity = $dialogueMatches / max(1, mb_strlen($content) / 1000);
            if ($dialogueDensity > 5) {
                $factors[] = 'high_dialogue_density';
                $score *= 1.3;
            }

            // Check for technical terms or complex vocabulary
            $kanjiDensity = preg_match_all('/[\x{4e00}-\x{9faf}]/u', $content) / max(1, mb_strlen($content));
            if ($kanjiDensity > 0.3) {
                $factors[] = 'high_kanji_density';
                $score *= 1.2;
            }
        } catch (Exception $e) {
            error_log("ChapterChunker: Error in analyzeContentComplexity: " . $e->getMessage());
            // Return safe defaults
            $factors = [];
            $score = 1.0;
        }

        // Check for scene transitions
        $sceneBreaks = preg_match_all('/\n\s*[＊\*]{3,}|\n\s*[-=]{3,}|\n\s*\n\s*\n/', $content);
        if ($sceneBreaks > 0) {
            $factors[] = 'scene_transitions';
            $score *= 0.9; // Easier to chunk at scene breaks
        }

        // Check for furigana presence
        if (preg_match('/[\x{3040}-\x{309f}]/u', $content)) {
            $factors[] = 'furigana_present';
            $score *= 1.1;
        }

        return [
            'score' => $score,
            'factors' => $factors,
            'dialogue_density' => $dialogueDensity,
            'kanji_density' => $kanjiDensity,
            'scene_breaks' => $sceneBreaks
        ];
    }

    /**
     * Estimate chunk count based on content and complexity
     */
    private function estimateChunkCount(string $content, array $complexity): int {
        $baseChunkSize = $this->getRecommendedChunkSize($complexity);
        return max(1, ceil(mb_strlen($content) / $baseChunkSize));
    }

    /**
     * Get recommended chunk size based on complexity
     */
    private function getRecommendedChunkSize(array $complexity): int {
        $baseSize = $this->chunkSizeLimit;
        $adjustedSize = $baseSize / $complexity['score'];

        return max($this->minChunkSize, min($this->maxChunkSize, (int)$adjustedSize));
    }

    /**
     * Check if chapter needs to be chunked with dynamic sizing
     */
    public function needsChunking(string $content): bool {
        if (!$this->dynamicSizing) {
            return mb_strlen($content) > $this->chunkSizeLimit;
        }

        $complexity = $this->analyzeContentComplexity($content);
        $recommendedSize = $this->getRecommendedChunkSize($complexity);

        return mb_strlen($content) > $recommendedSize;
    }

    /**
     * Create chunks for a chapter (alias for splitChapter)
     */
    public function createChunks(int $chapterId, string $content): array {
        return $this->splitChapter($chapterId, $content);
    }

    /**
     * Split chapter content into intelligent chunks with enhanced context preservation
     */
    public function splitChapter(int $chapterId, string $content): array {
        try {
            // Pre-chunking validation
            $validationResult = $this->validateContentForChunking($content);
            if (!$validationResult['valid']) {
                return [
                    'success' => false,
                    'error' => 'Content validation failed: ' . $validationResult['error']
                ];
            }

            $complexity = $this->analyzeContentComplexity($content);

            if (!$this->needsChunking($content)) {
                return [
                    'success' => true,
                    'chunks_created' => 0,
                    'message' => 'Chapter does not need chunking',
                    'complexity_analysis' => $complexity,
                    'content_validation' => $validationResult
                ];
            }

            // Clear existing chunks for this chapter
            $this->clearExistingChunks($chapterId);

            // Determine optimal chunk size based on complexity
            $optimalChunkSize = $this->getRecommendedChunkSize($complexity);
            $originalChunkSize = $this->chunkSizeLimit;
            $this->chunkSizeLimit = $optimalChunkSize;

            // Split content into chunks with enhanced logic
            $chunks = $this->enableSmartChunking
                ? $this->enhancedSmartSplit($content, $complexity)
                : $this->simpleSplit($content);

            // Restore original chunk size
            $this->chunkSizeLimit = $originalChunkSize;

            // Validate chunk completeness before adding context
            $completenessCheck = $this->validateChunkCompleteness($chunks, $content);
            if (!$completenessCheck['complete']) {
                return [
                    'success' => false,
                    'error' => 'Chunk completeness validation failed: ' . $completenessCheck['error']
                ];
            }

            // Add enhanced context information to chunks
            $chunks = $this->addEnhancedContextToChunks($chunks, $content);

            // Save chunks to database with validation
            $chunksCreated = 0;
            foreach ($chunks as $index => $chunk) {
                $this->saveChunk($chapterId, $index + 1, $chunk);
                $chunksCreated++;
            }

            // Final validation of saved chunks
            $savedChunksValidation = $this->validateSavedChunks($chapterId, $content);

            return [
                'success' => true,
                'chunks_created' => $chunksCreated,
                'total_characters' => mb_strlen($content),
                'average_chunk_size' => $chunksCreated > 0 ? round(mb_strlen($content) / $chunksCreated) : 0,
                'complexity_analysis' => $complexity,
                'optimal_chunk_size' => $optimalChunkSize,
                'content_validation' => $validationResult,
                'completeness_check' => $completenessCheck,
                'saved_chunks_validation' => $savedChunksValidation
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }

    /**
     * Enhanced smart content splitting with natural break points and context preservation
     */
    private function enhancedSmartSplit(string $content, array $complexity): array {
        $chunks = [];
        $currentChunk = '';
        $currentSize = 0;

        // First, identify natural break points
        $breakPoints = $this->identifyNaturalBreakPoints($content);

        // Split by the most appropriate break points
        $segments = $this->splitByBreakPoints($content, $breakPoints);

        foreach ($segments as $segment) {
            $segment = trim($segment);
            if (empty($segment)) continue;

            $segmentSize = mb_strlen($segment);

            // If single segment is too large, split it further
            if ($segmentSize > $this->chunkSizeLimit) {
                // Save current chunk if it has content
                if (!empty($currentChunk)) {
                    $chunks[] = $this->createChunkData($currentChunk, $currentSize);
                    $currentChunk = '';
                    $currentSize = 0;
                }

                // Split large segment by sentences or paragraphs
                $subChunks = $this->splitLargeSegment($segment);
                $chunks = array_merge($chunks, $subChunks);
                continue;
            }

            // Check if adding this segment would exceed limit
            if ($currentSize + $segmentSize > $this->chunkSizeLimit && !empty($currentChunk)) {
                // Save current chunk
                $chunks[] = $this->createChunkData($currentChunk, $currentSize);

                // Start new chunk with context preservation
                $currentChunk = $this->getContextualOverlap($currentChunk, $segment);
                $currentSize = mb_strlen($currentChunk);
            }

            // Add segment to current chunk
            $currentChunk .= ($currentChunk ? "\n\n" : '') . $segment;
            $currentSize = mb_strlen($currentChunk);
        }

        // Add final chunk if it has content
        if (!empty($currentChunk)) {
            $chunks[] = $this->createChunkData($currentChunk, $currentSize);
        }

        return $chunks;
    }

    /**
     * Identify natural break points in content
     */
    private function identifyNaturalBreakPoints(string $content): array {
        $breakPoints = [];

        // Scene transitions (highest priority)
        preg_match_all('/\n\s*[＊\*]{3,}[^\n]*\n|\n\s*[-=]{5,}\s*\n/', $content, $matches, PREG_OFFSET_CAPTURE);
        foreach ($matches[0] as $match) {
            $breakPoints[] = ['type' => 'scene_break', 'position' => $match[1], 'priority' => 1];
        }

        // Chapter/section breaks
        preg_match_all('/\n\s*第[一二三四五六七八九十\d]+[章節话回]\s*[^\n]*\n|\n\s*Chapter\s+\d+[^\n]*\n/i', $content, $matches, PREG_OFFSET_CAPTURE);
        foreach ($matches[0] as $match) {
            $breakPoints[] = ['type' => 'chapter_break', 'position' => $match[1], 'priority' => 1];
        }

        // Large paragraph breaks (medium priority)
        preg_match_all('/\n\s*\n\s*\n/', $content, $matches, PREG_OFFSET_CAPTURE);
        foreach ($matches[0] as $match) {
            $breakPoints[] = ['type' => 'paragraph_break', 'position' => $match[1], 'priority' => 2];
        }

        // Dialogue transitions (lower priority)
        preg_match_all('/」\s*\n\s*「|"\s*\n\s*"/', $content, $matches, PREG_OFFSET_CAPTURE);
        foreach ($matches[0] as $match) {
            $breakPoints[] = ['type' => 'dialogue_break', 'position' => $match[1], 'priority' => 3];
        }

        // Sort by position
        usort($breakPoints, function($a, $b) {
            return $a['position'] <=> $b['position'];
        });

        return $breakPoints;
    }

    /**
     * Split content by identified break points
     */
    private function splitByBreakPoints(string $content, array $breakPoints): array {
        if (empty($breakPoints)) {
            // Fallback to paragraph splitting
            return preg_split('/\n\s*\n/', $content, -1, PREG_SPLIT_NO_EMPTY);
        }

        $segments = [];
        $lastPosition = 0;

        foreach ($breakPoints as $breakPoint) {
            if ($breakPoint['position'] > $lastPosition) {
                $segment = mb_substr($content, $lastPosition, $breakPoint['position'] - $lastPosition);
                if (!empty(trim($segment))) {
                    $segments[] = trim($segment);
                }
                $lastPosition = $breakPoint['position'];
            }
        }

        // Add final segment
        if ($lastPosition < mb_strlen($content)) {
            $segment = mb_substr($content, $lastPosition);
            if (!empty(trim($segment))) {
                $segments[] = trim($segment);
            }
        }

        return $segments;
    }

    /**
     * Split large segment by sentences or paragraphs
     */
    private function splitLargeSegment(string $segment): array {
        $chunks = [];

        // Try splitting by paragraphs first
        $paragraphs = preg_split('/\n\s*\n/', $segment, -1, PREG_SPLIT_NO_EMPTY);

        if (count($paragraphs) > 1) {
            // Use paragraph-based splitting
            $currentChunk = '';
            $currentSize = 0;

            foreach ($paragraphs as $paragraph) {
                $paragraph = trim($paragraph);
                $paragraphSize = mb_strlen($paragraph);

                if ($currentSize + $paragraphSize > $this->chunkSizeLimit && !empty($currentChunk)) {
                    $chunks[] = $this->createChunkData($currentChunk, $currentSize);
                    $currentChunk = $this->getContextualOverlap($currentChunk, $paragraph);
                    $currentSize = mb_strlen($currentChunk);
                }

                $currentChunk .= ($currentChunk ? "\n\n" : '') . $paragraph;
                $currentSize = mb_strlen($currentChunk);
            }

            if (!empty($currentChunk)) {
                $chunks[] = $this->createChunkData($currentChunk, $currentSize);
            }
        } else {
            // Fallback to sentence splitting
            $chunks = $this->splitLargeParagraph($segment);
        }

        return $chunks;
    }

    /**
     * Split large paragraph by sentences (enhanced version)
     */
    private function splitLargeParagraph(string $paragraph): array {
        $chunks = [];
        $sentences = preg_split('/(?<=[。！？])\s*/', $paragraph, -1, PREG_SPLIT_NO_EMPTY);

        $currentChunk = '';
        $currentSize = 0;

        foreach ($sentences as $sentence) {
            $sentence = trim($sentence);
            if (empty($sentence)) continue;

            $sentenceSize = mb_strlen($sentence);

            if ($currentSize + $sentenceSize > $this->chunkSizeLimit && !empty($currentChunk)) {
                $chunks[] = $this->createChunkData($currentChunk, $currentSize);
                $currentChunk = $this->getContextualOverlap($currentChunk, $sentence);
                $currentSize = mb_strlen($currentChunk);
            }

            $currentChunk .= ($currentChunk ? ' ' : '') . $sentence;
            $currentSize = mb_strlen($currentChunk);
        }

        if (!empty($currentChunk)) {
            $chunks[] = $this->createChunkData($currentChunk, $currentSize);
        }

        return $chunks;
    }

    /**
     * Create chunk data structure
     */
    private function createChunkData(string $content, int $size = null): array {
        $size = $size ?? mb_strlen($content);
        return [
            'content' => trim($content),
            'type' => $this->detectChunkType($content),
            'character_count' => $size,
            'word_count' => str_word_count(strip_tags($content)),
            'context_preserved' => false
        ];
    }

    /**
     * Get contextual overlap that preserves narrative flow and sound effects
     */
    private function getContextualOverlap(string $currentChunk, string $nextSegment): string {
        if ($this->overlapSize <= 0) return '';

        // Try to find a good context boundary (end of sentence, dialogue, etc.)
        $contextBoundaries = [
            '/。[^。]*$/u',  // End of sentence
            '/」[^」]*$/u',  // End of dialogue
            '/\n[^\n]*$/u'   // End of line
        ];

        $overlap = '';
        $chunkLength = mb_strlen($currentChunk);

        // Start from the desired overlap size and work backwards
        $startPos = max(0, $chunkLength - $this->contextPreservationSize);
        $candidateOverlap = mb_substr($currentChunk, $startPos);

        // Check if there are sound effects near the boundary that need context preservation
        $soundEffectNearBoundary = $this->hasSoundEffectNearBoundary($candidateOverlap, $nextSegment);

        if ($soundEffectNearBoundary) {
            // Extend overlap to include more context for sound effects
            $extendedStartPos = max(0, $chunkLength - ($this->contextPreservationSize * 1.5));
            $candidateOverlap = mb_substr($currentChunk, $extendedStartPos);
        }

        foreach ($contextBoundaries as $pattern) {
            if (preg_match($pattern, $candidateOverlap, $matches)) {
                $overlap = $matches[0];
                break;
            }
        }

        // Fallback to simple overlap if no good boundary found
        if (empty($overlap)) {
            $overlap = mb_substr($currentChunk, max(0, $chunkLength - $this->overlapSize));
        }

        return trim($overlap);
    }

    /**
     * Check if there are sound effects near the chunk boundary
     */
    private function hasSoundEffectNearBoundary(string $endOfChunk, string $startOfNext): bool {
        // Look for sound effect patterns near the boundary
        $soundEffectPatterns = [
            '/[ドバガズビパダボポキギチリピビジシカタファパマラサザナハヤワ]{2,}/',
            '/「[ァ-ヶー・ッっ！？]+」/',
            '/[！？]{2,}/',
        ];

        $boundaryText = $endOfChunk . mb_substr($startOfNext, 0, 100);

        foreach ($soundEffectPatterns as $pattern) {
            if (preg_match($pattern, $boundaryText)) {
                return true;
            }
        }

        return false;
    }

    /**
     * Validate content before chunking
     */
    private function validateContentForChunking(string $content): array {
        $errors = [];

        // Check for empty content
        if (empty(trim($content))) {
            $errors[] = 'Content is empty';
        }

        // Check for minimum content length
        if (mb_strlen($content) < 100) {
            $errors[] = 'Content too short for reliable chunking';
        }

        // Check for encoding issues
        if (!mb_check_encoding($content, 'UTF-8')) {
            $errors[] = 'Content has encoding issues';
        }

        // Check for excessive whitespace that might affect chunking
        $whitespaceRatio = (mb_strlen($content) - mb_strlen(preg_replace('/\s+/', '', $content))) / mb_strlen($content);
        if ($whitespaceRatio > 0.7) {
            $errors[] = 'Content has excessive whitespace';
        }

        return [
            'valid' => empty($errors),
            'error' => implode('; ', $errors),
            'content_length' => mb_strlen($content),
            'whitespace_ratio' => $whitespaceRatio ?? 0
        ];
    }

    /**
     * Validate that chunks cover the complete original content
     */
    private function validateChunkCompleteness(array $chunks, string $originalContent): array {
        $totalChunkLength = 0;
        $reconstructedContent = '';
        $errors = [];

        // Reconstruct content from chunks (without overlap)
        foreach ($chunks as $index => $chunk) {
            $chunkContent = $chunk['content'];
            $totalChunkLength += mb_strlen($chunkContent);

            // For reconstruction, we need to handle overlaps
            if ($index === 0) {
                $reconstructedContent = $chunkContent;
            } else {
                // Remove overlap from subsequent chunks
                $overlapSize = min($this->overlapSize, mb_strlen($chunkContent));
                $uniqueContent = mb_substr($chunkContent, $overlapSize);
                $reconstructedContent .= $uniqueContent;
            }
        }

        $originalLength = mb_strlen($originalContent);
        $reconstructedLength = mb_strlen($reconstructedContent);

        // Check length discrepancy (allow reasonable variance due to overlap and whitespace normalization)
        $lengthDifference = abs($originalLength - $reconstructedLength);

        // More lenient variance calculation considering overlaps
        $totalOverlapEstimate = (count($chunks) - 1) * $this->overlapSize;
        $allowedVariance = max(100, $originalLength * 0.05, $totalOverlapEstimate * 1.5); // 5% or 100 chars or 1.5x overlap estimate

        if ($lengthDifference > $allowedVariance) {
            $errors[] = "Significant length difference: original {$originalLength}, reconstructed {$reconstructedLength}, difference {$lengthDifference}, allowed {$allowedVariance}";
        }

        // Check for empty chunks
        foreach ($chunks as $index => $chunk) {
            if (empty(trim($chunk['content']))) {
                $errors[] = "Chunk " . ($index + 1) . " is empty";
            }
        }

        // Check for chunk sequence integrity with more practical validation
        if (count($chunks) > 1) {
            for ($i = 1; $i < count($chunks); $i++) {
                $prevChunk = $chunks[$i - 1]['content'];
                $currentChunk = $chunks[$i]['content'];

                // Check if chunks end/start with reasonable boundaries
                $prevEnd = mb_substr(trim($prevChunk), -20);
                $currentStart = mb_substr(trim($currentChunk), 0, 20);

                // Check for proper sentence/paragraph boundaries
                $hasProperBoundary = false;

                // Check if previous chunk ends with sentence terminator
                if (preg_match('/[。！？\n]/', $prevEnd)) {
                    $hasProperBoundary = true;
                }

                // Check if current chunk starts with dialogue or new paragraph
                if (preg_match('/^[「『]/', $currentStart) || preg_match('/^\s*[ぁ-んァ-ヶ一-龯]/', $currentStart)) {
                    $hasProperBoundary = true;
                }

                // Check if there's scene transition marker
                if (preg_match('/[＊\*]{3,}|[-=]{3,}/', $prevEnd . $currentStart)) {
                    $hasProperBoundary = true;
                }

                // Only flag if there's clearly a mid-word break
                if (!$hasProperBoundary && preg_match('/[ぁ-んァ-ヶ一-龯]$/', $prevEnd) && preg_match('/^[ぁ-んァ-ヶ一-龯]/', $currentStart)) {
                    // This might indicate a mid-word break, but be lenient
                    // Only add as warning, not error
                    // $errors[] = "Potential mid-word break between chunk " . $i . " and " . ($i + 1);
                }
            }
        }

        return [
            'complete' => empty($errors),
            'error' => implode('; ', $errors),
            'original_length' => $originalLength,
            'reconstructed_length' => $reconstructedLength,
            'length_difference' => $lengthDifference,
            'chunk_count' => count($chunks)
        ];
    }

    /**
     * Calculate string similarity for overlap validation
     */
    private function calculateStringSimilarity(string $str1, string $str2): float {
        if (empty($str1) || empty($str2)) {
            return 0.0;
        }

        $len1 = mb_strlen($str1);
        $len2 = mb_strlen($str2);
        $maxLen = max($len1, $len2);

        if ($maxLen === 0) {
            return 1.0;
        }

        // Simple character-based similarity
        $commonChars = 0;
        $minLen = min($len1, $len2);

        for ($i = 0; $i < $minLen; $i++) {
            if (mb_substr($str1, $i, 1) === mb_substr($str2, $i, 1)) {
                $commonChars++;
            }
        }

        return $commonChars / $maxLen;
    }

    /**
     * Validate saved chunks against original content
     */
    private function validateSavedChunks(int $chapterId, string $originalContent): array {
        $savedChunks = $this->getChapterChunks($chapterId);
        $errors = [];

        if (empty($savedChunks)) {
            return [
                'valid' => false,
                'error' => 'No chunks were saved to database'
            ];
        }

        // Check chunk sequence
        $expectedChunkNumber = 1;
        foreach ($savedChunks as $chunk) {
            if ($chunk['chunk_number'] != $expectedChunkNumber) {
                $errors[] = "Missing chunk number {$expectedChunkNumber}";
            }
            $expectedChunkNumber++;
        }

        // Reconstruct content from saved chunks
        $reconstructedContent = '';
        foreach ($savedChunks as $index => $chunk) {
            if ($index === 0) {
                $reconstructedContent = $chunk['original_content'];
            } else {
                // Remove overlap
                $overlapSize = min($this->overlapSize, mb_strlen($chunk['original_content']));
                $uniqueContent = mb_substr($chunk['original_content'], $overlapSize);
                $reconstructedContent .= $uniqueContent;
            }
        }

        // Compare with original
        $originalLength = mb_strlen($originalContent);
        $reconstructedLength = mb_strlen($reconstructedContent);
        $lengthDifference = abs($originalLength - $reconstructedLength);
        $allowedVariance = max(50, $originalLength * 0.02);

        if ($lengthDifference > $allowedVariance) {
            $errors[] = "Saved chunks don't match original content length";
        }

        return [
            'valid' => empty($errors),
            'error' => implode('; ', $errors),
            'saved_chunks_count' => count($savedChunks),
            'original_length' => $originalLength,
            'reconstructed_length' => $reconstructedLength
        ];
    }

    /**
     * Add enhanced context information to chunks for better translation
     */
    private function addEnhancedContextToChunks(array $chunks, string $fullContent): array {
        $enhancedChunks = [];

        foreach ($chunks as $index => $chunk) {
            $enhancedChunk = $chunk;

            // Add extended previous chunk context (increased from 100 to 300 chars)
            if ($index > 0) {
                $prevChunk = $chunks[$index - 1];
                $enhancedChunk['previous_context'] = mb_substr($prevChunk['content'], -300);

                // Add character names from previous chunk for consistency
                $enhancedChunk['previous_characters'] = $this->extractCharacterNamesFromText($prevChunk['content']);
            }

            // Add extended next chunk context (increased from 100 to 300 chars)
            if ($index < count($chunks) - 1) {
                $nextChunk = $chunks[$index + 1];
                $enhancedChunk['next_context'] = mb_substr($nextChunk['content'], 0, 300);
            }

            // Add narrative context analysis
            $enhancedChunk['narrative_context'] = $this->analyzeNarrativeContext($chunk['content'], $index, $chunks);

            // Add dialogue context if chunk contains dialogue
            $enhancedChunk['dialogue_context'] = $this->analyzeDialogueContext($chunk['content'], $index, $chunks);

            // Add position information
            $enhancedChunk['chunk_position'] = $index + 1;
            $enhancedChunk['total_chunks'] = count($chunks);
            $enhancedChunk['is_first_chunk'] = $index === 0;
            $enhancedChunk['is_last_chunk'] = $index === count($chunks) - 1;

            // Add content type analysis
            $enhancedChunk['content_analysis'] = $this->analyzeChunkContent($chunk['content']);

            $enhancedChunks[] = $enhancedChunk;
        }

        return $enhancedChunks;
    }

    /**
     * Simple content splitting (fallback method)
     */
    private function simpleSplit(string $content): array {
        $chunks = [];
        $contentLength = mb_strlen($content);
        $position = 0;

        while ($position < $contentLength) {
            $chunkSize = min($this->chunkSizeLimit, $contentLength - $position);
            $chunk = mb_substr($content, $position, $chunkSize);

            $chunks[] = $this->createChunkData($chunk);

            // Move position forward, ensuring we don't get stuck in infinite loop
            $advance = max($chunkSize - $this->overlapSize, 1);
            $position += $advance;

            // Safety check to prevent infinite loops
            if ($position >= $contentLength) {
                break;
            }
        }

        return $chunks;
    }

    /**
     * Detect chunk type based on content patterns
     */
    private function detectChunkType(string $content): string {
        // Check for dialogue patterns
        if (preg_match('/「[^」]*」/', $content) || preg_match('/"[^"]*"/', $content)) {
            return 'dialogue';
        }

        // Check for scene break patterns
        if (preg_match('/^\s*[＊\*]{3,}|^[-=]{3,}/', $content)) {
            return 'scene_break';
        }

        return 'paragraph';
    }

    /**
     * Get overlap content from the end of a chunk
     */
    private function getOverlapContent(string $content): string {
        if ($this->overlapSize <= 0) return '';

        $contentLength = mb_strlen($content);
        if ($contentLength <= $this->overlapSize) return $content;

        return mb_substr($content, $contentLength - $this->overlapSize);
    }

    /**
     * Save chunk to database
     */
    private function saveChunk(int $chapterId, int $chunkNumber, array $chunkData): void {
        $this->db->insert('chapter_chunks', [
            'chapter_id' => $chapterId,
            'chunk_number' => $chunkNumber,
            'original_content' => $chunkData['content'],
            'character_count' => $chunkData['character_count'],
            'word_count' => $chunkData['word_count'],
            'chunk_type' => $chunkData['type'],
            'translation_status' => 'pending'
        ]);
    }

    /**
     * Clear existing chunks for a chapter
     */
    private function clearExistingChunks(int $chapterId): void {
        $this->db->delete('chapter_chunks', 'chapter_id = ?', [$chapterId]);
    }

    /**
     * Get chunks for a chapter
     */
    public function getChapterChunks(int $chapterId): array {
        return $this->db->fetchAll(
            "SELECT * FROM chapter_chunks WHERE chapter_id = ? ORDER BY chunk_number",
            [$chapterId]
        );
    }

    /**
     * Check if chapter has chunks
     */
    public function hasChunks(int $chapterId): bool {
        $count = $this->db->fetchOne(
            "SELECT COUNT(*) as count FROM chapter_chunks WHERE chapter_id = ?",
            [$chapterId]
        );
        return $count['count'] > 0;
    }

    /**
     * Update chunk size configuration
     */
    public function updateChunkSize(int $newSize): bool {
        try {
            $this->db->update(
                'user_preferences',
                ['preference_value' => (string)$newSize],
                'preference_key = ?',
                ['chunk_size_limit']
            );
            $this->chunkSizeLimit = $newSize;
            return true;
        } catch (Exception $e) {
            return false;
        }
    }

    /**
     * Adapt chunk size based on translation performance
     */
    public function adaptChunkSizeBasedOnPerformance(int $chapterId): array {
        try {
            // Get recent translation performance data
            $performanceData = $this->getTranslationPerformanceData($chapterId);

            if (empty($performanceData)) {
                return ['success' => false, 'message' => 'No performance data available'];
            }

            $timeoutRate = $performanceData['timeout_rate'];
            $avgTranslationTime = $performanceData['avg_translation_time'];
            $currentChunkSize = $this->chunkSizeLimit;

            $newChunkSize = $currentChunkSize;
            $recommendation = '';

            // If timeout rate is high, reduce chunk size
            if ($timeoutRate > 0.3) { // More than 30% timeout rate
                $newChunkSize = max($this->minChunkSize, $currentChunkSize * 0.7);
                $recommendation = 'Reduced chunk size due to high timeout rate';
            }
            // If translation time is consistently high, reduce chunk size
            elseif ($avgTranslationTime > 180) { // More than 3 minutes per chunk
                $newChunkSize = max($this->minChunkSize, $currentChunkSize * 0.8);
                $recommendation = 'Reduced chunk size due to long translation times';
            }
            // If performance is good, we can try increasing chunk size
            elseif ($timeoutRate < 0.1 && $avgTranslationTime < 60) {
                $newChunkSize = min($this->maxChunkSize, $currentChunkSize * 1.2);
                $recommendation = 'Increased chunk size due to good performance';
            }

            if ($newChunkSize != $currentChunkSize) {
                $this->updateChunkSize((int)$newChunkSize);
                return [
                    'success' => true,
                    'old_size' => $currentChunkSize,
                    'new_size' => $newChunkSize,
                    'recommendation' => $recommendation,
                    'performance_data' => $performanceData
                ];
            }

            return [
                'success' => true,
                'message' => 'No chunk size adjustment needed',
                'current_size' => $currentChunkSize,
                'performance_data' => $performanceData
            ];

        } catch (Exception $e) {
            return ['success' => false, 'error' => $e->getMessage()];
        }
    }

    /**
     * Extract character names from text for context preservation
     */
    private function extractCharacterNamesFromText(string $text): array {
        $names = [];

        // Japanese name patterns with honorifics
        $patterns = [
            '/([ぁ-んァ-ヶ一-龯]{2,4})(?:さん|くん|ちゃん|様|殿|君|氏|先生)/u',
            '/「([ぁ-んァ-ヶ一-龯]{2,4})」/u', // Names in quotes
            '/([ぁ-んァ-ヶ一-龯]{2,4})(?:は|が|を|に|の)/u' // Names with particles
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches)) {
                $names = array_merge($names, $matches[1]);
            }
        }

        // Remove duplicates and common words
        $names = array_unique($names);
        $commonWords = ['それ', 'これ', 'あれ', 'その', 'この', 'あの', 'ここ', 'そこ', 'あそこ'];
        $names = array_diff($names, $commonWords);

        return array_values($names);
    }

    /**
     * Analyze narrative context of a chunk
     */
    private function analyzeNarrativeContext(string $content, int $chunkIndex, array $allChunks): array {
        $context = [
            'has_scene_transition' => false,
            'has_time_transition' => false,
            'narrative_voice' => 'third_person',
            'tense' => 'past',
            'setting_indicators' => [],
            'narrative_confidence' => 0.0,
            'perspective_indicators' => []
        ];

        // Check for scene transitions
        if (preg_match('/[＊\*]{3,}|[-=]{5,}/', $content)) {
            $context['has_scene_transition'] = true;
        }

        // Check for time transitions
        $timePatterns = [
            '/(?:翌日|次の日|その後|しばらく|数時間|数分|朝|昼|夜|夕方)/u',
            '/(?:meanwhile|later|next day|hours later|morning|evening)/i'
        ];

        foreach ($timePatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $context['has_time_transition'] = true;
                break;
            }
        }

        // Enhanced narrative voice detection with multi-language support
        $narrativeAnalysis = $this->detectNarrativePerspective($content);
        $context['narrative_voice'] = $narrativeAnalysis['perspective'];
        $context['narrative_confidence'] = $narrativeAnalysis['confidence'];
        $context['perspective_indicators'] = $narrativeAnalysis['indicators'];

        // Check consistency with previous chunks in the same chapter
        if ($chunkIndex > 0) {
            $prevChunkContext = $allChunks[$chunkIndex - 1]['narrative_context'] ?? null;
            if ($prevChunkContext && $prevChunkContext['narrative_voice'] !== $context['narrative_voice']) {
                // If there's a perspective mismatch, use the more confident detection
                if ($prevChunkContext['narrative_confidence'] > $context['narrative_confidence']) {
                    $context['narrative_voice'] = $prevChunkContext['narrative_voice'];
                    $context['perspective_override'] = true;
                    $context['override_reason'] = 'consistency_with_previous_chunk';
                }
            }
        }

        // Extract setting indicators
        $settingPatterns = [
            '/([ぁ-んァ-ヶ一-龯]{2,6})(?:で|に|へ|から)/u', // Location particles
            '/([ぁ-んァ-ヶ一-龯]{2,6})(?:部屋|教室|廊下|街|村|町|城|宮殿)/u' // Location words
        ];

        foreach ($settingPatterns as $pattern) {
            if (preg_match_all($pattern, $content, $matches)) {
                $context['setting_indicators'] = array_merge($context['setting_indicators'], $matches[1]);
            }
        }

        $context['setting_indicators'] = array_unique($context['setting_indicators']);

        return $context;
    }

    /**
     * Detect narrative perspective with enhanced multi-language support
     */
    private function detectNarrativePerspective(string $content): array {
        $firstPersonIndicators = [];
        $thirdPersonIndicators = [];
        $confidence = 0.0;

        // Japanese first-person pronouns and patterns
        $japaneseFirstPerson = [
            '/(?:私|僕|俺|わたし|ぼく|おれ)(?:は|が|を|に|の|と|で|から|まで)/u' => 1.0,
            '/(?:私|僕|俺)(?:たち|達)(?:は|が|を|に)/u' => 0.9,
            '/(?:私|僕|俺)(?:自身|じしん)/u' => 0.8,
            '/(?:私|僕|俺)(?:の|が)(?:心|気持ち|思い)/u' => 0.7
        ];

        // Chinese first-person pronouns
        $chineseFirstPerson = [
            '/(?:我|我们|咱|咱们)(?:走|去|来|到|在|有|要|会|能|可以|是|的)/u' => 1.0,
            '/(?:我|我们)(?:觉得|认为|想|希望|需要|担心|问道|说)/u' => 0.9,
            '/(?:我|我们)(?:自己|本人)/u' => 0.8,
            '/(?:我|我们)(?:很|非常|特别|十分)/u' => 0.7
        ];

        // Korean first-person pronouns
        $koreanFirstPerson = [
            '/(?:나|내|저|제|우리)(?:는|가|를|을|의|에|와|과)/u' => 1.0,
            '/(?:나|저)(?:자신|스스로)/u' => 0.8,
            '/(?:우리|저희)(?:는|가|를)/u' => 0.9
        ];

        // Japanese third-person indicators
        $japaneseThirdPerson = [
            '/(?:彼|彼女|彼ら|彼女ら)(?:は|が|を|に)/u' => 0.8,
            '/(?:[ぁ-んァ-ヶ一-龯]{2,6})(?:は|が)(?:思った|考えた|感じた)/u' => 0.6,
            '/(?:[ぁ-んァ-ヶ一-龯]{2,6})(?:の|が)(?:心|気持ち|思い)/u' => 0.5
        ];

        // Check for first-person patterns
        foreach (array_merge($japaneseFirstPerson, $chineseFirstPerson, $koreanFirstPerson) as $pattern => $weight) {
            if (preg_match_all($pattern, $content, $matches)) {
                $count = count($matches[0]);
                $firstPersonIndicators = array_merge($firstPersonIndicators, $matches[0]);
                $confidence += $count * $weight;
            }
        }

        // Check for third-person patterns
        foreach ($japaneseThirdPerson as $pattern => $weight) {
            if (preg_match_all($pattern, $content, $matches)) {
                $count = count($matches[0]);
                $thirdPersonIndicators = array_merge($thirdPersonIndicators, $matches[0]);
                $confidence -= $count * $weight; // Negative for third person
            }
        }

        // Determine perspective based on confidence score
        if ($confidence > 0.5) {
            $perspective = 'first_person';
            $finalConfidence = min($confidence / 3.0, 1.0); // Normalize confidence
        } elseif ($confidence < -0.5) {
            $perspective = 'third_person';
            $finalConfidence = min(abs($confidence) / 3.0, 1.0);
        } else {
            // Default to third person with low confidence
            $perspective = 'third_person';
            $finalConfidence = 0.3;
        }

        return [
            'perspective' => $perspective,
            'confidence' => $finalConfidence,
            'indicators' => [
                'first_person' => array_unique($firstPersonIndicators),
                'third_person' => array_unique($thirdPersonIndicators)
            ]
        ];
    }

    /**
     * Analyze dialogue context in a chunk
     */
    private function analyzeDialogueContext(string $content, int $chunkIndex, array $allChunks): array {
        $context = [
            'has_dialogue' => false,
            'dialogue_speakers' => [],
            'dialogue_count' => 0,
            'is_dialogue_heavy' => false,
            'conversation_flow' => 'none'
        ];

        // Count dialogue instances
        $dialogueCount = preg_match_all('/「[^」]*」|"[^"]*"/u', $content, $dialogueMatches);
        $context['dialogue_count'] = $dialogueCount;
        $context['has_dialogue'] = $dialogueCount > 0;

        // Determine if dialogue-heavy (more than 3 dialogues per 1000 chars)
        $contentLength = mb_strlen($content);
        $dialogueRatio = $contentLength > 0 ? ($dialogueCount / $contentLength) * 1000 : 0;
        $context['is_dialogue_heavy'] = $dialogueRatio > 3;

        // Extract potential speakers (names before dialogue)
        if ($context['has_dialogue']) {
            $speakerPattern = '/([ぁ-んァ-ヶ一-龯]{2,4})(?:は|が).*?「[^」]*」/u';
            if (preg_match_all($speakerPattern, $content, $speakerMatches)) {
                $context['dialogue_speakers'] = array_unique($speakerMatches[1]);
            }

            // Analyze conversation flow
            if ($dialogueCount > 2) {
                $context['conversation_flow'] = 'multi_turn';
            } elseif ($dialogueCount > 0) {
                $context['conversation_flow'] = 'single_exchange';
            }
        }

        return $context;
    }

    /**
     * Analyze content type and characteristics of a chunk
     */
    private function analyzeChunkContent(string $content): array {
        $analysis = [
            'content_type' => 'mixed',
            'description_ratio' => 0,
            'dialogue_ratio' => 0,
            'action_ratio' => 0,
            'has_sound_effects' => false,
            'complexity_score' => 0
        ];

        $contentLength = mb_strlen($content);
        if ($contentLength === 0) {
            return $analysis;
        }

        // Calculate dialogue ratio
        $dialogueLength = 0;
        if (preg_match_all('/「([^」]*)」|"([^"]*)"/u', $content, $matches)) {
            foreach ($matches[0] as $dialogue) {
                $dialogueLength += mb_strlen($dialogue);
            }
        }
        $analysis['dialogue_ratio'] = $dialogueLength / $contentLength;

        // Detect sound effects with enhanced patterns
        $soundEffectPatterns = [
            '/[ぁ-んァ-ヶ]{3,}/', // Repeated kana (onomatopoeia)
            '/[！？]{2,}/', // Multiple exclamation/question marks
            '/[。]{3,}/', // Ellipsis
            '/「[ァ-ヶー・ッっ！？]+」/', // Quoted katakana/hiragana (common for sound effects)
            '/[ドバガズビパダボポキギチリピビジシカタファパマラサザナハヤワ]{2,}/', // Common sound effect patterns
        ];

        foreach ($soundEffectPatterns as $pattern) {
            if (preg_match($pattern, $content)) {
                $analysis['has_sound_effects'] = true;
                break;
            }
        }

        // Determine primary content type
        if ($analysis['dialogue_ratio'] > 0.6) {
            $analysis['content_type'] = 'dialogue_heavy';
        } elseif ($analysis['dialogue_ratio'] < 0.2) {
            $analysis['content_type'] = 'narrative_heavy';
        } else {
            $analysis['content_type'] = 'balanced';
        }

        // Calculate complexity score based on various factors
        $kanjiCount = preg_match_all('/[一-龯]/u', $content);
        $kanjiRatio = $kanjiCount / $contentLength;
        $sentenceCount = preg_match_all('/[。！？]/u', $content);
        $avgSentenceLength = $sentenceCount > 0 ? $contentLength / $sentenceCount : $contentLength;

        $analysis['complexity_score'] = ($kanjiRatio * 0.4) +
                                       (min($avgSentenceLength / 100, 1) * 0.3) +
                                       ($analysis['dialogue_ratio'] * 0.3);

        return $analysis;
    }

    /**
     * Get translation performance data for adaptive sizing
     */
    private function getTranslationPerformanceData(int $chapterId): array {
        try {
            // Get recent chunk translation data
            $recentData = $this->db->fetchAll(
                "SELECT cc.character_count, cc.translation_status,
                        TIMESTAMPDIFF(SECOND, cc.updated_at, cc.translation_date) as translation_time
                 FROM chapter_chunks cc
                 JOIN chapters c ON cc.chapter_id = c.id
                 WHERE c.novel_id = (SELECT novel_id FROM chapters WHERE id = ?)
                 AND cc.translation_date IS NOT NULL
                 AND cc.updated_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
                 ORDER BY cc.translation_date DESC
                 LIMIT 50",
                [$chapterId]
            );

            if (empty($recentData)) {
                return [];
            }

            $totalChunks = count($recentData);
            $timeoutCount = 0;
            $totalTime = 0;
            $validTimeCount = 0;

            foreach ($recentData as $chunk) {
                if ($chunk['translation_status'] === 'error') {
                    $timeoutCount++;
                }

                if ($chunk['translation_time'] > 0 && $chunk['translation_time'] < 600) { // Valid time range
                    $totalTime += $chunk['translation_time'];
                    $validTimeCount++;
                }
            }

            return [
                'total_chunks' => $totalChunks,
                'timeout_count' => $timeoutCount,
                'timeout_rate' => $totalChunks > 0 ? $timeoutCount / $totalChunks : 0,
                'avg_translation_time' => $validTimeCount > 0 ? $totalTime / $validTimeCount : 0,
                'sample_size' => $totalChunks
            ];

        } catch (Exception $e) {
            error_log("ChapterChunker: Error getting performance data: " . $e->getMessage());
            return [];
        }
    }

    /**
     * Get chunking statistics for monitoring
     */
    public function getChunkingStatistics(): array {
        try {
            $stats = $this->db->fetchOne(
                "SELECT
                    COUNT(DISTINCT cc.chapter_id) as chunked_chapters,
                    COUNT(cc.id) as total_chunks,
                    AVG(cc.character_count) as avg_chunk_size,
                    MIN(cc.character_count) as min_chunk_size,
                    MAX(cc.character_count) as max_chunk_size,
                    SUM(CASE WHEN cc.translation_status = 'completed' THEN 1 ELSE 0 END) as completed_chunks,
                    SUM(CASE WHEN cc.translation_status = 'error' THEN 1 ELSE 0 END) as failed_chunks
                 FROM chapter_chunks cc"
            );

            $recentPerformance = $this->db->fetchOne(
                "SELECT
                    AVG(TIMESTAMPDIFF(SECOND, cc.updated_at, cc.translation_date)) as avg_translation_time,
                    COUNT(CASE WHEN cc.translation_status = 'error' THEN 1 END) / COUNT(*) as error_rate
                 FROM chapter_chunks cc
                 WHERE cc.translation_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)"
            );

            return [
                'chunked_chapters' => (int)($stats['chunked_chapters'] ?? 0),
                'total_chunks' => (int)($stats['total_chunks'] ?? 0),
                'avg_chunk_size' => round((float)($stats['avg_chunk_size'] ?? 0)),
                'min_chunk_size' => (int)($stats['min_chunk_size'] ?? 0),
                'max_chunk_size' => (int)($stats['max_chunk_size'] ?? 0),
                'completed_chunks' => (int)($stats['completed_chunks'] ?? 0),
                'failed_chunks' => (int)($stats['failed_chunks'] ?? 0),
                'success_rate' => $stats['total_chunks'] > 0 ?
                    round(($stats['completed_chunks'] / $stats['total_chunks']) * 100, 2) : 0,
                'avg_translation_time' => round((float)($recentPerformance['avg_translation_time'] ?? 0)),
                'recent_error_rate' => round(((float)($recentPerformance['error_rate'] ?? 0)) * 100, 2),
                'current_chunk_size_limit' => $this->chunkSizeLimit,
                'dynamic_sizing_enabled' => $this->dynamicSizing
            ];

        } catch (Exception $e) {
            return ['error' => $e->getMessage()];
        }
    }
}
