<?php

/**
 * Service for handling Japanese and Chinese honorifics preservation during translation
 */
class HonorificService {

    /**
     * Japanese honorifics and suffixes that must be preserved (romanized forms)
     */
    private const JAPANESE_HONORIFICS = [
        // Common honorifics
        '-kun', '-chan', '-sama', '-san', '-senpai', '-sensei', '-shisou',

        // Family terms
        'onii-chan', 'onee-chan', 'oji-san', 'ojisan', 'ojichan', 'ochan', 'oba-chan', 'obasan',
        'otou-san', 'okaa-san', 'otou-sama', 'okaa-sama', 'ojiichan', 'obaachan',

        // Formal titles
        'ojo-sama', 'kei-sama', 'ni-sama', 'tenchoo',

        // Casual terms
        'ossan', 'obaa-chan', 'jii-chan', 'jiichan',

        // Additional variations
        'kun', 'chan', 'sama', 'san', 'senpai', 'sensei', 'shisou',
        'onii', 'onee', 'oji', 'oba', 'otou', 'okaa',

        // Martial arts terms
        'shihan', 'sempai', 'kohai'
    ];

    /**
     * Japanese script honorifics (hiragana/kanji) with their romanized equivalents
     */
    private const JAPANESE_SCRIPT_HONORIFICS = [
        // Common honorifics in Japanese script
        'さん' => '-san',
        'くん' => '-kun',
        'ちゃん' => '-chan',
        '様' => '-sama',
        '君' => '-kun',
        '先生' => '-sensei',
        '先輩' => '-senpai',
        '師匠' => '-shisou',

        // Family terms in Japanese script
        'おじさん' => 'ojisan',
        'おばさん' => 'obasan',
        'おじいちゃん' => 'ojiichan',
        'おばあちゃん' => 'obaachan',
        'お兄ちゃん' => 'onii-chan',
        'お姉ちゃん' => 'onee-chan',
        'お父さん' => 'otou-san',
        'お母さん' => 'okaa-san',
        'お父様' => 'otou-sama',
        'お母様' => 'okaa-sama',
        'おじちゃん' => 'ojichan',
        'おばちゃん' => 'obachan',
        'じいちゃん' => 'jiichan',
        'ばあちゃん' => 'baachan',

        // Variations without 'お' prefix
        '兄ちゃん' => 'niichan',
        '姉ちゃん' => 'neechan',
        '父さん' => 'tou-san',
        '母さん' => 'kaa-san',

        // Formal variations
        'お嬢様' => 'ojo-sama',
        '殿' => '-dono',
        '氏' => '-shi',

        // Casual terms
        'おっさん' => 'ossan'
    ];
    
    /**
     * Chinese honorifics and titles that must be preserved
     */
    private const CHINESE_HONORIFICS = [
        // Family terms
        '-ge', '-jie', 'dage', 'jiejie', 'meimei', 'didi',
        'xiongdi', 'jiemei', 'gege', 'dajie',

        // Martial/cultivation terms
        'shifu', 'shizun', 'shixiong', 'shidi', 'shijie', 'shimei',
        'shibo', 'shishu', 'shigu', 'shigong', 'shiye',

        // Formal titles
        'gongzi', 'xiaojie', 'laoshi', 'xiansheng', 'furen',
        'laozu', 'qianbei', 'wanbei', 'zhangbei',

        // Imperial/noble titles
        'wang-ye', 'dianxia', 'bixia', 'zongzhu', 'zhanglao',

        // Respectful terms
        'lao', 'xiao', 'da', 'er', 'san', 'si', 'wu',

        // Additional variations without hyphens
        'ge', 'jie', 'xiong', 'mei', 'di',
        'wang', 'ye', 'zun', 'jun'
    ];

    /**
     * Korean honorifics and titles that must be preserved
     */
    private const KOREAN_HONORIFICS = [
        // Formal honorifics
        '-nim', '-ssi', 'nim', 'ssi',

        // Casual honorifics
        '-ah', '-ya', 'ah', 'ya',

        // Academic/workplace terms
        'sunbae', 'hoobae', 'seonsaengnim', 'gyosunim',
        'sajangnim', 'bujangnim', 'gwajangnim', 'daepyonim',

        // Family terms
        'oppa', 'unni', 'hyung', 'noona', 'eonni',
        'abeoji', 'eomeoni', 'harabeoji', 'halmeoni',
        'samchon', 'imo', 'gomo', 'oesamchon',

        // Formal titles
        'jeonha', 'mama', 'daegam', 'naeuri',
        'yangban', 'gongja', 'agassi', 'doryeongnim',

        // Professional titles
        'uisa', 'byeonhosa', 'gyosa', 'gisa',
        'gijanim', 'jakganim', 'gamsanim'
    ];
    
    /**
     * Patterns to detect honorifics in text by language
     */
    private const HONORIFIC_PATTERNS = [
        'japanese' => [
            // Japanese romanized patterns
            '/([^\s\p{P}]+)(?:-?(?:kun|chan|sama|san|senpai|sensei|shisou))\b/ui',
            '/\b(?:onii|onee|oji|oba|otou|okaa)(?:-?(?:chan|san|sama))\b/ui',
            '/\b(?:ojo|kei|ni)-?sama\b/ui',

            // Japanese script patterns (hiragana/kanji)
            '/[\p{Hiragana}\p{Katakana}\p{Han}]*(?:さん|くん|ちゃん|様|君|先生|先輩|師匠)/u',
            '/(?:おじさん|おばさん|おじいちゃん|おばあちゃん|お兄ちゃん|お姉ちゃん|お父さん|お母さん)/u',
            '/(?:おじちゃん|おばちゃん|じいちゃん|ばあちゃん|兄ちゃん|姉ちゃん|父さん|母さん)/u',
            '/(?:お嬢様|殿|氏|おっさん)/u'
        ],
        'chinese' => [
            // Chinese patterns
            '/([^\s\p{P}]+)(?:-?(?:ge|jie|xiong|mei|di))\b/ui',
            '/\b(?:shi(?:fu|zun|xiong|di|jie|mei|bo|shu|gu|gong|ye))\b/ui',
            '/\b(?:gongzi|xiaojie|laoshi|xiansheng|furen|laozu|qianbei)\b/ui',
            '/\b(?:wang-?ye|dianxia|bixia|zongzhu)\b/ui'
        ],
        'korean' => [
            // Korean patterns
            '/([^\s\p{P}]+)(?:-?(?:nim|ssi|ah|ya))\b/ui',
            '/\b(?:sunbae|hoobae|seonsaengnim|gyosunim|sajangnim)\b/ui',
            '/\b(?:oppa|unni|hyung|noona|eonni)\b/ui',
            '/\b(?:abeoji|eomeoni|harabeoji|halmeoni)\b/ui',
            '/\b(?:jeonha|mama|daegam|naeuri|yangban)\b/ui'
        ]
    ];

    /**
     * Detect the primary language of the text
     */
    public function detectLanguage(string $text): string {
        // Use specific Unicode ranges instead of character classes for better compatibility

        // Check for Korean characters (Hangul) - U+AC00 to U+D7AF
        if (preg_match('/[\x{AC00}-\x{D7AF}]/u', $text)) {
            return 'korean';
        }

        // Check for Japanese Hiragana (U+3040 to U+309F) or Katakana (U+30A0 to U+30FF)
        if (preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}]/u', $text)) {
            return 'japanese';
        }

        // Check for Chinese characters (CJK Unified Ideographs) - U+4E00 to U+9FFF
        // Only if no Hiragana/Katakana found (to distinguish from Japanese)
        if (preg_match('/[\x{4E00}-\x{9FFF}]/u', $text) &&
            !preg_match('/[\x{3040}-\x{309F}\x{30A0}-\x{30FF}]/u', $text)) {
            return 'chinese';
        }

        // Default to auto-detection if no specific script found
        return 'auto';
    }

    /**
     * Get honorifics list for a specific language
     */
    private function getHonorificsForLanguage(string $language): array {
        switch ($language) {
            case 'japanese':
                return self::JAPANESE_HONORIFICS;
            case 'chinese':
                return self::CHINESE_HONORIFICS;
            case 'korean':
                return self::KOREAN_HONORIFICS;
            default:
                // For auto or unknown languages, include all honorifics
                return array_merge(
                    self::JAPANESE_HONORIFICS,
                    self::CHINESE_HONORIFICS,
                    self::KOREAN_HONORIFICS
                );
        }
    }

    /**
     * Get honorific patterns for a specific language
     */
    private function getPatternsForLanguage(string $language): array {
        if (isset(self::HONORIFIC_PATTERNS[$language])) {
            return self::HONORIFIC_PATTERNS[$language];
        }

        // For auto or unknown languages, include all patterns
        return array_merge(
            self::HONORIFIC_PATTERNS['japanese'],
            self::HONORIFIC_PATTERNS['chinese'],
            self::HONORIFIC_PATTERNS['korean']
        );
    }

    /**
     * Preprocess text to mark honorifics for preservation
     */
    public function preprocessTextForTranslation(string $text, string $sourceLanguage = 'auto'): array {
        $honorifics = [];
        $markerIndex = 0;

        // Detect language if not specified
        if ($sourceLanguage === 'auto') {
            $sourceLanguage = $this->detectLanguage($text);
        }

        // Collect all honorific matches with their positions and priorities
        $allMatches = [];

        // First, find Japanese script honorifics (higher priority) - only for Japanese text
        if ($sourceLanguage === 'japanese' || $sourceLanguage === 'auto') {
            foreach (self::JAPANESE_SCRIPT_HONORIFICS as $japaneseScript => $romanized) {
                $pattern = '/' . preg_quote($japaneseScript, '/') . '/u';

                if (preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
                    foreach ($matches[0] as $match) {
                        // Convert byte position to character position
                        $beforeText = substr($text, 0, $match[1]);
                        $charStart = mb_strlen($beforeText, 'UTF-8');
                        $charLength = mb_strlen($match[0], 'UTF-8');

                        $allMatches[] = [
                            'original' => $match[0],
                            'romanized' => $romanized,
                            'start' => $charStart,
                            'end' => $charStart + $charLength,
                            'length' => $charLength,
                            'type' => 'japanese_script',
                            'priority' => $charLength // Longer matches have higher priority
                        ];
                    }
                }
            }
        }

        // Then, find romanized honorifics (lower priority) - language-specific
        $languageSpecificHonorifics = $this->getHonorificsForLanguage($sourceLanguage);

        foreach ($languageSpecificHonorifics as $honorific) {
            $pattern = '/\b' . preg_quote($honorific, '/') . '\b/ui';

            if (preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $match) {
                    // Convert byte position to character position for consistency
                    $beforeText = substr($text, 0, $match[1]);
                    $charStart = mb_strlen($beforeText, 'UTF-8');
                    $charLength = mb_strlen($match[0], 'UTF-8');

                    // Check if this honorific should be preserved based on context
                    if ($this->shouldPreserveHonorific($match[0], $text, $charStart)) {
                        $allMatches[] = [
                            'original' => $match[0],
                            'romanized' => $this->normalizeHonorific($match[0]),
                            'start' => $charStart,
                            'end' => $charStart + $charLength,
                            'length' => $charLength,
                            'type' => $this->getHonorificType($match[0]),
                            'priority' => $charLength
                        ];
                    }
                }
            }
        }

        // Sort matches by priority (longer first) and position
        usort($allMatches, function($a, $b) {
            if ($a['priority'] !== $b['priority']) {
                return $b['priority'] - $a['priority']; // Higher priority first
            }
            return $a['start'] - $b['start']; // Earlier position first
        });

        // Remove overlapping matches (keep higher priority ones)
        $filteredMatches = [];
        foreach ($allMatches as $match) {
            $overlaps = false;
            foreach ($filteredMatches as $existing) {
                // Check for any overlap between the two ranges
                if (($match['start'] < $existing['end'] && $match['end'] > $existing['start'])) {
                    $overlaps = true;
                    break;
                }
            }
            if (!$overlaps) {
                $filteredMatches[] = $match;
            }
        }

        // Sort by position for replacement (reverse order to maintain positions)
        usort($filteredMatches, function($a, $b) {
            return $b['start'] - $a['start'];
        });

        // Replace honorifics with markers
        $processedText = $text;
        foreach ($filteredMatches as $match) {
            $marker = "HONORIFIC_MARKER_{$markerIndex}";
            $markerIndex++;

            // Store honorific info
            $honorifics[$marker] = [
                'original' => $match['original'],
                'romanized' => $match['romanized'],
                'position' => $match['start'],
                'type' => $match['type']
            ];

            // Replace in text with marker
            if ($match['type'] === 'japanese_script') {
                // Use mb_substr for multibyte characters
                $processedText = mb_substr($processedText, 0, $match['start'], 'UTF-8') .
                               $marker .
                               mb_substr($processedText, $match['start'] + $match['length'], null, 'UTF-8');
            } else {
                // Use substr for ASCII characters
                $processedText = substr_replace(
                    $processedText,
                    $marker,
                    $match['start'],
                    $match['length']
                );
            }
        }

        return [
            'text' => $processedText,
            'honorifics' => $honorifics
        ];
    }
    
    /**
     * Postprocess translated text to restore honorifics
     */
    public function postprocessTranslatedText(string $translatedText, array $honorifics): string {
        $restoredText = $translatedText;
        
        foreach ($honorifics as $marker => $honorificData) {
            // Replace marker with romanized honorific
            $restoredText = str_replace($marker, $honorificData['romanized'], $restoredText);
        }
        
        return $restoredText;
    }
    
    /**
     * Normalize honorific to standard romanized form
     */
    private function normalizeHonorific(string $honorific): string {
        $normalized = strtolower(trim($honorific));

        // Remove leading/trailing hyphens for normalization
        $normalized = trim($normalized, '-');

        // Standardize common variations
        $standardizations = [
            'sempai' => 'senpai',
            'sensai' => 'sensei',
            'sama' => '-sama',
            'san' => '-san',
            'kun' => '-kun',
            'chan' => '-chan',
            'senpai' => '-senpai',
            'sensei' => '-sensei',
            'shisou' => '-shisou',
            'ojisan' => 'ojisan',
            'obasan' => 'obasan',
            'ojiichan' => 'ojiichan',
            'obaachan' => 'obaachan'
        ];

        if (isset($standardizations[$normalized])) {
            return $standardizations[$normalized];
        }

        // Add hyphen prefix for single honorifics if not present
        if (in_array($normalized, ['ge', 'jie', 'kun', 'chan', 'san', 'sama', 'senpai', 'sensei'])) {
            return '-' . $normalized;
        }

        return $normalized;
    }
    
    /**
     * Check if an honorific should be preserved based on context
     */
    private function shouldPreserveHonorific(string $honorific, string $text, int $position): bool {
        $normalized = strtolower(trim($honorific, '-'));

        // Always preserve family terms and standalone honorifics that are clearly cultural
        $alwaysPreserve = [
            'onii-chan', 'onee-chan', 'oji-san', 'ojisan', 'oba-chan', 'obasan',
            'otou-san', 'okaa-san', 'ojiichan', 'obaachan', 'shifu', 'shizun',
            'gongzi', 'xiaojie', 'wang-ye', 'dianxia'
        ];

        if (in_array($normalized, $alwaysPreserve)) {
            return true;
        }

        // For common honorifics like -san, -kun, -chan, -sama, sensei, senpai
        // only preserve if they're attached to a name or in appropriate context
        $contextualHonorifics = ['san', 'kun', 'chan', 'sama', 'sensei', 'senpai', 'shisou'];

        if (in_array($normalized, $contextualHonorifics)) {
            // Check if it's attached to a name (preceded by a word character)
            $beforeChar = $position > 0 ? mb_substr($text, $position - 1, 1, 'UTF-8') : '';
            $afterChar = mb_substr($text, $position + mb_strlen($honorific, 'UTF-8'), 1, 'UTF-8');

            // If preceded by a hyphen or word character, it's likely attached to a name
            if ($beforeChar === '-' || preg_match('/\w/', $beforeChar)) {
                return true;
            }

            // Special case for "sensei" - only preserve if it's clearly a title
            if ($normalized === 'sensei') {
                // Look for patterns like "Tanaka-sensei" or "the sensei" but not "The sensei taught"
                $beforeContext = mb_substr($text, max(0, $position - 20), 20, 'UTF-8');
                $afterContext = mb_substr($text, $position + mb_strlen($honorific, 'UTF-8'), 20, 'UTF-8');

                // Check if it's preceded by "the " (case insensitive)
                $contextAroundSensei = mb_substr($text, max(0, $position - 10), 10 + mb_strlen($honorific, 'UTF-8'), 'UTF-8');

                // If it's preceded by a name or "the", preserve it
                if (preg_match('/[A-Z][a-z]+[-\s]?$/', $beforeContext) ||
                    preg_match('/\bthe\s+sensei/i', $contextAroundSensei)) {
                    // Only exclude if followed by common teaching verbs in active voice
                    if (preg_match('/^\s+(taught|teaches|explained|showed|demonstrated)\b/', $afterContext)) {
                        return false;
                    }
                    return true;
                }

                return false; // Don't preserve standalone "sensei" in most contexts
            }

            return false; // Don't preserve other contextual honorifics without proper context
        }

        // For other honorifics, preserve by default
        return true;
    }

    /**
     * Get the type of honorific (Japanese, Chinese, Korean, or Japanese script)
     */
    private function getHonorificType(string $honorific): string {
        $normalized = strtolower(trim($honorific, '-'));

        // Check if it's a Japanese script honorific
        if (array_key_exists($honorific, self::JAPANESE_SCRIPT_HONORIFICS)) {
            return 'japanese_script';
        }

        if (in_array($normalized, array_map('strtolower', self::JAPANESE_HONORIFICS))) {
            return 'japanese';
        }

        if (in_array($normalized, array_map('strtolower', self::CHINESE_HONORIFICS))) {
            return 'chinese';
        }

        if (in_array($normalized, array_map('strtolower', self::KOREAN_HONORIFICS))) {
            return 'korean';
        }

        return 'unknown';
    }
    
    /**
     * Get honorific preservation instructions for translation prompts
     */
    public function getHonorificPreservationInstructions(string $sourceLanguage = 'auto'): string {
        $instructions = "\nHONORIFIC PRESERVATION RULES:\n" .
                       "- ONLY preserve honorifics when they are attached to character names or used as standalone terms\n" .
                       "- NEVER add honorifics to regular English pronouns (you, her, him, she, they)\n" .
                       "- Do NOT add honorifics where they don't exist in the original text\n" .
                       "- Translate regular pronouns and words normally without adding honorifics\n";

        // Add language-specific instructions
        switch ($sourceLanguage) {
            case 'japanese':
                $instructions .= "- NEVER translate or remove Japanese honorifics: -kun, -chan, -sama, -san, -senpai, -sensei, -shisou\n" .
                               "- NEVER translate family terms: onii-chan, onee-chan, oji-san, ojisan, oba-chan, obasan, etc.\n" .
                               "- NEVER translate Japanese script honorifics: さん, くん, ちゃん, 様, おじさん, おばさん, etc.\n" .
                               "- Convert Japanese script honorifics to romanized form (おじさん → ojisan)\n" .
                               "- Keep ALL Japanese honorifics in their original romanized form when they appear with names\n";
                break;

            case 'chinese':
                $instructions .= "- NEVER translate or remove Chinese honorifics: -ge, -jie, shifu, shizun, gongzi, xiaojie, etc.\n" .
                               "- NEVER translate martial/cultivation terms: shixiong, shidi, shijie, shimei, shibo, shishu, etc.\n" .
                               "- NEVER translate family terms: dage, jiejie, meimei, didi, gege, dajie, etc.\n" .
                               "- NEVER translate formal titles: wang-ye, dianxia, bixia, zongzhu, zhanglao, etc.\n" .
                               "- Keep ALL Chinese honorifics in their original romanized form when they appear with names\n";
                break;

            case 'korean':
                $instructions .= "- NEVER translate or remove Korean honorifics: -nim, -ssi, -ah, -ya, etc.\n" .
                               "- NEVER translate academic/workplace terms: sunbae, hoobae, seonsaengnim, gyosunim, etc.\n" .
                               "- NEVER translate family terms: oppa, unni, hyung, noona, eonni, abeoji, eomeoni, etc.\n" .
                               "- NEVER translate formal titles: jeonha, mama, daegam, naeuri, yangban, etc.\n" .
                               "- Keep ALL Korean honorifics in their original romanized form when they appear with names\n";
                break;

            default:
                // For auto or mixed content, include all language instructions
                $instructions .= "- NEVER translate Japanese honorifics: -kun, -chan, -sama, -san, -senpai, -sensei, etc.\n" .
                               "- NEVER translate Chinese honorifics: -ge, -jie, shifu, shizun, gongzi, xiaojie, etc.\n" .
                               "- NEVER translate Korean honorifics: -nim, -ssi, -ah, -ya, sunbae, hoobae, etc.\n" .
                               "- NEVER translate family terms from any language: onii-chan, dage, oppa, etc.\n" .
                               "- Convert Japanese script honorifics to romanized form (おじさん → ojisan)\n" .
                               "- Keep ALL honorifics in their original romanized form when they appear with names\n";
                break;
        }

        $instructions .= "- Preserve the exact spelling and capitalization\n" .
                        "- These terms are cultural elements that must remain unchanged\n";

        return $instructions;
    }
    
    /**
     * Validate that honorifics are preserved in translation
     */
    public function validateHonorificPreservation(string $originalText, string $translatedText, string $sourceLanguage = 'auto'): array {
        $issues = [];

        // Check romanized honorifics based on source language
        $languageSpecificHonorifics = $this->getHonorificsForLanguage($sourceLanguage);

        foreach ($languageSpecificHonorifics as $honorific) {
            $pattern = '/\b' . preg_quote($honorific, '/') . '\b/ui';

            $originalCount = preg_match_all($pattern, $originalText);
            $translatedCount = preg_match_all($pattern, $translatedText);

            if ($originalCount > 0 && $translatedCount < $originalCount) {
                $issues[] = "Honorific '{$honorific}' appears {$originalCount} times in original but only {$translatedCount} times in translation";
            }
        }

        // Check Japanese script honorifics (should be converted to romanized in translation)
        foreach (self::JAPANESE_SCRIPT_HONORIFICS as $japaneseScript => $romanized) {
            $originalPattern = '/' . preg_quote($japaneseScript, '/') . '/u';
            $romanizedPattern = '/\b' . preg_quote($romanized, '/') . '\b/ui';

            $originalCount = preg_match_all($originalPattern, $originalText);
            $translatedCount = preg_match_all($romanizedPattern, $translatedText);

            if ($originalCount > 0 && $translatedCount < $originalCount) {
                $issues[] = "Japanese script honorific '{$japaneseScript}' appears {$originalCount} times in original but romanized form '{$romanized}' only appears {$translatedCount} times in translation";
            }
        }

        return [
            'is_valid' => empty($issues),
            'issues' => $issues
        ];
    }

    /**
     * Detect Japanese script honorifics in text for analysis
     */
    public function detectJapaneseScriptHonorifics(string $text): array {
        $detected = [];

        foreach (self::JAPANESE_SCRIPT_HONORIFICS as $japaneseScript => $romanized) {
            $pattern = '/' . preg_quote($japaneseScript, '/') . '/u';

            if (preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $match) {
                    $detected[] = [
                        'original' => $match[0],
                        'romanized' => $romanized,
                        'position' => $match[1],
                        'type' => 'japanese_script'
                    ];
                }
            }
        }

        return $detected;
    }

    /**
     * Get comprehensive honorific statistics for debugging
     */
    public function getHonorificStatistics(string $text): array {
        $stats = [
            'romanized_honorifics' => [],
            'japanese_script_honorifics' => [],
            'total_count' => 0
        ];

        // Count romanized honorifics
        $allRomanizedHonorifics = array_merge(
            self::JAPANESE_HONORIFICS,
            self::CHINESE_HONORIFICS,
            self::KOREAN_HONORIFICS
        );
        foreach ($allRomanizedHonorifics as $honorific) {
            $pattern = '/\b' . preg_quote($honorific, '/') . '\b/ui';
            $count = preg_match_all($pattern, $text);
            if ($count > 0) {
                $stats['romanized_honorifics'][$honorific] = $count;
                $stats['total_count'] += $count;
            }
        }

        // Count Japanese script honorifics
        foreach (self::JAPANESE_SCRIPT_HONORIFICS as $japaneseScript => $romanized) {
            $pattern = '/' . preg_quote($japaneseScript, '/') . '/u';
            $count = preg_match_all($pattern, $text);
            if ($count > 0) {
                $stats['japanese_script_honorifics'][$japaneseScript] = [
                    'count' => $count,
                    'romanized' => $romanized
                ];
                $stats['total_count'] += $count;
            }
        }

        return $stats;
    }
}
