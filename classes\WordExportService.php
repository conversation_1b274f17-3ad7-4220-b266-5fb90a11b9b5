<?php
/**
 * Word Export Service
 * Handles generation of Word documents (.docx) for translated chapters
 */

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;
use PhpOffice\PhpWord\Style\Font;
use PhpOffice\PhpWord\Style\Paragraph;

class WordExportService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Export a chapter to Word document
     */
    public function exportChapter(int $novelId, int $chapterNumber): array {
        try {
            // Get novel information
            $novel = $this->db->fetchOne(
                "SELECT * FROM novels WHERE id = ?",
                [$novelId]
            );

            if (!$novel) {
                throw new Exception("Novel not found");
            }

            // Get chapter information
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );

            if (!$chapter) {
                throw new Exception("Chapter not found");
            }

            if (!$chapter['translated_content']) {
                throw new Exception("Chapter has no translated content to export");
            }

            // Debug logging for title information
            error_log("WordExport Debug - Novel: " . json_encode([
                'original_title' => $novel['original_title'],
                'translated_title' => $novel['translated_title']
            ]));
            error_log("WordExport Debug - Chapter: " . json_encode([
                'chapter_number' => $chapter['chapter_number'],
                'original_title' => $chapter['original_title'],
                'translated_title' => $chapter['translated_title']
            ]));

            // Generate the Word document
            $filename = $this->generateWordDocument($novel, $chapter);

            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filename // Full path for download
            ];

        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate Word document using PHPWord
     */
    private function generateWordDocument(array $novel, array $chapter): string {
        try {
            // Create new PHPWord instance
            $phpWord = new PhpWord();

            // Set document properties with proper encoding
            $properties = $phpWord->getDocInfo();
            $properties->setCreator('Novel Translator');
            $properties->setCompany('Novel Translation App');
            $properties->setTitle($this->getDocumentTitle($novel, $chapter));
            $properties->setDescription('Translated chapter from ' . ($novel['translated_title'] ?: $novel['original_title']));

            // Add section with basic setup for maximum compatibility
            $section = $phpWord->addSection();

            // Define styles
            $this->defineStyles($phpWord);

            // Add content to document
            $this->addDocumentContent($section, $novel, $chapter);

            // Generate filename
            $filename = $this->generateFilename($novel, $chapter);
            $filepath = APP_ROOT . '/temp/' . $filename;

            // Ensure temp directory exists
            $tempDir = dirname($filepath);
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }

            // Save document with error handling
            $writer = IOFactory::createWriter($phpWord, 'Word2007');
            $writer->save($filepath);

            // Verify file was created and has content
            if (!file_exists($filepath) || filesize($filepath) === 0) {
                throw new Exception("Failed to create Word document or file is empty");
            }

            return $filepath;

        } catch (Exception $e) {
            throw new Exception("Error generating Word document: " . $e->getMessage());
        }
    }
    
    /**
     * Define document styles
     */
    private function defineStyles(PhpWord $phpWord): void {
        // Title style - simplified for better compatibility
        $phpWord->addTitleStyle(1, [
            'name' => 'Arial',
            'size' => 18,
            'bold' => true
        ]);

        // Subtitle style
        $phpWord->addTitleStyle(2, [
            'name' => 'Arial',
            'size' => 14,
            'bold' => true
        ]);

        // Chapter title style
        $phpWord->addTitleStyle(3, [
            'name' => 'Arial',
            'size' => 16,
            'bold' => true
        ]);

        // Normal font style - simplified
        $phpWord->addFontStyle('normalFont', [
            'name' => 'Times New Roman',
            'size' => 12
        ]);

        // Normal paragraph style - simplified
        $phpWord->addParagraphStyle('normalPara', [
            'spaceAfter' => 120
        ]);
    }
    
    /**
     * Add content to the document
     */
    private function addDocumentContent($section, array $novel, array $chapter): void {
        // Add novel title (original + translated format)
        $novelTitleLine = '';
        if ($novel['translated_title'] && $novel['original_title']) {
            // Both titles available - show translated first, then original in parentheses
            $cleanTranslatedTitle = $this->cleanTextForWord($novel['translated_title']);
            $cleanOriginalTitle = $this->cleanTextForWord($novel['original_title']);
            if ($cleanTranslatedTitle !== $cleanOriginalTitle) {
                $novelTitleLine = $cleanTranslatedTitle . ' (' . $cleanOriginalTitle . ')';
            } else {
                $novelTitleLine = $cleanTranslatedTitle;
            }
        } elseif ($novel['translated_title']) {
            $novelTitleLine = $this->cleanTextForWord($novel['translated_title']);
        } else {
            $novelTitleLine = $this->cleanTextForWord($novel['original_title']);
        }

        $section->addTitle($novelTitleLine, 1);

        // Add chapter number and title (original + translated format)
        $chapterTitleLine = 'Chapter ' . $chapter['chapter_number'];

        // Add chapter titles if available
        if (!empty($chapter['translated_title']) && !empty($chapter['original_title'])) {
            // Both titles available - show translated first, then original in parentheses
            $cleanTranslatedChapterTitle = $this->cleanTextForWord($chapter['translated_title']);
            $cleanOriginalChapterTitle = $this->cleanTextForWord($chapter['original_title']);
            if ($cleanTranslatedChapterTitle !== $cleanOriginalChapterTitle) {
                $chapterTitleLine .= ': ' . $cleanTranslatedChapterTitle . ' (' . $cleanOriginalChapterTitle . ')';
            } else {
                $chapterTitleLine .= ': ' . $cleanTranslatedChapterTitle;
            }
        } elseif (!empty($chapter['translated_title'])) {
            $cleanTranslatedChapterTitle = $this->cleanTextForWord($chapter['translated_title']);
            $chapterTitleLine .= ': ' . $cleanTranslatedChapterTitle;
        } elseif (!empty($chapter['original_title'])) {
            $cleanOriginalChapterTitle = $this->cleanTextForWord($chapter['original_title']);
            $chapterTitleLine .= ': ' . $cleanOriginalChapterTitle;
        }
        // If no titles are available, just use "Chapter X" which is already set

        $section->addTitle($chapterTitleLine, 3);

        // Add some spacing before content
        $section->addTextBreak(2);

        // Add translated content
        $this->addFormattedContent($section, $chapter['translated_content']);
    }
    
    /**
     * Add formatted content with proper paragraph handling
     */
    private function addFormattedContent($section, string $content): void {
        // Split content into paragraphs while preserving structure
        $paragraphs = $this->splitContentIntoParagraphs($content);

        foreach ($paragraphs as $paragraph) {
            $trimmed = trim($paragraph);
            if (!empty($trimmed)) {
                // Skip chunk boundary markers
                if (strpos($trimmed, '<!-- CHUNK_BOUNDARY:') === 0) {
                    continue;
                }

                // Clean and prepare text for Word with minimal processing
                $cleanText = $this->cleanTextForWord($trimmed);

                if (!empty($cleanText)) {
                    // Enhanced line break handling for better formatting
                    $this->addParagraphWithLineBreaks($section, $cleanText);
                }
            }
        }
    }

    /**
     * Add paragraph with proper line break handling
     */
    private function addParagraphWithLineBreaks($section, string $text): void {
        // Split on single newlines to preserve line structure
        $lines = explode("\n", $text);

        if (count($lines) === 1) {
            // Single line - add as simple paragraph
            $section->addText($text, 'normalFont', 'normalPara');
        } else {
            // Multiple lines - preserve line breaks within paragraph
            $textRun = $section->addTextRun('normalPara');

            foreach ($lines as $lineIndex => $line) {
                $line = trim($line);

                if ($lineIndex > 0 && !empty($line)) {
                    $textRun->addTextBreak(); // Add line break before non-empty lines
                }

                if (!empty($line)) {
                    $textRun->addText($line, 'normalFont');
                }
            }
        }
    }

    /**
     * Clean text for Word document compatibility
     */
    private function cleanTextForWord(string $text): string {
        // Ensure UTF-8 encoding
        if (!mb_check_encoding($text, 'UTF-8')) {
            $text = mb_convert_encoding($text, 'UTF-8', 'auto');
        }

        // Normalize line endings but preserve structure
        $text = str_replace(["\r\n", "\r"], "\n", $text);

        // Decode HTML entities first to restore proper characters
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove only truly problematic control characters, preserve formatting
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Very conservative XML escaping - only escape characters that break XML
        // Preserve quotation marks, apostrophes, and other formatting characters
        $text = str_replace(['&', '<', '>'], ['&amp;', '&lt;', '&gt;'], $text);

        // Clean up excessive whitespace but preserve intentional spacing
        $text = preg_replace('/[ \t]{3,}/', '  ', $text); // Max 2 spaces
        $text = preg_replace('/\n{4,}/', "\n\n\n", $text); // Max 3 consecutive newlines

        // Do NOT trim whitespace aggressively - preserve paragraph structure
        return $text;
    }
    
    /**
     * Split content into paragraphs while preserving structure
     */
    private function splitContentIntoParagraphs(string $content): array {
        // Remove arbitrary content length limits - export all content
        // Only apply basic safety checks for extremely large content (>500KB)
        if (strlen($content) > 500000) {
            error_log("WordExport: Very large content detected (" . strlen($content) . " bytes), proceeding with full export");
        }

        // Split on double newlines (paragraph breaks) while preserving structure
        $paragraphs = preg_split('/\n\s*\n/', $content);

        // Clean up paragraphs but do NOT truncate content
        return array_map(function($paragraph) {
            $paragraph = trim($paragraph);
            // Remove arbitrary paragraph length limits - export full content
            return $paragraph;
        }, $paragraphs);
    }
    
    /**
     * Generate document title
     */
    private function getDocumentTitle(array $novel, array $chapter): string {
        $title = $novel['translated_title'] ?: $novel['original_title'];
        return $title . ' - Chapter ' . $chapter['chapter_number'];
    }
    
    /**
     * Generate filename for the document
     */
    private function generateFilename(array $novel, array $chapter): string {
        // Use translated title if available, otherwise original title
        $novelTitle = $novel['translated_title'] ?: $novel['original_title'];

        // Clean title for filename (remove invalid characters)
        $cleanTitle = preg_replace('/[^\w\s\-_]/', '', $novelTitle);
        $cleanTitle = preg_replace('/\s+/', '_', $cleanTitle);
        $cleanTitle = trim($cleanTitle, '_');

        // Limit length to prevent filesystem issues
        if (strlen($cleanTitle) > 50) {
            $cleanTitle = substr($cleanTitle, 0, 50);
        }

        // Format: [NovelTitle]_Chapter[Number].docx
        return '[' . $cleanTitle . ']_Chapter' . $chapter['chapter_number'] . '.docx';
    }
    
    /**
     * Clean up temporary files
     */
    public function cleanupTempFiles(): void {
        $tempDir = APP_ROOT . '/temp/';
        if (is_dir($tempDir)) {
            $files = glob($tempDir . '*.docx');
            $cutoff = time() - (24 * 60 * 60); // 24 hours ago
            
            foreach ($files as $file) {
                if (filemtime($file) < $cutoff) {
                    unlink($file);
                }
            }
        }
    }
}
