<?php
/**
 * Test the actual API endpoint with a real HTTP request
 * This simulates exactly what the browser does
 */

echo "=== Real API Call Test ===\n";

// Test data - use a chapter that doesn't have content yet
$testData = [
    'novel_id' => 1,
    'chapter_number' => 155  // Try a higher chapter number that might not have content
];

$jsonData = json_encode($testData);
echo "Testing with data: " . $jsonData . "\n\n";

// Make a real HTTP request to the API
$url = 'http://localhost/wc/api/chapters.php';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($jsonData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 60);

echo "Making HTTP POST request to: $url\n";
echo "Request headers: Content-Type: application/json\n";
echo "Request body: $jsonData\n\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);

if (curl_error($ch)) {
    echo "❌ cURL Error: " . curl_error($ch) . "\n";
    curl_close($ch);
    exit;
}

curl_close($ch);

// Split headers and body
$headers = substr($response, 0, $headerSize);
$body = substr($response, $headerSize);

echo "=== Response Analysis ===\n";
echo "HTTP Status Code: $httpCode\n";
echo "Response Headers:\n$headers\n";
echo "Response Body Length: " . strlen($body) . " characters\n";
echo "Response Body Preview (first 500 chars):\n";
echo substr($body, 0, 500) . "\n";

if (strlen($body) > 500) {
    echo "...\n";
    echo "Response Body End (last 200 chars):\n";
    echo substr($body, -200) . "\n";
}

echo "\n=== JSON Validation ===\n";

// Check if response is valid JSON
$decoded = json_decode($body, true);
if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
    echo "❌ JSON Parsing Failed: " . json_last_error_msg() . "\n";
    echo "JSON Error Code: " . json_last_error() . "\n";
    
    // Try to identify the issue
    echo "\n=== Debugging JSON Issues ===\n";
    
    // Check for HTML content (common when PHP errors occur)
    if (strpos($body, '<html>') !== false || strpos($body, '<!DOCTYPE') !== false) {
        echo "❌ Response contains HTML - likely a PHP error page\n";
        
        // Extract any error messages
        if (preg_match('/<title>(.*?)<\/title>/i', $body, $matches)) {
            echo "Page title: " . $matches[1] . "\n";
        }
        
        if (preg_match('/<b>Fatal error<\/b>:(.*?)<br/i', $body, $matches)) {
            echo "Fatal error: " . strip_tags($matches[1]) . "\n";
        }
        
        if (preg_match('/<b>Warning<\/b>:(.*?)<br/i', $body, $matches)) {
            echo "Warning: " . strip_tags($matches[1]) . "\n";
        }
    }
    
    // Check for common JSON issues
    $trimmed = trim($body);
    if (empty($trimmed)) {
        echo "❌ Response body is empty\n";
    } elseif ($trimmed[0] !== '{' && $trimmed[0] !== '[') {
        echo "❌ Response doesn't start with JSON delimiter\n";
        echo "First character: '" . $trimmed[0] . "' (ASCII: " . ord($trimmed[0]) . ")\n";
        
        // Show first few characters
        echo "First 50 characters: " . substr($trimmed, 0, 50) . "\n";
    }
    
    // Check for null bytes or other problematic characters
    if (strpos($body, "\0") !== false) {
        echo "❌ Response contains null bytes\n";
    }
    
    // Check encoding
    if (!mb_check_encoding($body, 'UTF-8')) {
        echo "❌ Response is not valid UTF-8\n";
    }
    
} else {
    echo "✅ JSON parsing successful\n";
    echo "Response structure:\n";
    print_r($decoded);
    
    // Validate expected structure
    if (isset($decoded['success'])) {
        echo "\n✅ Response has 'success' field: " . ($decoded['success'] ? 'true' : 'false') . "\n";
        
        if ($decoded['success']) {
            if (isset($decoded['data'])) {
                echo "✅ Response has 'data' field\n";
                
                if (isset($decoded['data']['message'])) {
                    echo "Message: " . $decoded['data']['message'] . "\n";
                }
            } else {
                echo "⚠️  Success response missing 'data' field\n";
            }
        } else {
            if (isset($decoded['error'])) {
                echo "❌ Error: " . $decoded['error'] . "\n";
            } else {
                echo "⚠️  Error response missing 'error' field\n";
            }
        }
    } else {
        echo "⚠️  Response missing 'success' field\n";
    }
}

echo "\n=== Test Complete ===\n";

// Test with a different chapter if the first one failed
if ($httpCode !== 200 || ($decoded === null && json_last_error() !== JSON_ERROR_NONE)) {
    echo "\n=== Trying Alternative Test ===\n";
    
    $altTestData = [
        'novel_id' => 1,
        'chapter_number' => 3  // Use a chapter we know exists
    ];
    
    $altJsonData = json_encode($altTestData);
    echo "Testing with alternative data: " . $altJsonData . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, $altJsonData);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen($altJsonData)
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 60);
    
    $altResponse = curl_exec($ch);
    $altHttpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "Alternative test HTTP code: $altHttpCode\n";
    echo "Alternative response length: " . strlen($altResponse) . " characters\n";
    
    $altDecoded = json_decode($altResponse, true);
    if ($altDecoded === null && json_last_error() !== JSON_ERROR_NONE) {
        echo "❌ Alternative test also failed JSON parsing\n";
    } else {
        echo "✅ Alternative test JSON parsing successful\n";
        if (isset($altDecoded['success'])) {
            echo "Success: " . ($altDecoded['success'] ? 'true' : 'false') . "\n";
        }
    }
}
?>
