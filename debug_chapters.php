<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$url = 'https://69shuba.cx/book/44425.htm';

try {
    echo "Testing chapter list extraction from: $url\n";
    
    // Test basic connectivity first
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);

    $html = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);

    echo "HTTP Code: $httpCode\n";
    echo "HTML length: " . strlen($html) . "\n";
    echo "HTML encoding: " . mb_detect_encoding($html) . "\n";

    // Check if we can access the site
    if (strpos($html, '69shuba') !== false || strpos($html, '69书吧') !== false) {
        echo "✓ Successfully accessed 69shuba site\n";
    } else {
        echo "✗ May not have accessed the correct site\n";
        echo "First 500 chars of HTML:\n";
        echo substr($html, 0, 500) . "\n";
    }
    
    // Try to get chapters
    $chapters = $crawler->getChapterList($url);
    echo "Chapters returned: " . (is_array($chapters) ? count($chapters) : 'not an array') . "\n";
    
    if (is_array($chapters) && !empty($chapters)) {
        echo "First 3 chapters:\n";
        for ($i = 0; $i < min(3, count($chapters)); $i++) {
            echo "Chapter " . ($i + 1) . ":\n";
            print_r($chapters[$i]);
        }
    } else {
        echo "No chapters found or error occurred\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}
?>
