<?php
/**
 * Name Dictionary Management Page
 * Comprehensive interface for managing name types and translations
 */

require_once 'config/config.php';
require_once 'includes/header.php';

// Get novel ID from URL parameter
$novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;

if (!$novelId) {
    header('Location: index.php');
    exit;
}

// Get novel information
$db = Database::getInstance();
$novel = $db->fetchOne("SELECT * FROM novels WHERE id = ?", [$novelId]);

if (!$novel) {
    header('Location: index.php');
    exit;
}

renderHeader('Name Dictionary Management - ' . htmlspecialchars($novel['original_title']));
?>

<div class="container-fluid mt-4">
    <div class="row">
        <div class="col-12">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <div>
                    <h2><i class="fas fa-book me-2"></i>Name Dictionary Management</h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb">
                            <li class="breadcrumb-item"><a href="index.php">Home</a></li>
                            <li class="breadcrumb-item"><a href="index.php">Novels</a></li>
                            <li class="breadcrumb-item active"><?= htmlspecialchars($novel['original_title']) ?></li>
                            <li class="breadcrumb-item active">Name Dictionary</li>
                        </ol>
                    </nav>
                </div>
                <div>
                    <button class="btn btn-outline-secondary" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back
                    </button>
                </div>
            </div>

            <!-- Novel Info Card -->
            <div class="card mb-4">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-8">
                            <h5 class="card-title mb-1"><?= htmlspecialchars($novel['translated_title'] ?: $novel['original_title']) ?></h5>
                            <?php if ($novel['translated_title'] && $novel['original_title'] !== $novel['translated_title']): ?>
                                <p class="text-muted mb-0"><small>Original: <?= htmlspecialchars($novel['original_title']) ?></small></p>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-4 text-end">
                            <span class="badge bg-primary" id="total-names-badge">Loading...</span>
                            <span class="badge bg-info" id="selected-names-badge" style="display: none;">0 selected</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Add New Name Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-plus me-2"></i>Add New Names</h6>
                </div>
                <div class="card-body">
                    <!-- Tab Navigation -->
                    <ul class="nav nav-tabs" id="add-name-tabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="manual-tab" data-bs-toggle="tab"
                                    data-bs-target="#manual-form" type="button" role="tab">
                                <i class="fas fa-edit me-1"></i>Manual Entry
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="excel-tab" data-bs-toggle="tab"
                                    data-bs-target="#excel-import" type="button" role="tab">
                                <i class="fas fa-file-excel me-1"></i>Excel Import
                            </button>
                        </li>
                    </ul>

                    <!-- Tab Content -->
                    <div class="tab-content mt-3" id="add-name-tab-content">
                        <!-- Manual Entry Tab -->
                        <div class="tab-pane fade show active" id="manual-form" role="tabpanel">
                            <form id="add-name-form-element">
                                <div class="row g-3">
                                    <div class="col-md-3">
                                        <label for="new-original-name" class="form-label">Original Name <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" id="new-original-name"
                                               placeholder="Enter original name" required>
                                        <div class="form-text">The name in its original language</div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="new-romanization" class="form-label">Romanization</label>
                                        <input type="text" class="form-control" id="new-romanization"
                                               placeholder="Enter romanization">
                                        <div class="form-text">Romanized version (optional)</div>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="new-translation" class="form-label">Translation</label>
                                        <input type="text" class="form-control" id="new-translation"
                                               placeholder="Enter translation">
                                        <div class="form-text">English translation (optional)</div>
                                    </div>
                                    <div class="col-md-2">
                                        <label for="new-name-type" class="form-label">Type <span class="text-danger">*</span></label>
                                        <select class="form-select" id="new-name-type" required>
                                            <option value="character">Character</option>
                                            <option value="location">Location</option>
                                            <option value="organization">Organization</option>
                                            <option value="country">Country</option>
                                            <option value="skill">Skill</option>
                                            <option value="monster">Monster</option>
                                            <option value="item">Item</option>
                                            <option value="class/profession">Class/Profession</option>
                                            <option value="other">Other</option>
                                        </select>
                                    </div>
                                    <div class="col-md-1 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary w-100">
                                            <i class="fas fa-plus me-1"></i>
                                            Add
                                        </button>
                                    </div>
                                </div>
                            </form>
                        </div>

                        <!-- Excel Import Tab -->
                        <div class="tab-pane fade" id="excel-import" role="tabpanel">
                            <div class="row g-3">
                                <div class="col-md-6">
                                    <label for="excel-file" class="form-label">Excel File <span class="text-danger">*</span></label>
                                    <input type="file" class="form-control" id="excel-file"
                                           accept=".xlsx,.xls" required>
                                    <div class="form-text">Upload .xlsx or .xls file with name data</div>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-outline-secondary w-100"
                                            onclick="nameDictionary.downloadTemplate()">
                                        <i class="fas fa-download me-1"></i>
                                        Download Template
                                    </button>
                                </div>
                                <div class="col-md-3 d-flex align-items-end">
                                    <button type="button" class="btn btn-success w-100"
                                            onclick="nameDictionary.importExcel()">
                                        <i class="fas fa-upload me-1"></i>
                                        Import Excel
                                    </button>
                                </div>
                            </div>

                            <!-- Import Progress -->
                            <div id="import-progress" class="mt-3" style="display: none;">
                                <div class="progress">
                                    <div class="progress-bar progress-bar-striped progress-bar-animated"
                                         role="progressbar" style="width: 0%"></div>
                                </div>
                                <small class="text-muted mt-1 d-block" id="import-status">Processing...</small>
                            </div>
                        </div>
                    </div>
            </div>

            <!-- Controls Card -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-filter me-2"></i>Filters & Controls</h6>
                </div>
                <div class="card-body">
                    <div class="row g-3">
                        <!-- Search -->
                        <div class="col-md-4">
                            <label for="search-names" class="form-label">Search Names</label>
                            <div class="input-group">
                                <input type="text" class="form-control" id="search-names" 
                                       placeholder="Search by original name, romanization, or translation...">
                                <button class="btn btn-outline-secondary" type="button" onclick="nameDictionary.clearSearch()">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>

                        <!-- Type Filter -->
                        <div class="col-md-3">
                            <label for="filter-type" class="form-label">Filter by Type</label>
                            <select class="form-select" id="filter-type">
                                <option value="">All Types</option>
                                <option value="character">Character</option>
                                <option value="location">Location</option>
                                <option value="organization">Organization</option>
                                <option value="country">Country</option>
                                <option value="skill">Skill</option>
                                <option value="monster">Monster</option>
                                <option value="other">Other</option>
                            </select>
                        </div>

                        <!-- Status Filter -->
                        <div class="col-md-3">
                            <label for="filter-status" class="form-label">Filter by Status</label>
                            <select class="form-select" id="filter-status">
                                <option value="">All Entries</option>
                                <option value="translated">Has Translation</option>
                                <option value="romanized">Has Romanization</option>
                                <option value="untranslated">No Translation</option>
                                <option value="unromanized">No Romanization</option>
                            </select>
                        </div>

                        <!-- Items per page -->
                        <div class="col-md-2">
                            <label for="items-per-page" class="form-label">Items per Page</label>
                            <select class="form-select" id="items-per-page">
                                <option value="25">25</option>
                                <option value="50" selected>50</option>
                                <option value="100">100</option>
                                <option value="all">All</option>
                            </select>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="row mt-3" id="bulk-actions" style="display: none;">
                        <div class="col-12">
                            <div class="border-top pt-3">
                                <h6 class="mb-2">Bulk Actions</h6>
                                <div class="d-flex gap-2 flex-wrap">
                                    <div class="input-group" style="width: auto;">
                                        <span class="input-group-text">Set Type:</span>
                                        <select class="form-select" id="bulk-type-select" style="width: 150px;">
                                            <option value="">Select Type</option>
                                            <option value="character">Character</option>
                                            <option value="location">Location</option>
                                            <option value="organization">Organization</option>
                                            <option value="country">Country</option>
                                            <option value="skill">Skill</option>
                                            <option value="monster">Monster</option>
                                            <option value="other">Other</option>
                                        </select>
                                        <button class="btn btn-primary" onclick="nameDictionary.bulkUpdateType()">
                                            <i class="fas fa-edit me-1"></i>Apply
                                        </button>
                                    </div>
                                    <button class="btn btn-warning" onclick="nameDictionary.bulkGenerateRomanization()">
                                        <i class="fas fa-magic me-1"></i>Generate Romanization
                                    </button>
                                    <button class="btn btn-danger" onclick="nameDictionary.bulkDelete()">
                                        <i class="fas fa-trash me-1"></i>Delete Selected
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Names Table -->
            <div class="card">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h6 class="mb-0"><i class="fas fa-list me-2"></i>Name Dictionary Entries</h6>
                    <div class="d-flex gap-2">
                        <button class="btn btn-sm btn-outline-primary" onclick="nameDictionary.toggleSelectAll()">
                            <i class="fas fa-check-square me-1"></i>
                            <span id="select-all-text">Select All</span>
                        </button>
                        <button class="btn btn-sm btn-outline-success" onclick="nameDictionary.exportData()">
                            <i class="fas fa-download me-1"></i>
                            Export
                        </button>
                    </div>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0" id="names-table">
                            <thead class="table-light">
                                <tr>
                                    <th width="40">
                                        <input type="checkbox" class="form-check-input" id="select-all-checkbox">
                                    </th>
                                    <th>Original Name</th>
                                    <th>Type</th>
                                    <th>Romanization</th>
                                    <th>Translation</th>
                                    <th width="80">Frequency</th>
                                    <th width="80">First Ch.</th>
                                    <th width="100">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="names-table-body">
                                <tr>
                                    <td colspan="8" class="text-center py-4">
                                        <div class="spinner-border text-primary" role="status">
                                            <span class="visually-hidden">Loading...</span>
                                        </div>
                                        <p class="mt-2 mb-0">Loading name dictionary...</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
                <div class="card-footer" id="pagination-container">
                    <!-- Pagination will be inserted here -->
                </div>
            </div>
        </div>
    </div>
</div>



<script>
// Pass novel ID to JavaScript
window.novelId = <?= $novelId ?>;
</script>

<?php renderFooter(['assets/js/name-dictionary.js']); ?>
