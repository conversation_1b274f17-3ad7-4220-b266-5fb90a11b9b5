<?php
/**
 * API endpoint for managing user preferences
 */

require_once '../config/config.php';

header('Content-Type: application/json');

try {
    $db = Database::getInstance();
    $method = $_SERVER['REQUEST_METHOD'];
    
    switch ($method) {
        case 'POST':
            // Update or create a preference
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!isset($input['preference_key']) || !isset($input['preference_value'])) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Missing preference_key or preference_value']);
                exit;
            }
            
            $preferenceKey = $input['preference_key'];
            $preferenceValue = $input['preference_value'];
            
            // Validate preference key (legacy WordPress settings removed - now using profile system)
            $allowedKeys = [
                'show_chunk_markers',
                'furigana_display_mode',
                'theme'
            ];
            if (!in_array($preferenceKey, $allowedKeys)) {
                http_response_code(400);
                echo json_encode(['success' => false, 'error' => 'Invalid preference key']);
                exit;
            }
            
            // Check if preference exists
            $existing = $db->fetchOne(
                "SELECT id FROM user_preferences WHERE preference_key = ?",
                [$preferenceKey]
            );
            
            if ($existing) {
                // Update existing preference
                $db->update(
                    'user_preferences',
                    ['preference_value' => $preferenceValue],
                    'preference_key = ?',
                    [$preferenceKey]
                );
            } else {
                // Create new preference
                $db->insert('user_preferences', [
                    'preference_key' => $preferenceKey,
                    'preference_value' => $preferenceValue
                ]);
            }
            
            echo json_encode([
                'success' => true,
                'message' => 'Preference updated successfully',
                'preference_key' => $preferenceKey,
                'preference_value' => $preferenceValue
            ]);
            break;
            
        case 'GET':
            // Get preferences
            $preferenceKey = $_GET['key'] ?? null;
            
            if ($preferenceKey) {
                // Get specific preference
                $preference = $db->fetchOne(
                    "SELECT * FROM user_preferences WHERE preference_key = ?",
                    [$preferenceKey]
                );
                
                if ($preference) {
                    echo json_encode([
                        'success' => true,
                        'preference' => $preference
                    ]);
                } else {
                    echo json_encode([
                        'success' => false,
                        'error' => 'Preference not found'
                    ]);
                }
            } else {
                // Get all preferences
                $preferences = $db->fetchAll("SELECT * FROM user_preferences");
                
                // Convert to key-value format
                $preferencesMap = [];
                foreach ($preferences as $pref) {
                    $preferencesMap[$pref['preference_key']] = $pref['preference_value'];
                }
                
                echo json_encode([
                    'success' => true,
                    'preferences' => $preferencesMap
                ]);
            }
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['success' => false, 'error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode([
        'success' => false,
        'error' => 'Server error: ' . $e->getMessage()
    ]);
}
?>
