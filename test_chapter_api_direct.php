<?php
/**
 * Direct test of the chapter API endpoint to identify JSON issues
 */

// Simulate the exact environment of the API call
$_SERVER['REQUEST_METHOD'] = 'POST';
$_SERVER['CONTENT_TYPE'] = 'application/json';

// Test data
$testData = [
    'novel_id' => 1,
    'chapter_number' => 3
];

// Create a temporary file to simulate php://input
$tempFile = tempnam(sys_get_temp_dir(), 'api_test');
file_put_contents($tempFile, json_encode($testData));

// Override php://input for this test
$GLOBALS['test_input'] = json_encode($testData);

// Capture all output
ob_start();

// Override file_get_contents for php://input
function file_get_contents_override($filename) {
    if ($filename === 'php://input') {
        return $GLOBALS['test_input'];
    }
    return file_get_contents($filename);
}

// Temporarily override the function
if (!function_exists('file_get_contents_original')) {
    function file_get_contents_original($filename) {
        return file_get_contents($filename);
    }
}

echo "=== Direct API Test ===\n";
echo "Testing with data: " . json_encode($testData) . "\n\n";

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

try {
    // Include the API file but capture its output
    ob_start();
    
    // We need to manually simulate the API call since we can't override php://input easily
    require_once 'config/config.php';
    
    $novelManager = new NovelManager();
    
    // Simulate the exact handleSaveChapter function logic
    echo "Simulating handleSaveChapter function...\n";
    
    // Clean output buffer
    while (ob_get_level() > 1) {
        ob_end_clean();
    }
    
    // Enable error reporting for debugging but prevent HTML output
    error_reporting(E_ALL);
    ini_set('display_errors', 0);
    ini_set('log_errors', 1);

    // Log the incoming request for debugging
    error_log("Test API: Save chapter request received");

    $rawInput = json_encode($testData);
    error_log("Test API: Raw input: " . $rawInput);

    $input = json_decode($rawInput, true);

    if (!$input) {
        error_log("Test API: Invalid JSON input - Raw: " . $rawInput);
        echo "❌ Invalid JSON input\n";
        exit;
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        echo "❌ Valid novel_id is required\n";
        exit;
    }

    if (!isset($input['chapter_number']) || !is_numeric($input['chapter_number'])) {
        echo "❌ Valid chapter_number is required\n";
        exit;
    }

    $novelId = (int)$input['novel_id'];
    $chapterNumber = (int)$input['chapter_number'];

    error_log("Test API: Processing save for novel {$novelId}, chapter {$chapterNumber}");

    // Check if this is a manual chapter creation (has content in the request)
    if (isset($input['original_content']) && !empty(trim($input['original_content']))) {
        // This is a manual chapter creation
        error_log("Test API: Manual chapter creation");
        $result = $novelManager->addManualChapter($novelId, $input);
    } else {
        // This is a regular chapter save (crawled content)
        error_log("Test API: Regular chapter save (crawled content)");
        $result = $novelManager->saveChapter($novelId, $chapterNumber);
    }

    // Test JSON encoding before sending response
    $testJson = json_encode($result);
    if ($testJson === false) {
        error_log("Test API: JSON encoding failed for result: " . json_last_error_msg());
        
        // Clean the result data to fix encoding issues
        function cleanDataForJson($data) {
            if (is_array($data)) {
                return array_map('cleanDataForJson', $data);
            } elseif (is_string($data)) {
                // Remove null bytes and fix encoding
                $cleaned = str_replace("\0", '', $data);
                if (!mb_check_encoding($cleaned, 'UTF-8')) {
                    $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'auto');
                }
                // Remove any remaining problematic characters
                $cleaned = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $cleaned);
                return $cleaned;
            }
            return $data;
        }
        
        $cleanedResult = cleanDataForJson($result);
        $testJson = json_encode($cleanedResult);
        
        if ($testJson === false) {
            error_log("Test API: JSON encoding still failed after cleaning: " . json_last_error_msg());
            echo "❌ JSON encoding failed even after cleaning: " . json_last_error_msg() . "\n";
            exit;
        }
        
        $result = $cleanedResult;
        echo "⚠️  Had to clean data for JSON encoding\n";
    }
    
    error_log("Test API: Save result JSON length: " . strlen($testJson));

    if ($result['success']) {
        $response = [
            'success' => true,
            'data' => $result
        ];
    } else {
        $response = [
            'success' => false,
            'error' => $result['error']
        ];
    }
    
    // Final JSON test
    $finalJson = json_encode($response);
    if ($finalJson === false) {
        echo "❌ Final response JSON encoding failed: " . json_last_error_msg() . "\n";
    } else {
        echo "✅ API call simulation successful\n";
        echo "Response JSON length: " . strlen($finalJson) . " characters\n";
        echo "Response preview: " . substr($finalJson, 0, 200) . "...\n";
        
        // Test if this would be valid JSON for the frontend
        $parsed = json_decode($finalJson, true);
        if ($parsed === null) {
            echo "❌ Generated JSON is not parseable\n";
        } else {
            echo "✅ Generated JSON is valid and parseable\n";
        }
    }

} catch (Exception $e) {
    echo "❌ Exception during API simulation: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ Fatal PHP error during API simulation: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Get any unexpected output
$output = ob_get_clean();
echo $output;

// Clean up
unlink($tempFile);

echo "\n=== Test Complete ===\n";
?>
