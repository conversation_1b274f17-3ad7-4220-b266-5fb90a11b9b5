<?php
// Test chapter content extraction with proper URL
$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "Testing chapter URL: $chapterUrl\n";

// Test raw HTML first
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $chapterUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding: gzip, deflate, br',
    'Referer: https://69shuba.cx/book/44425.htm'
]);

$html = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

echo "HTTP Code: $httpCode\n";
echo "HTML Length: " . strlen($html) . "\n";
echo "Detected Encoding: " . mb_detect_encoding($html) . "\n";
echo "Is valid UTF-8: " . (mb_check_encoding($html, 'UTF-8') ? 'Yes' : 'No') . "\n";

if ($httpCode == 200 && !empty($html)) {
    echo "\nFirst 2000 characters of raw HTML:\n";
    echo substr($html, 0, 2000) . "\n";
    
    // Test DOMDocument parsing
    echo "\n--- Testing DOMDocument parsing ---\n";
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    
    // Try different encoding approaches
    echo "Method 1: Standard UTF-8 parsing\n";
    $dom1 = new DOMDocument();
    $dom1->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
    $textContent1 = $dom1->textContent;
    echo "DOM textContent length: " . strlen($textContent1) . "\n";
    echo "DOM textContent encoding: " . mb_detect_encoding($textContent1) . "\n";
    echo "DOM textContent first 500 chars:\n";
    echo substr($textContent1, 0, 500) . "\n";
    
    echo "\nMethod 2: Direct HTML parsing\n";
    $dom2 = new DOMDocument();
    $dom2->loadHTML($html);
    $textContent2 = $dom2->textContent;
    echo "DOM textContent length: " . strlen($textContent2) . "\n";
    echo "DOM textContent encoding: " . mb_detect_encoding($textContent2) . "\n";
    echo "DOM textContent first 500 chars:\n";
    echo substr($textContent2, 0, 500) . "\n";
    
    libxml_clear_errors();
}
?>
