<?php
/**
 * Cache Performance API
 * Monitor and analyze DeepSeek prompt caching performance
 */

require_once __DIR__ . '/../config/config.php';
require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../classes/CacheOptimizationService.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

/**
 * Send JSON response
 */
function sendJsonResponse(array $data, int $statusCode = 200): void {
    http_response_code($statusCode);
    echo json_encode($data, JSON_PRETTY_PRINT);
    exit;
}

try {
    $cacheService = new CacheOptimizationService();
    $method = $_SERVER['REQUEST_METHOD'];

    switch ($method) {
        case 'GET':
            handleGet($cacheService);
            break;
        case 'POST':
            handlePost($cacheService);
            break;
        case 'PUT':
            handlePut($cacheService);
            break;
        default:
            sendJsonResponse(['error' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    error_log("Cache Performance API Error: " . $e->getMessage());
    sendJsonResponse([
        'success' => false,
        'error' => 'Internal server error: ' . $e->getMessage()
    ], 500);
}

/**
 * Handle GET requests - retrieve cache performance data
 */
function handleGet($cacheService) {
    $action = $_GET['action'] ?? 'summary';
    
    switch ($action) {
        case 'summary':
            $days = (int)($_GET['days'] ?? 30);
            $summary = $cacheService->getCachePerformanceSummary($days);
            sendJsonResponse([
                'success' => true,
                'data' => $summary
            ]);
            break;
            
        case 'novel':
            $novelId = (int)($_GET['novel_id'] ?? 0);
            $days = (int)($_GET['days'] ?? 7);
            
            if ($novelId <= 0) {
                sendJsonResponse(['error' => 'Valid novel_id required'], 400);
            }

            $analysis = $cacheService->analyzeCachePerformance($novelId, $days);
            sendJsonResponse([
                'success' => true,
                'data' => $analysis
            ]);
            break;
            
        case 'daily':
            $days = (int)($_GET['days'] ?? 7);
            $dailyStats = getDailyStats($days);
            sendJsonResponse([
                'success' => true,
                'data' => $dailyStats
            ]);
            break;

        case 'config':
            $config = getCacheConfig();
            sendJsonResponse([
                'success' => true,
                'data' => $config
            ]);
            break;

        default:
            sendJsonResponse(['error' => 'Invalid action'], 400);
    }
}

/**
 * Handle POST requests - analyze specific data
 */
function handlePost($cacheService) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    $action = $input['action'] ?? '';

    switch ($action) {
        case 'analyze_novel':
            $novelId = (int)($input['novel_id'] ?? 0);
            $days = (int)($input['days'] ?? 7);

            if ($novelId <= 0) {
                sendJsonResponse(['error' => 'Valid novel_id required'], 400);
            }

            $analysis = $cacheService->analyzeCachePerformance($novelId, $days);
            sendJsonResponse([
                'success' => true,
                'data' => $analysis
            ]);
            break;
            
        case 'bulk_analysis':
            $novelIds = $input['novel_ids'] ?? [];
            $days = (int)($input['days'] ?? 7);
            
            if (empty($novelIds) || !is_array($novelIds)) {
                sendJsonResponse(['error' => 'Valid novel_ids array required'], 400);
            }

            $results = [];
            foreach ($novelIds as $novelId) {
                $results[] = $cacheService->analyzeCachePerformance((int)$novelId, $days);
            }

            sendJsonResponse([
                'success' => true,
                'data' => $results
            ]);
            break;

        default:
            sendJsonResponse(['error' => 'Invalid action'], 400);
    }
}

/**
 * Handle PUT requests - update configuration
 */
function handlePut($cacheService) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        sendJsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    $action = $input['action'] ?? '';

    switch ($action) {
        case 'update_config':
            $key = $input['key'] ?? '';
            $value = $input['value'] ?? '';

            if (empty($key)) {
                sendJsonResponse(['error' => 'Configuration key required'], 400);
            }

            $success = $cacheService->updateConfig($key, $value);

            if ($success) {
                sendJsonResponse([
                    'success' => true,
                    'message' => 'Configuration updated successfully'
                ]);
            } else {
                sendJsonResponse(['error' => 'Failed to update configuration'], 500);
            }
            break;

        default:
            sendJsonResponse(['error' => 'Invalid action'], 400);
    }
}

/**
 * Get daily cache performance statistics
 */
function getDailyStats(int $days): array {
    $db = Database::getInstance();
    
    try {
        $stats = $db->fetchAll(
            "SELECT 
                DATE(created_at) as date,
                COUNT(*) as total_requests,
                AVG(cache_hit_rate) as avg_cache_hit_rate,
                SUM(estimated_cost_savings) as daily_cost_savings,
                SUM(cache_hit_tokens) as total_cache_hits,
                SUM(cache_miss_tokens) as total_cache_misses,
                AVG(translation_time_seconds) as avg_translation_time
             FROM translation_logs 
             WHERE api_used = 'deepseek' 
               AND status = 'success'
               AND created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)
             GROUP BY DATE(created_at)
             ORDER BY date DESC",
            [$days]
        );
        
        // Format the data
        $formattedStats = [];
        foreach ($stats as $stat) {
            $formattedStats[] = [
                'date' => $stat['date'],
                'total_requests' => (int)$stat['total_requests'],
                'avg_cache_hit_rate' => round((float)$stat['avg_cache_hit_rate'], 2),
                'daily_cost_savings' => round((float)$stat['daily_cost_savings'], 6),
                'total_cache_hits' => (int)$stat['total_cache_hits'],
                'total_cache_misses' => (int)$stat['total_cache_misses'],
                'avg_translation_time' => round((float)$stat['avg_translation_time'], 2)
            ];
        }
        
        return $formattedStats;
        
    } catch (Exception $e) {
        error_log("Error getting daily stats: " . $e->getMessage());
        return [];
    }
}

/**
 * Get cache optimization configuration
 */
function getCacheConfig(): array {
    $db = Database::getInstance();
    
    try {
        $config = $db->fetchAll(
            "SELECT config_key, config_value, description, updated_at 
             FROM cache_optimization_config 
             ORDER BY config_key"
        );
        
        $formattedConfig = [];
        foreach ($config as $item) {
            $value = $item['config_value'];
            // Convert string booleans to actual booleans for JSON
            if ($value === 'true') $value = true;
            elseif ($value === 'false') $value = false;
            elseif (is_numeric($value)) $value = (float)$value;
            
            $formattedConfig[] = [
                'key' => $item['config_key'],
                'value' => $value,
                'description' => $item['description'],
                'updated_at' => $item['updated_at']
            ];
        }
        
        return $formattedConfig;
        
    } catch (Exception $e) {
        error_log("Error getting cache config: " . $e->getMessage());
        return [];
    }
}
