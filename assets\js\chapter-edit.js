/**
 * Chapter Edit JavaScript
 * Handles TinyMCE editor integration and chapter editing functionality
 */

class ChapterEditor {
    constructor(novelId, chapterNumber) {
        this.novelId = novelId;
        this.chapterNumber = chapterNumber;
        this.chapter = null;
        this.novel = null;
        this.editor = null;
        this.autoSaveEnabled = true;
        this.autoSaveInterval = null;
        this.lastSaved = null;
        this.isFullscreen = false;
        this.hasUnsavedChanges = false;

        // Initialize the editor
        this.init();
    }

    async init() {
        try {
            // Load chapter data first
            await this.loadChapter();

            // Validate that we have the minimum required data
            if (!this.chapter) {
                throw new Error('Chapter data is required but not available');
            }

            // Check if chapter has any content to edit
            if (!this.chapter.original_content && !this.chapter.translated_content) {
                // We'll still allow the editor to open, but show a message
                console.warn('ChapterEditor: Chapter has no content to edit');
            }

            // Initialize TinyMCE
            await this.initTinyMCE();

            // Setup auto-save
            this.setupAutoSave();

            // Setup keyboard shortcuts
            this.setupKeyboardShortcuts();

            // Setup beforeunload warning
            this.setupBeforeUnload();

            // Show editor
            this.showEditor();

        } catch (error) {
            console.error('Failed to initialize chapter editor:', error);

            // Provide more specific error messages
            let errorMessage = 'Failed to initialize editor';
            if (error.message.includes('Chapter data not found')) {
                errorMessage = 'Chapter not found. Please check the chapter number and try again.';
            } else if (error.message.includes('Novel data not found')) {
                errorMessage = 'Novel information not found. Please check the novel ID and try again.';
            } else if (error.message.includes('Failed to load chapter')) {
                errorMessage = 'Unable to load chapter data. Please check your connection and try again.';
            } else if (error.message) {
                errorMessage = error.message;
            }

            this.showError(errorMessage);
        }
    }

    async loadChapter() {
        try {
            // Use the correct API endpoint for single chapter access
            const response = await utils.makeApiRequest(`api/chapter.php?novel_id=${this.novelId}&chapter=${this.chapterNumber}`);

            if (!response.success) {
                throw new Error(response.error || 'Failed to load chapter');
            }

            // Validate response data structure
            if (!response.data) {
                throw new Error('Invalid response: missing data');
            }

            this.chapter = response.data.chapter;
            this.novel = response.data.novel;

            // Validate that we have the required data
            if (!this.chapter) {
                throw new Error('Chapter data not found in response');
            }

            if (!this.novel) {
                throw new Error('Novel data not found in response');
            }

            // Update UI with chapter info
            this.updateChapterInfo();

        } catch (error) {
            console.error('ChapterEditor: Load chapter error:', error);
            throw error;
        }
    }

    updateChapterInfo() {
        try {
            // Safely update chapter title with null checks
            const chapterTitle = (this.chapter && (this.chapter.translated_title || this.chapter.original_title)) || `Chapter ${this.chapterNumber}`;
            const chapterTitleElement = document.getElementById('chapter-title');
            if (chapterTitleElement) {
                chapterTitleElement.innerHTML = `
                    <i class="fas fa-file-alt text-muted me-2"></i>
                    ${utils.escapeHtml(chapterTitle)}
                `;
            }

            // Safely update novel title with null checks
            const novelTitle = (this.novel && (this.novel.translated_title || this.novel.original_title)) || 'Unknown Novel';
            const novelTitleElement = document.getElementById('novel-title');
            if (novelTitleElement) {
                novelTitleElement.innerHTML = `
                    <i class="fas fa-book text-muted me-2"></i>
                    ${utils.escapeHtml(novelTitle)}
                `;
            }

            // Safely update last saved time
            const lastSavedElement = document.getElementById('last-saved');
            if (lastSavedElement && this.chapter && this.chapter.updated_at) {
                lastSavedElement.textContent = utils.formatDate(this.chapter.updated_at);
            }

            // Safely update original content in modal and panel
            if (this.chapter && this.chapter.original_content) {
                const formattedOriginal = this.formatOriginalText(this.chapter.original_content);

                const originalContentElement = document.getElementById('original-content');
                if (originalContentElement) {
                    originalContentElement.innerHTML = formattedOriginal;
                }

                const modalOriginalContentElement = document.getElementById('modal-original-content');
                if (modalOriginalContentElement) {
                    modalOriginalContentElement.innerHTML = formattedOriginal;
                }
            } else {
                // Handle case where there's no original content
                const noContentMessage = '<p class="text-muted">No original content available</p>';

                const originalContentElement = document.getElementById('original-content');
                if (originalContentElement) {
                    originalContentElement.innerHTML = noContentMessage;
                }

                const modalOriginalContentElement = document.getElementById('modal-original-content');
                if (modalOriginalContentElement) {
                    modalOriginalContentElement.innerHTML = noContentMessage;
                }
            }
        } catch (error) {
            console.error('Error updating chapter info:', error);
            // Don't throw here, just log the error and continue
        }
    }

    formatOriginalText(text) {
        if (!text) return '<p class="text-muted">No original content available</p>';

        // Split into paragraphs and format
        const paragraphs = this.splitTextIntoParagraphs(text);

        return paragraphs
            .map(paragraph => {
                // Handle furigana markup {kanji|furigana}
                const formattedParagraph = this.formatTextWithFurigana(paragraph);
                return `<p>${formattedParagraph}</p>`;
            })
            .join('');
    }

    /**
     * Format translated text for TinyMCE editor
     * Converts plain text with line breaks to proper HTML paragraphs
     */
    formatTranslatedTextForEditor(text) {
        if (!text) return '';

        // Split into paragraphs while preserving formatting
        const paragraphs = this.splitTextIntoParagraphs(text);

        return paragraphs
            .map(paragraph => {
                // Check if this is a chunk boundary marker
                if (paragraph.startsWith('<!-- CHUNK_BOUNDARY:')) {
                    // Skip chunk markers in the editor - they're for display only
                    return '';
                }

                // Regular paragraph formatting
                const trimmed = paragraph.trim();
                if (!trimmed) return '';

                // Handle furigana markup and escape HTML
                const formattedParagraph = this.formatTextWithFurigana(trimmed);
                return `<p>${formattedParagraph}</p>`;
            })
            .filter(p => p) // Remove empty paragraphs
            .join('');
    }

    /**
     * Split text into paragraphs while preserving structure
     * Same logic as chapter-view.js for consistency
     */
    splitTextIntoParagraphs(text) {
        // Split on double newlines (paragraph breaks) but preserve single newlines within paragraphs
        return text.split(/\n\s*\n/)
            .map(paragraph => paragraph.trim())
            .filter(paragraph => paragraph);
    }

    /**
     * Format text with furigana support
     * Same logic as chapter-view.js for consistency
     */
    formatTextWithFurigana(text) {
        if (!text) return '';

        // Convert furigana markup {kanji|furigana} to HTML ruby tags
        const furiganaPattern = /\{([^|]+)\|([^}]+)\}/g;

        return utils.escapeHtml(text).replace(furiganaPattern, (_, kanji, furigana) => {
            return `<ruby>${kanji}<rt>${furigana}</rt></ruby>`;
        });
    }

    async initTinyMCE() {
        return new Promise((resolve, reject) => {
            tinymce.init({
                selector: '#translation-editor',
                height: 600,
                menubar: true,
                plugins: [
                    'advlist', 'autolink', 'lists', 'link', 'image', 'charmap', 'preview',
                    'anchor', 'searchreplace', 'visualblocks', 'code', 'fullscreen',
                    'insertdatetime', 'media', 'table', 'help', 'wordcount', 'autosave'
                ],
                toolbar: 'undo redo | blocks | bold italic underline strikethrough | ' +
                         'alignleft aligncenter alignright alignjustify | ' +
                         'bullist numlist outdent indent | removeformat | ' +
                         'searchreplace | fullscreen | help',
                content_style: `
                    body { 
                        font-family: Georgia, serif; 
                        font-size: 16px; 
                        line-height: 1.6; 
                        color: #333;
                        max-width: none;
                        margin: 1rem;
                    }
                    p { margin-bottom: 1rem; }
                    ruby { ruby-align: center; }
                    rt { font-size: 0.6em; color: #666; }
                `,
                setup: (editor) => {
                    this.editor = editor;
                    
                    editor.on('init', () => {
                        try {
                            // Get raw translated content
                            const rawContent = (this.chapter && this.chapter.translated_content) || '';

                            // Format the content properly for TinyMCE
                            const formattedContent = this.formatTranslatedTextForEditor(rawContent);

                            // Set the properly formatted content
                            editor.setContent(formattedContent);
                            this.updateWordCount();

                            resolve();
                        } catch (error) {
                            console.error('ChapterEditor: Error initializing editor content:', error);
                            // Set empty content as fallback
                            editor.setContent('');
                            resolve();
                        }
                    });

                    editor.on('change keyup', () => {
                        this.hasUnsavedChanges = true;
                        this.updateWordCount();
                    });

                    // Custom keyboard shortcuts
                    editor.addShortcut('ctrl+s', 'Save chapter', () => {
                        this.saveChapter();
                    });

                    editor.addShortcut('ctrl+o', 'Show original text', () => {
                        this.showOriginal();
                    });
                },
                autosave_ask_before_unload: false,
                autosave_interval: '30s',
                autosave_prefix: `chapter-${this.novelId}-${this.chapterNumber}`,
                autosave_restore_when_empty: true,
                paste_data_images: false,
                browser_spellcheck: true,
                contextmenu: 'link image table',
                resize: false,
                branding: false,
                promotion: false
            }).catch(reject);
        });
    }

    updateWordCount() {
        if (this.editor) {
            const content = this.editor.getContent({ format: 'text' });
            const wordCount = content.trim() ? content.trim().split(/\s+/).length : 0;
            document.getElementById('word-count').textContent = wordCount.toLocaleString();
        }
    }

    setupAutoSave() {
        if (this.autoSaveEnabled) {
            this.autoSaveInterval = setInterval(() => {
                if (this.hasUnsavedChanges) {
                    this.autoSave();
                }
            }, 30000); // Auto-save every 30 seconds
        }
    }

    async autoSave() {
        if (!this.editor || !this.hasUnsavedChanges) return;

        try {
            this.showAutoSaveIndicator('Saving...', 'saving');

            // Get HTML content and convert to plain text
            const htmlContent = this.editor.getContent();
            const plainTextContent = this.convertHtmlToPlainText(htmlContent);

            const result = await this.saveTranslation(plainTextContent);

            if (result.success) {
                this.hasUnsavedChanges = false;
                this.lastSaved = new Date();
                document.getElementById('last-saved').textContent = utils.formatDate(this.lastSaved);

                // Update the chapter object with the new content
                if (this.chapter) {
                    this.chapter.translated_content = plainTextContent;
                }

                this.showAutoSaveIndicator('Auto-saved', 'saved');
            } else {
                this.showAutoSaveIndicator('Auto-save failed', 'error');
            }
        } catch (error) {
            console.error('Auto-save error:', error);
            this.showAutoSaveIndicator('Auto-save failed', 'error');
        }
    }

    showAutoSaveIndicator(message, type) {
        // Remove existing indicator
        const existing = document.querySelector('.auto-save-indicator');
        if (existing) {
            existing.remove();
        }

        // Create new indicator
        const indicator = document.createElement('div');
        indicator.className = `auto-save-indicator ${type}`;
        indicator.innerHTML = `
            <span class="status-indicator ${type}"></span>
            ${message}
        `;
        
        document.body.appendChild(indicator);
        
        // Show indicator
        setTimeout(() => indicator.classList.add('show'), 100);
        
        // Hide after 3 seconds (except for saving state)
        if (type !== 'saving') {
            setTimeout(() => {
                indicator.classList.remove('show');
                setTimeout(() => indicator.remove(), 300);
            }, 3000);
        }
    }

    setupKeyboardShortcuts() {
        document.addEventListener('keydown', (e) => {
            // Ctrl+S - Save
            if (e.ctrlKey && e.key === 's') {
                e.preventDefault();
                this.saveChapter();
            }
            
            // F11 - Fullscreen
            if (e.key === 'F11') {
                e.preventDefault();
                this.toggleFullscreen();
            }
            
            // Ctrl+O - Show original
            if (e.ctrlKey && e.key === 'o') {
                e.preventDefault();
                this.showOriginal();
            }
        });
    }

    setupBeforeUnload() {
        window.addEventListener('beforeunload', (e) => {
            if (this.hasUnsavedChanges) {
                e.preventDefault();
                e.returnValue = 'You have unsaved changes. Are you sure you want to leave?';
                return e.returnValue;
            }
        });
    }

    showEditor() {
        document.getElementById('loading-state').style.display = 'none';
        document.getElementById('error-state').style.display = 'none';
        document.getElementById('editor-container').style.display = 'block';
        document.getElementById('editor-container').classList.add('fade-in');
    }

    showError(message) {
        document.getElementById('loading-state').style.display = 'none';
        document.getElementById('editor-container').style.display = 'none';
        document.getElementById('error-message').textContent = message;
        document.getElementById('error-state').style.display = 'block';
    }

    /**
     * Convert TinyMCE HTML content back to plain text format for database storage
     * This ensures consistency with how content is stored and displayed elsewhere
     */
    convertHtmlToPlainText(htmlContent) {
        if (!htmlContent) return '';

        // Create a temporary div to parse HTML
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = htmlContent;

        // Handle different HTML structures
        const textParagraphs = [];

        // First, try to extract from paragraph tags
        const paragraphs = tempDiv.querySelectorAll('p');

        if (paragraphs.length > 0) {
            // Content has proper paragraph structure
            paragraphs.forEach(p => {
                let text = '';

                // Handle ruby tags (furigana) - convert back to {kanji|furigana} format
                const rubyElements = p.querySelectorAll('ruby');
                rubyElements.forEach(ruby => {
                    const kanji = ruby.firstChild ? ruby.firstChild.textContent : '';
                    const rt = ruby.querySelector('rt');
                    const furigana = rt ? rt.textContent : '';
                    if (kanji && furigana) {
                        ruby.outerHTML = `{${kanji}|${furigana}}`;
                    }
                });

                text = p.textContent || p.innerText || '';
                if (text.trim()) {
                    textParagraphs.push(text.trim());
                }
            });
        } else {
            // Fallback: content might be in other formats or plain text
            const allText = tempDiv.textContent || tempDiv.innerText || '';
            if (allText.trim()) {
                // Split by line breaks and create paragraphs
                const lines = allText.split(/\n+/).map(line => line.trim()).filter(line => line);
                textParagraphs.push(...lines);
            }
        }

        // Join paragraphs with double newlines (standard format)
        return textParagraphs.join('\n\n');
    }

    async saveChapter() {
        if (!this.editor) {
            utils.showToast('Editor not ready', 'warning');
            return;
        }

        const saveBtn = document.getElementById('save-btn');
        const originalText = saveBtn.innerHTML;

        try {
            // Update button state
            saveBtn.disabled = true;
            saveBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Saving...';

            // Get HTML content from editor
            const htmlContent = this.editor.getContent();

            // Convert to plain text format for database storage
            const plainTextContent = this.convertHtmlToPlainText(htmlContent);

            const result = await this.saveTranslation(plainTextContent);

            if (result.success) {
                this.hasUnsavedChanges = false;
                this.lastSaved = new Date();
                document.getElementById('last-saved').textContent = utils.formatDate(this.lastSaved);

                // Update the chapter object with the new content
                if (this.chapter) {
                    this.chapter.translated_content = plainTextContent;
                }

                utils.showToast('Chapter saved successfully', 'success');
            } else {
                utils.showToast(result.error || 'Failed to save chapter', 'error');
            }
        } catch (error) {
            console.error('Save error:', error);
            utils.showToast('Network error occurred', 'error');
        } finally {
            // Restore button state
            saveBtn.disabled = false;
            saveBtn.innerHTML = originalText;
        }
    }

    async saveTranslation(content) {
        return await utils.makeApiRequest('api/chapters.php', {
            method: 'PATCH',
            body: JSON.stringify({
                novel_id: this.novelId,
                chapter_number: this.chapterNumber,
                translated_content: content
            })
        });
    }

    showOriginal() {
        const originalPanel = document.getElementById('original-panel');
        const editorPanel = document.getElementById('editor-panel');

        if (originalPanel.classList.contains('d-none')) {
            // Show side-by-side
            originalPanel.classList.remove('d-none');
            editorPanel.classList.remove('col-lg-12');
            editorPanel.classList.add('col-lg-6');
            document.body.classList.add('side-by-side');
        } else {
            // Show modal instead on smaller screens
            const modal = new bootstrap.Modal(document.getElementById('originalTextModal'));
            modal.show();
        }
    }

    hideOriginal() {
        const originalPanel = document.getElementById('original-panel');
        const editorPanel = document.getElementById('editor-panel');

        originalPanel.classList.add('d-none');
        editorPanel.classList.remove('col-lg-6');
        editorPanel.classList.add('col-lg-12');
        document.body.classList.remove('side-by-side');
    }

    toggleFullscreen() {
        const container = document.querySelector('.container-fluid');

        if (!this.isFullscreen) {
            // Enter fullscreen
            container.classList.add('editor-fullscreen');
            this.isFullscreen = true;

            // Update button
            const btn = document.querySelector('[onclick="chapterEditor.toggleFullscreen()"]');
            btn.innerHTML = '<i class="fas fa-compress me-1"></i>Exit Fullscreen';

            // Resize editor
            if (this.editor) {
                this.editor.execCommand('mceFullScreen');
            }
        } else {
            // Exit fullscreen
            container.classList.remove('editor-fullscreen');
            this.isFullscreen = false;

            // Update button
            const btn = document.querySelector('[onclick="chapterEditor.toggleFullscreen()"]');
            btn.innerHTML = '<i class="fas fa-expand me-1"></i>Fullscreen';

            // Resize editor
            if (this.editor) {
                this.editor.execCommand('mceFullScreen');
            }
        }
    }

    toggleAutoSave() {
        this.autoSaveEnabled = !this.autoSaveEnabled;

        const statusSpan = document.getElementById('auto-save-status');
        const toggleBtn = document.getElementById('auto-save-toggle');

        if (this.autoSaveEnabled) {
            statusSpan.textContent = 'ON';
            toggleBtn.classList.remove('btn-outline-success');
            toggleBtn.classList.add('btn-outline-success');
            this.setupAutoSave();
        } else {
            statusSpan.textContent = 'OFF';
            toggleBtn.classList.remove('btn-outline-success');
            toggleBtn.classList.add('btn-outline-secondary');
            if (this.autoSaveInterval) {
                clearInterval(this.autoSaveInterval);
                this.autoSaveInterval = null;
            }
        }
    }

    showHelp() {
        const modal = new bootstrap.Modal(document.getElementById('helpModal'));
        modal.show();
    }

    destroy() {
        // Clean up resources
        if (this.autoSaveInterval) {
            clearInterval(this.autoSaveInterval);
        }

        if (this.editor) {
            tinymce.remove('#translation-editor');
        }

        // Remove event listeners
        window.removeEventListener('beforeunload', this.setupBeforeUnload);
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    if (typeof window.novelId !== 'undefined' && typeof window.chapterNumber !== 'undefined') {
        window.chapterEditor = new ChapterEditor(window.novelId, window.chapterNumber);
    }
});

// Clean up when page is unloaded
window.addEventListener('beforeunload', () => {
    if (window.chapterEditor) {
        window.chapterEditor.destroy();
    }
});
