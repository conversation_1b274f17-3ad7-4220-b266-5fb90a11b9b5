# HTML Entity Decoding Fix

## Problem
Novel titles were coming in with HTML entities like `&#039;` (apostrophe) and `&quot;` (quotation marks) that were not being properly decoded, resulting in titles displaying with encoded entities instead of readable characters.

**Example problematic title:**
```
The one who manipulates the power of the gods ~ I&#039;m scorned for having a &quot;0&quot; stat, but in reality, I&#039;m one of the strongest in the world ~
```

**Should display as:**
```
The one who manipulates the power of the gods ~ I'm scorned for having a "0" stat, but in reality, I'm one of the strongest in the world ~
```

## Root Cause
The issue was in the data flow:
1. **Crawlers** extracted titles and properly decoded HTML entities using `cleanText()`
2. **API endpoints** received titles and applied `sanitizeInput()` which re-encoded them with `htmlspecialchars()`
3. **Database** stored the re-encoded titles with HTML entities
4. **Frontend** displayed the encoded titles without decoding them

## Solution

### 1. New Title Processing Function
Added `processTitleText()` function in `config/config.php`:
```php
function processTitleText($title) {
    if (empty($title)) {
        return '';
    }
    
    // Decode HTML entities (handles &#039;, &quot;, etc.)
    $decoded = html_entity_decode($title, ENT_QUOTES | ENT_HTML5, 'UTF-8');
    
    // Remove extra whitespace
    $cleaned = preg_replace('/\s+/', ' ', $decoded);
    
    // Trim and return
    return trim($cleaned);
}
```

### 2. Updated sanitizeInput Function
Modified `sanitizeInput()` to support preserving decoded entities:
```php
function sanitizeInput($input, $preserveEntities = false) {
    $trimmed = trim($input);
    
    if ($preserveEntities) {
        // For titles and content where we want to preserve decoded entities
        return $trimmed;
    }
    
    return htmlspecialchars($trimmed, ENT_QUOTES, 'UTF-8');
}
```

### 3. Updated API Endpoints
Modified `api/novels.php` to use proper title processing:
```php
$novelData = [
    'original_title' => processTitleText($input['original_title']),
    'translated_title' => processTitleText($input['translated_title']),
    'author' => sanitizeInput($input['author'], true),
    // ... other fields
];
```

### 4. Updated NovelManager
Modified `classes/NovelManager.php` to process chapter titles:
```php
'original_title' => processTitleText($chapter['original_title'] ?? '')
```

### 5. Migration Script
Created `scripts/fix_html_entities.php` to fix existing titles in the database.

## Files Modified

1. **config/config.php** - Added `processTitleText()` function and updated `sanitizeInput()`
2. **api/novels.php** - Updated to use proper title processing
3. **classes/NovelManager.php** - Updated chapter title processing and fixed database method calls
4. **scripts/fix_html_entities.php** - Migration script for existing data
5. **test_html_entities.php** - Test script for the fix
6. **test_complete_flow.php** - Complete flow test

## Testing

Run the following tests to verify the fix:

```bash
# Test the HTML entity decoding function
php test_html_entities.php

# Test the complete flow from API to database to display
php test_complete_flow.php

# Fix existing titles in the database
php scripts/fix_html_entities.php
```

## Results

✅ **Titles are now stored clean in the database** without HTML entities  
✅ **Frontend displays titles correctly** with proper quotes and characters  
✅ **Both novel and chapter titles are processed** consistently  
✅ **Existing data can be migrated** using the provided script  
✅ **XSS protection is maintained** through proper frontend escaping  

## Compatibility

- ✅ **PHP 8.2** compatible
- ✅ **MySQL** compatible  
- ✅ **Maintains existing security** (XSS protection)
- ✅ **Backward compatible** with existing data structure
