<?php
/**
 * WordPress Connection Monitor
 * Monitors and logs WordPress connection health and posting failures
 */

class WordPressConnectionMonitor {
    private $db;
    private $logFile;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->logFile = APP_ROOT . '/logs/wordpress_connections.log';
        
        // Ensure log directory exists
        $logDir = dirname($this->logFile);
        if (!is_dir($logDir)) {
            mkdir($logDir, 0755, true);
        }
    }
    
    /**
     * Log WordPress connection attempt
     */
    public function logConnectionAttempt(string $profileName, string $siteUrl, bool $success, string $error = ''): void {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'profile' => $profileName,
            'site_url' => $siteUrl,
            'success' => $success,
            'error' => $error,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        $this->writeLog('CONNECTION_ATTEMPT', $logEntry);
        
        // Store in database for analysis
        try {
            $this->db->insert('wordpress_connection_log', [
                'profile_name' => $profileName,
                'site_url' => $siteUrl,
                'attempt_type' => 'connection',
                'success' => $success ? 1 : 0,
                'error_message' => $error,
                'memory_usage' => $logEntry['memory_usage'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Failed to log connection attempt to database: " . $e->getMessage());
        }
    }
    
    /**
     * Log WordPress posting attempt
     */
    public function logPostingAttempt(string $profileName, string $postType, int $itemId, bool $success, string $error = '', array $metadata = []): void {
        $logEntry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'profile' => $profileName,
            'post_type' => $postType,
            'item_id' => $itemId,
            'success' => $success,
            'error' => $error,
            'metadata' => $metadata,
            'memory_usage' => memory_get_usage(true),
            'peak_memory' => memory_get_peak_usage(true)
        ];
        
        $this->writeLog('POSTING_ATTEMPT', $logEntry);
        
        // Store in database for analysis
        try {
            $this->db->insert('wordpress_connection_log', [
                'profile_name' => $profileName,
                'site_url' => $metadata['site_url'] ?? '',
                'attempt_type' => 'posting',
                'post_type' => $postType,
                'item_id' => $itemId,
                'success' => $success ? 1 : 0,
                'error_message' => $error,
                'metadata' => json_encode($metadata),
                'memory_usage' => $logEntry['memory_usage'],
                'created_at' => date('Y-m-d H:i:s')
            ]);
        } catch (Exception $e) {
            error_log("Failed to log posting attempt to database: " . $e->getMessage());
        }
    }
    
    /**
     * Get connection statistics for a profile
     */
    public function getConnectionStats(string $profileName, int $hours = 24): array {
        $since = date('Y-m-d H:i:s', time() - ($hours * 3600));
        
        try {
            $stats = $this->db->fetchOne(
                "SELECT 
                    COUNT(*) as total_attempts,
                    SUM(success) as successful_attempts,
                    COUNT(*) - SUM(success) as failed_attempts,
                    AVG(memory_usage) as avg_memory_usage,
                    MAX(memory_usage) as peak_memory_usage
                 FROM wordpress_connection_log 
                 WHERE profile_name = ? AND created_at >= ?",
                [$profileName, $since]
            );
            
            $recentErrors = $this->db->fetchAll(
                "SELECT error_message, COUNT(*) as count
                 FROM wordpress_connection_log 
                 WHERE profile_name = ? AND created_at >= ? AND success = 0 AND error_message != ''
                 GROUP BY error_message
                 ORDER BY count DESC
                 LIMIT 5",
                [$profileName, $since]
            );
            
            return [
                'success' => true,
                'stats' => $stats ?: [
                    'total_attempts' => 0,
                    'successful_attempts' => 0,
                    'failed_attempts' => 0,
                    'avg_memory_usage' => 0,
                    'peak_memory_usage' => 0
                ],
                'recent_errors' => $recentErrors,
                'success_rate' => $stats && $stats['total_attempts'] > 0 
                    ? round(($stats['successful_attempts'] / $stats['total_attempts']) * 100, 2) 
                    : 0
            ];
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Detect potential connection issues
     */
    public function detectIssues(string $profileName): array {
        $issues = [];
        $stats = $this->getConnectionStats($profileName, 24);
        
        if (!$stats['success']) {
            return ['error' => 'Failed to analyze connection stats'];
        }
        
        // Check success rate
        if ($stats['success_rate'] < 80 && $stats['stats']['total_attempts'] > 5) {
            $issues[] = [
                'type' => 'low_success_rate',
                'severity' => 'high',
                'message' => "Low success rate: {$stats['success_rate']}% over last 24 hours",
                'recommendation' => 'Check WordPress site availability and credentials'
            ];
        }
        
        // Check for memory issues
        if ($stats['stats']['peak_memory_usage'] > 128 * 1024 * 1024) { // 128MB
            $issues[] = [
                'type' => 'high_memory_usage',
                'severity' => 'medium',
                'message' => 'High memory usage detected: ' . $this->formatBytes($stats['stats']['peak_memory_usage']),
                'recommendation' => 'Consider optimizing content size or increasing PHP memory limit'
            ];
        }
        
        // Check for common error patterns
        foreach ($stats['recent_errors'] as $error) {
            if (strpos($error['error_message'], 'timeout') !== false && $error['count'] > 3) {
                $issues[] = [
                    'type' => 'timeout_errors',
                    'severity' => 'high',
                    'message' => "Frequent timeout errors: {$error['count']} occurrences",
                    'recommendation' => 'Check network connectivity and WordPress site performance'
                ];
            }
            
            if (strpos($error['error_message'], 'authentication') !== false && $error['count'] > 1) {
                $issues[] = [
                    'type' => 'auth_errors',
                    'severity' => 'high',
                    'message' => "Authentication errors: {$error['count']} occurrences",
                    'recommendation' => 'Verify WordPress credentials and application password'
                ];
            }
        }
        
        return $issues;
    }
    
    /**
     * Clean old log entries
     */
    public function cleanOldLogs(int $daysToKeep = 30): int {
        $cutoffDate = date('Y-m-d H:i:s', time() - ($daysToKeep * 24 * 3600));
        
        try {
            $stmt = $this->db->query(
                "DELETE FROM wordpress_connection_log WHERE created_at < ?",
                [$cutoffDate]
            );
            
            return $stmt->rowCount();
        } catch (Exception $e) {
            error_log("Failed to clean old WordPress connection logs: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Write log entry to file
     */
    private function writeLog(string $type, array $data): void {
        $logLine = date('Y-m-d H:i:s') . " [$type] " . json_encode($data) . PHP_EOL;
        file_put_contents($this->logFile, $logLine, FILE_APPEND | LOCK_EX);
    }
    
    /**
     * Format bytes for human reading
     */
    private function formatBytes(int $bytes): string {
        $units = ['B', 'KB', 'MB', 'GB'];
        $bytes = max($bytes, 0);
        $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
        $pow = min($pow, count($units) - 1);
        
        $bytes /= (1 << (10 * $pow));
        
        return round($bytes, 2) . ' ' . $units[$pow];
    }
}
