<?php
/**
 * Chunking Performance Optimizer
 * Analyzes translation performance and suggests optimal chunking settings
 */

require_once __DIR__ . '/../config/config.php';

class ChunkingOptimizer {
    private $db;
    private $chapterChunker;
    
    public function __construct() {
        $this->db = Database::getInstance();
        $this->chapterChunker = new ChapterChunker();
    }
    
    /**
     * Analyze current chunking performance
     */
    public function analyzePerformance(): array {
        echo "Analyzing chunking performance...\n";
        
        // Get overall statistics
        $stats = $this->chapterChunker->getChunkingStatistics();
        
        // Get recent performance data
        $recentPerformance = $this->getRecentPerformanceData();
        
        // Get timeout analysis
        $timeoutAnalysis = $this->analyzeTimeouts();
        
        // Get chunk size distribution
        $sizeDistribution = $this->getChunkSizeDistribution();
        
        return [
            'overall_stats' => $stats,
            'recent_performance' => $recentPerformance,
            'timeout_analysis' => $timeoutAnalysis,
            'size_distribution' => $sizeDistribution,
            'recommendations' => $this->generateRecommendations($stats, $recentPerformance, $timeoutAnalysis)
        ];
    }
    
    /**
     * Get recent performance data
     */
    private function getRecentPerformanceData(): array {
        $data = $this->db->fetchAll(
            "SELECT 
                DATE(cc.translation_date) as date,
                COUNT(*) as total_chunks,
                AVG(cc.character_count) as avg_chunk_size,
                AVG(TIMESTAMPDIFF(SECOND, cc.updated_at, cc.translation_date)) as avg_translation_time,
                SUM(CASE WHEN cc.translation_status = 'error' THEN 1 ELSE 0 END) as failed_chunks,
                SUM(CASE WHEN cc.translation_status = 'completed' THEN 1 ELSE 0 END) as successful_chunks
             FROM chapter_chunks cc
             WHERE cc.translation_date >= DATE_SUB(NOW(), INTERVAL 7 DAY)
             GROUP BY DATE(cc.translation_date)
             ORDER BY date DESC"
        );
        
        return $data;
    }
    
    /**
     * Analyze timeout patterns
     */
    private function analyzeTimeouts(): array {
        $timeoutData = $this->db->fetchAll(
            "SELECT 
                cc.character_count,
                cc.chunk_type,
                TIMESTAMPDIFF(SECOND, cc.updated_at, cc.translation_date) as translation_time,
                cc.translation_status
             FROM chapter_chunks cc
             WHERE cc.translation_date >= DATE_SUB(NOW(), INTERVAL 14 DAY)
             ORDER BY cc.character_count DESC"
        );
        
        $timeoutsBySize = [];
        $timeoutsByType = [];
        
        foreach ($timeoutData as $chunk) {
            $sizeRange = $this->getSizeRange($chunk['character_count']);
            
            if (!isset($timeoutsBySize[$sizeRange])) {
                $timeoutsBySize[$sizeRange] = ['total' => 0, 'timeouts' => 0];
            }
            
            if (!isset($timeoutsByType[$chunk['chunk_type']])) {
                $timeoutsByType[$chunk['chunk_type']] = ['total' => 0, 'timeouts' => 0];
            }
            
            $timeoutsBySize[$sizeRange]['total']++;
            $timeoutsByType[$chunk['chunk_type']]['total']++;
            
            if ($chunk['translation_status'] === 'error' || $chunk['translation_time'] > 300) {
                $timeoutsBySize[$sizeRange]['timeouts']++;
                $timeoutsByType[$chunk['chunk_type']]['timeouts']++;
            }
        }
        
        return [
            'by_size' => $timeoutsBySize,
            'by_type' => $timeoutsByType
        ];
    }
    
    /**
     * Get chunk size distribution
     */
    private function getChunkSizeDistribution(): array {
        $distribution = $this->db->fetchAll(
            "SELECT 
                CASE 
                    WHEN character_count < 5000 THEN 'Small (< 5K)'
                    WHEN character_count < 10000 THEN 'Medium (5K-10K)'
                    WHEN character_count < 15000 THEN 'Large (10K-15K)'
                    WHEN character_count < 20000 THEN 'Very Large (15K-20K)'
                    ELSE 'Oversized (> 20K)'
                END as size_category,
                COUNT(*) as chunk_count,
                AVG(TIMESTAMPDIFF(SECOND, updated_at, translation_date)) as avg_time,
                SUM(CASE WHEN translation_status = 'error' THEN 1 ELSE 0 END) as error_count
             FROM chapter_chunks
             WHERE translation_date IS NOT NULL
             GROUP BY size_category
             ORDER BY MIN(character_count)"
        );
        
        return $distribution;
    }
    
    /**
     * Generate optimization recommendations
     */
    private function generateRecommendations(array $stats, array $recentPerformance, array $timeoutAnalysis): array {
        $recommendations = [];
        
        // Check overall success rate
        if ($stats['success_rate'] < 90) {
            $recommendations[] = [
                'type' => 'critical',
                'message' => 'Low success rate detected. Consider reducing chunk size limit.',
                'action' => 'reduce_chunk_size',
                'current_value' => $stats['current_chunk_size_limit'],
                'suggested_value' => max(5000, $stats['current_chunk_size_limit'] * 0.8)
            ];
        }
        
        // Check recent error rate
        if ($stats['recent_error_rate'] > 20) {
            $recommendations[] = [
                'type' => 'warning',
                'message' => 'High recent error rate. Enable dynamic sizing if not already active.',
                'action' => 'enable_dynamic_sizing',
                'current_value' => $stats['dynamic_sizing_enabled'],
                'suggested_value' => true
            ];
        }
        
        // Check average translation time
        if ($stats['avg_translation_time'] > 180) {
            $recommendations[] = [
                'type' => 'optimization',
                'message' => 'Long average translation times. Consider smaller chunks.',
                'action' => 'optimize_chunk_size',
                'current_value' => $stats['avg_chunk_size'],
                'suggested_value' => max(8000, $stats['avg_chunk_size'] * 0.9)
            ];
        }
        
        // Check for oversized chunks
        foreach ($timeoutAnalysis['by_size'] as $sizeRange => $data) {
            if (strpos($sizeRange, 'Oversized') !== false && $data['total'] > 0) {
                $timeoutRate = $data['timeouts'] / $data['total'];
                if ($timeoutRate > 0.5) {
                    $recommendations[] = [
                        'type' => 'critical',
                        'message' => 'High timeout rate for oversized chunks. Implement stricter size limits.',
                        'action' => 'enforce_max_chunk_size',
                        'current_value' => 'unlimited',
                        'suggested_value' => 18000
                    ];
                }
            }
        }
        
        return $recommendations;
    }
    
    /**
     * Apply optimization recommendations
     */
    public function applyOptimizations(array $recommendations): array {
        $results = [];
        
        foreach ($recommendations as $rec) {
            try {
                switch ($rec['action']) {
                    case 'reduce_chunk_size':
                        $this->chapterChunker->updateChunkSize($rec['suggested_value']);
                        $results[] = "✓ Reduced chunk size to {$rec['suggested_value']}";
                        break;
                        
                    case 'enable_dynamic_sizing':
                        $this->updatePreference('dynamic_chunk_sizing', 'true');
                        $results[] = "✓ Enabled dynamic chunk sizing";
                        break;
                        
                    case 'optimize_chunk_size':
                        $this->chapterChunker->updateChunkSize($rec['suggested_value']);
                        $results[] = "✓ Optimized chunk size to {$rec['suggested_value']}";
                        break;
                        
                    case 'enforce_max_chunk_size':
                        $this->updatePreference('max_chunk_size', $rec['suggested_value']);
                        $results[] = "✓ Set maximum chunk size to {$rec['suggested_value']}";
                        break;
                }
            } catch (Exception $e) {
                $results[] = "✗ Failed to apply {$rec['action']}: " . $e->getMessage();
            }
        }
        
        return $results;
    }
    
    /**
     * Update user preference
     */
    private function updatePreference(string $key, string $value): void {
        $this->db->update(
            'user_preferences',
            ['preference_value' => $value],
            'preference_key = ?',
            [$key]
        );
    }
    
    /**
     * Get size range for chunk
     */
    private function getSizeRange(int $size): string {
        if ($size < 5000) return 'Small (< 5K)';
        if ($size < 10000) return 'Medium (5K-10K)';
        if ($size < 15000) return 'Large (10K-15K)';
        if ($size < 20000) return 'Very Large (15K-20K)';
        return 'Oversized (> 20K)';
    }
}

// CLI interface
if (php_sapi_name() === 'cli') {
    $optimizer = new ChunkingOptimizer();
    
    echo "=== Chunking Performance Optimizer ===\n\n";
    
    $analysis = $optimizer->analyzePerformance();
    
    echo "Overall Statistics:\n";
    echo "- Total chunks: " . $analysis['overall_stats']['total_chunks'] . "\n";
    echo "- Success rate: " . $analysis['overall_stats']['success_rate'] . "%\n";
    echo "- Average chunk size: " . $analysis['overall_stats']['avg_chunk_size'] . " characters\n";
    echo "- Recent error rate: " . $analysis['overall_stats']['recent_error_rate'] . "%\n\n";
    
    if (!empty($analysis['recommendations'])) {
        echo "Recommendations:\n";
        foreach ($analysis['recommendations'] as $rec) {
            echo "- [{$rec['type']}] {$rec['message']}\n";
        }
        
        echo "\nApply optimizations? (y/n): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim($line) === 'y') {
            echo "\nApplying optimizations...\n";
            $results = $optimizer->applyOptimizations($analysis['recommendations']);
            foreach ($results as $result) {
                echo $result . "\n";
            }
        }
    } else {
        echo "No optimizations needed. Your chunking configuration is performing well!\n";
    }
}
?>
