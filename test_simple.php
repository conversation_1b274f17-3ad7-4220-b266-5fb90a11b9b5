<?php
// Simple test to check raw HTML from 69shuba
$url = 'https://69shuba.cx/book/44425/2.htm';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_ENCODING, 'gzip, deflate');
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
    'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
    'Accept-Encoding: gzip, deflate, br',
]);

$html = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);
curl_close($ch);

if ($error) {
    echo "CURL Error: $error\n";
    exit;
}

echo "HTTP Code: $httpCode\n";
echo "HTML Length: " . strlen($html) . "\n";
echo "Detected Encoding: " . mb_detect_encoding($html) . "\n";
echo "Is valid UTF-8: " . (mb_check_encoding($html, 'UTF-8') ? 'Yes' : 'No') . "\n";

echo "\nFirst 1000 characters of raw HTML:\n";
echo substr($html, 0, 1000) . "\n";

// Test parsing with DOMDocument
echo "\n--- Testing DOMDocument parsing ---\n";
$dom = new DOMDocument();
libxml_use_internal_errors(true);
$dom->loadHTML('<?xml encoding="UTF-8">' . $html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD);
libxml_clear_errors();

$textContent = $dom->textContent;
echo "DOM textContent length: " . strlen($textContent) . "\n";
echo "DOM textContent encoding: " . mb_detect_encoding($textContent) . "\n";
echo "DOM textContent first 500 chars:\n";
echo substr($textContent, 0, 500) . "\n";
?>
