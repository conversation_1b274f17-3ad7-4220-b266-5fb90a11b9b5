<?php
// Test direct content extraction without using the problematic crawler methods

$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "=== Direct Content Extraction Test ===\n";
echo "URL: $chapterUrl\n\n";

// Get raw HTML
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $chapterUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_ENCODING, '');

$rawHtml = curl_exec($ch);
curl_close($ch);

echo "Raw HTML length: " . strlen($rawHtml) . "\n";
echo "Raw HTML encoding: " . mb_detect_encoding($rawHtml) . "\n";

// Convert from GBK to UTF-8 properly
$html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
echo "Converted HTML length: " . strlen($html) . "\n";
echo "Converted HTML encoding: " . mb_detect_encoding($html) . "\n";
echo "Valid UTF-8: " . (mb_check_encoding($html, 'UTF-8') ? 'Yes' : 'No') . "\n";

// Update meta charset
$html = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $html);

// Parse with DOMDocument
$dom = new DOMDocument();
libxml_use_internal_errors(true);
$success = $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
libxml_clear_errors();

if (!$success) {
    echo "DOM parsing failed\n";
    exit;
}

echo "DOM parsing successful\n";

// Get text content directly
$allText = $dom->textContent;
echo "All text length: " . strlen($allText) . "\n";
echo "All text encoding: " . mb_detect_encoding($allText) . "\n";
echo "Valid UTF-8: " . (mb_check_encoding($allText, 'UTF-8') ? 'Yes' : 'No') . "\n";

$chineseCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $allText);
echo "Chinese character count: $chineseCount\n";

if ($chineseCount > 0) {
    echo "\n=== Content Extraction ===\n";
    
    // Try to extract the story content using a pattern that works with the actual structure
    // Look for content after the chapter title and before navigation
    if (preg_match('/第1章\s+怒气.*?(\d{4}-\d{1,2}-\d{1,2}).*?第1章\s+怒气(.+?)(?:上一章|下一章|目录|Copyright|69书吧|$)/s', $allText, $matches)) {
        $storyContent = trim($matches[2]);
        
        echo "Pattern 1 matched!\n";
        echo "Date: " . $matches[1] . "\n";
        echo "Story content length: " . strlen($storyContent) . "\n";
        echo "Chinese chars in story: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $storyContent) . "\n";
        
        if (preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $storyContent) > 0) {
            echo "First 1000 characters:\n";
            echo "---\n";
            echo mb_substr($storyContent, 0, 1000, 'UTF-8') . "\n";
            echo "---\n";
        }
    } else {
        echo "Pattern 1 failed, trying simpler patterns...\n";
        
        // Try a much simpler pattern - just look for substantial Chinese text
        if (preg_match('/[\x{4E00}-\x{9FFF}]{10,}[^上下目录收藏Copyright69书吧]{200,}/u', $allText, $matches)) {
            echo "Found Chinese text block:\n";
            echo "Length: " . strlen($matches[0]) . "\n";
            echo "First 500 chars:\n";
            echo mb_substr($matches[0], 0, 500, 'UTF-8') . "\n";
        } else {
            echo "No substantial Chinese text found\n";
            echo "Sample of all text (first 2000 chars):\n";
            echo mb_substr($allText, 0, 2000, 'UTF-8') . "\n";
        }
    }
} else {
    echo "No Chinese characters found in extracted text\n";
    echo "Sample of all text (first 1000 chars):\n";
    echo substr($allText, 0, 1000) . "\n";
}
?>
