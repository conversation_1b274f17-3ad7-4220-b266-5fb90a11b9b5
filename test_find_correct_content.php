<?php
// Test to find the correct novel content

$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "=== Finding Correct Novel Content ===\n";
echo "URL: $chapterUrl\n\n";

// Get and convert HTML
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $chapterUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_ENCODING, '');

$rawHtml = curl_exec($ch);
curl_close($ch);

$html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
$html = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $html);

$dom = new DOMDocument();
libxml_use_internal_errors(true);
$dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
libxml_clear_errors();

$allText = $dom->textContent;

echo "Total text length: " . strlen($allText) . "\n";
echo "Chinese characters: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $allText) . "\n";

// Look for the novel title we expect
if (strpos($allText, '从零开始缔造游戏帝国') !== false) {
    echo "✓ Found expected novel title: 从零开始缔造游戏帝国\n";
} else {
    echo "✗ Expected novel title not found\n";
}

// Look for the chapter title we expect
if (strpos($allText, '第1章') !== false && strpos($allText, '怒气') !== false) {
    echo "✓ Found expected chapter title: 第1章 怒气\n";
} else {
    echo "✗ Expected chapter title not found\n";
}

// Try to find any substantial story content
echo "\n=== Searching for Story Content ===\n";

// Look for patterns that might indicate story content
$patterns = [
    '/从零开始缔造游戏帝国.*?第1章.*?怒气(.+?)(?:上一章|下一章|目录|Copyright|69书吧|$)/s',
    '/第1章.*?怒气.*?(\d{4}-\d{1,2}-\d{1,2})(.+?)(?:上一章|下一章|目录|Copyright|69书吧|$)/s',
    '/怒气.*?(\d{4}-\d{1,2}-\d{1,2})(.+?)(?:上一章|下一章|目录|Copyright|69书吧|$)/s',
    '/第1章.*?怒气(.+?)(?:上一章|下一章|目录|Copyright|69书吧|$)/s'
];

foreach ($patterns as $i => $pattern) {
    echo "Trying pattern " . ($i + 1) . "...\n";
    if (preg_match($pattern, $allText, $matches)) {
        $content = trim($matches[count($matches) - 1]); // Get the last capture group
        
        echo "✓ Pattern " . ($i + 1) . " matched!\n";
        echo "Content length: " . strlen($content) . "\n";
        echo "Chinese chars: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content) . "\n";
        
        if (preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content) > 50) {
            echo "First 1000 characters:\n";
            echo "---\n";
            echo mb_substr($content, 0, 1000, 'UTF-8') . "\n";
            echo "---\n";
            break;
        } else {
            echo "Content too short or no Chinese characters\n";
        }
    } else {
        echo "Pattern " . ($i + 1) . " failed\n";
    }
}

// If no patterns work, let's see what's actually in the page
echo "\n=== Page Content Analysis ===\n";
echo "Looking for all occurrences of key terms...\n";

$pos1 = strpos($allText, '从零开始缔造游戏帝国');
$pos2 = strpos($allText, '第1章');
$pos3 = strpos($allText, '怒气');

echo "Novel title position: " . ($pos1 !== false ? $pos1 : 'not found') . "\n";
echo "Chapter number position: " . ($pos2 !== false ? $pos2 : 'not found') . "\n";
echo "Chapter title position: " . ($pos3 !== false ? $pos3 : 'not found') . "\n";

if ($pos1 !== false && $pos2 !== false && $pos3 !== false) {
    $start = min($pos1, $pos2, $pos3);
    $context = mb_substr($allText, max(0, $start - 200), 2000, 'UTF-8');
    echo "\nContext around key terms:\n";
    echo "---\n";
    echo $context . "\n";
    echo "---\n";
}
?>
