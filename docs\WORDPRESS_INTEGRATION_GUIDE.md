# WordPress Integration Guide

This guide explains how to set up and use the WordPress integration feature in the Novel Translation Application.

## Overview

The WordPress integration allows you to automatically post translated novels and chapters to your WordPress website with a single button click. The system handles:

- Automatic novel creation in WordPress
- Chapter posting with proper formatting
- Duplicate prevention
- Error handling and status tracking
- Relationship management between novels and chapters

## Features

### Core Functionality
- **One-click posting**: Post chapters directly from the chapter view page
- **Bulk operations**: Post multiple chapters at once from the novel details page
- **Automatic novel creation**: Creates novel pages/posts automatically before posting chapters
- **Status tracking**: Tracks which content has been posted to avoid duplicates
- **Content formatting**: Preserves formatting, furigana, and structure

### WordPress Structure
- **Novels**: Created as WordPress pages or posts (configurable)
- **Chapters**: Created as WordPress posts with novel title prefix
- **Relationships**: Chapters are linked to their parent novels
- **Categories**: Optional category assignment for organization

## Setup Instructions

### 1. WordPress Preparation

#### Create Application Password
1. Log into your WordPress admin panel
2. Go to **Users → Your Profile**
3. Scroll down to **Application Passwords**
4. Enter a name like "Novel Translator"
5. Click **Add New Application Password**
6. Copy the generated password (format: `xxxx xxxx xxxx xxxx xxxx xxxx`)

#### Verify REST API
Ensure your WordPress site has the REST API enabled (it's enabled by default in modern WordPress).

### 2. Application Configuration

#### Access Settings
1. Navigate to the **Settings** page in your novel translation application
2. Locate the **WordPress Integration** section

#### Configure Connection
- **WordPress Site URL**: Your full WordPress site URL (e.g., `https://yoursite.com`)
- **WordPress Username**: Your WordPress admin username
- **Application Password**: The password generated in step 1
- **Novel Post Type**: Choose `Page` or `Post` for novels (default types)
- **Chapter Post Type**: Choose `Post` or `Page` for chapters (default types)
- **Use Custom Post Types**: Enable to use custom post types instead of default page/post
- **Novel Custom Post Type**: Custom post type slug for novels (e.g., `novel`, `book`, `story`)
- **Chapter Custom Post Type**: Custom post type slug for chapters (e.g., `chapter`, `episode`, `part`)
- **Default Category ID**: Optional WordPress category ID for posts
- **Auto-publish**: Check to publish immediately, uncheck to save as drafts
- **Include Original Titles**: Check to show both original and translated titles

#### Test Connection
After saving settings, the system will automatically test the connection and display the result.

### 3. Verification

Run the test script to verify everything is working:
```
http://yoursite.com/test_wordpress_integration.php
```

## Usage Guide

### Posting Individual Chapters

1. **Navigate to Chapter**: Go to any translated chapter via the chapter view page
2. **Click Post Button**: Look for the "Post to WordPress" button (green button with WordPress icon)
3. **Automatic Processing**: The system will:
   - Check if the novel exists in WordPress (create if needed)
   - Post the chapter with proper formatting
   - Update the button to show success status
4. **View Result**: Click the "Posted" button to view the chapter on WordPress

### Bulk Posting

1. **Novel Details Page**: Go to any novel's details page
2. **Enable Bulk Mode**: Click "Bulk Select" to enable selection mode
3. **Select Chapters**: Check the boxes for translated chapters you want to post
4. **Bulk Action**: Click "Post to WordPress" in the bulk actions section
5. **Monitor Progress**: The system will post each chapter and show results

### Status Indicators

- **Green "Posted" button**: Chapter already posted to WordPress
- **Green "Post to WordPress" button**: Ready to post
- **Disabled button**: Chapter not translated yet
- **Error messages**: Configuration or connection issues

## Content Structure

### Novel Posts/Pages
```
Title: [Translated Title] ([Original Title])
Content:
- Author information
- Publication date
- Source platform and URL
- Synopsis (translated or original)
- Chapter list information
```

### Chapter Posts
```
Title: [Novel Title] - Chapter [Number]: [Chapter Title]
Content:
- Chapter number and title
- Translated content with proper formatting
- Word count information
- Furigana preserved as HTML ruby tags
```

## Troubleshooting

### Common Issues

#### Connection Failed
- **Check URL**: Ensure WordPress site URL is correct (no trailing slash)
- **Verify Credentials**: Double-check username and application password
- **Test Accessibility**: Make sure your WordPress site is accessible from your server

#### Authentication Failed
- **Regenerate Password**: Create a new application password in WordPress
- **Check Username**: Verify the username is correct (case-sensitive)
- **User Permissions**: Ensure the user has publishing permissions

#### Posting Errors
- **Content Too Large**: Very long chapters might hit WordPress limits
- **Category Issues**: Invalid category ID will be ignored
- **Duplicate Content**: System prevents duplicates automatically

#### Custom Post Type Issues
- **Post Type Not Found**: The specified custom post type doesn't exist in WordPress
- **Permission Denied**: User doesn't have permission to create posts of that type
- **Invalid Slug**: Post type slug contains invalid characters or format

### Error Messages

| Error | Solution |
|-------|----------|
| "WordPress not configured" | Complete the settings configuration |
| "Authentication failed" | Check username and application password |
| "Chapter already posted" | Chapter exists in WordPress (not an error) |
| "Failed to post novel first" | Novel creation failed, check permissions |
| "Network error" | Check internet connection and WordPress accessibility |
| "Post type 'xyz' not found" | Verify custom post type slug is correct |
| "Custom post type validation failed" | Check available post types and fix configuration |
| "Failed to fetch post types" | Check WordPress REST API accessibility |

### Debug Steps

1. **Test Connection**: Use the test script to verify basic connectivity
2. **Check Logs**: Look for error messages in browser console
3. **Verify Settings**: Ensure all WordPress settings are correct
4. **Test Manually**: Try creating a post manually in WordPress
5. **Check Permissions**: Verify user has proper WordPress permissions

## Custom Post Types

### Overview
Custom post types allow you to create specialized content types in WordPress beyond the default posts and pages. Many themes and plugins create custom post types for specific content like books, novels, products, etc.

### Finding Custom Post Type Names

#### Method 1: WordPress Admin
1. Look for custom menu items in your WordPress admin sidebar
2. Custom post types often appear as separate menu items (e.g., "Books", "Novels", "Stories")
3. The URL when viewing these sections often contains the post type slug

#### Method 2: Using the Settings Page
1. Configure your WordPress connection in the settings
2. Enable "Use Custom Post Types"
3. Click "Fetch Available Post Types" to see all available types
4. The system will display a table with post type slugs, names, and properties

#### Method 3: Developer Tools
If you have access to your WordPress installation:
```php
// Add this to your theme's functions.php temporarily
function show_post_types() {
    $post_types = get_post_types(array('public' => true), 'objects');
    foreach ($post_types as $post_type) {
        echo $post_type->name . ' - ' . $post_type->label . '<br>';
    }
}
add_action('wp_footer', 'show_post_types');
```

#### Method 4: REST API
Visit: `https://yoursite.com/wp-json/wp/v2/types` to see all available post types.

### Configuration Steps

1. **Enable Custom Post Types**
   - Check "Use Custom Post Types" in the WordPress settings
   - The custom post type fields will appear

2. **Enter Post Type Slugs**
   - **Novel Custom Post Type**: Enter the slug for your novel post type (e.g., `novel`, `book`, `story`)
   - **Chapter Custom Post Type**: Enter the slug for your chapter post type (e.g., `chapter`, `episode`, `part`)

3. **Validate Configuration**
   - Click "Fetch Available Post Types" to verify your post types exist
   - Save settings to test the configuration
   - The system will validate that the specified post types exist in WordPress

### Common Custom Post Type Examples

| Theme/Plugin | Novel Post Type | Chapter Post Type | Notes |
|--------------|----------------|-------------------|-------|
| BookPress | `book` | `chapter` | Popular book publishing theme |
| Novel Theme | `novel` | `episode` | Fiction-focused themes |
| Custom Setup | `story` | `part` | Custom implementations |
| WP Book | `wp_book` | `wp_chapter` | Plugin-based solutions |

### Hierarchical vs Non-Hierarchical

- **Hierarchical Post Types** (like pages): Support parent-child relationships
- **Non-Hierarchical Post Types** (like posts): Don't support parent-child relationships

The system automatically handles both types:
- For hierarchical types: Chapters will be set as children of novels
- For non-hierarchical types: Chapters will be independent posts with novel title prefix

### Fallback Behavior

If custom post types are enabled but:
- The specified post type doesn't exist
- There's an error accessing the post type
- The post type doesn't support the required features

The system will:
1. Display validation errors in the settings
2. Fall back to default post types (page/post)
3. Log the issue for troubleshooting

## Advanced Configuration

### Category Management
Set up categories in WordPress first, then use their IDs in the settings for automatic categorization.

### Content Formatting
The system automatically:
- Converts line breaks to paragraphs
- Preserves furigana as HTML ruby tags
- Escapes HTML entities for safety
- Maintains chapter structure

## API Reference

### WordPress Service Methods
- `testConnection()`: Test WordPress connectivity
- `postNovel($novelId)`: Post a novel to WordPress
- `postChapter($chapterId)`: Post a chapter to WordPress
- `getNovelPostingStatus($novelId)`: Check novel posting status
- `getChapterPostingStatus($chapterId)`: Check chapter posting status

### Database Tables
- `wordpress_posts`: Tracks posted content and WordPress IDs
- `user_preferences`: Stores WordPress configuration

## Security Considerations

- **Application Passwords**: More secure than regular passwords
- **HTTPS**: Use HTTPS for both sites when possible
- **Permissions**: Use WordPress users with minimal required permissions
- **Validation**: All content is validated and escaped before posting

## Limitations

- **Content Size**: Very large chapters may hit WordPress limits
- **Rate Limiting**: WordPress may limit rapid posting
- **Formatting**: Complex formatting may not transfer perfectly
- **Media**: Images and media files are not automatically transferred

## Support

For issues with WordPress integration:

1. Run the test script first
2. Check the troubleshooting section
3. Verify WordPress and application logs
4. Ensure both systems are up to date
