<?php
/**
 * Novel Details Page
 * Displays detailed information about a specific novel with chapter management
 */

require_once 'config/config.php';
require_once 'includes/header.php';

// Get novel ID from URL
$novelId = isset($_GET['id']) ? (int)$_GET['id'] : 0;

if (!$novelId) {
    header('Location: index.php');
    exit;
}

renderHeader('Novel Details');
?>

<?php include 'includes/navigation.php'; ?>

<div class="container mt-4">
    <!-- Back Navigation -->
    <div class="mb-3">
        <a href="index.php" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Dashboard
        </a>
    </div>

    <!-- Novel Details Content -->
    <div id="novel-details-content">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading novel details...</p>
        </div>
    </div>
</div>

<?php renderFooter(['assets/js/novel-details.js']); ?>

<script>
// Pass novel ID to JavaScript
window.novelId = <?= $novelId ?>;
</script>
