<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "=== Comprehensive Debug Test ===\n";
echo "URL: $chapterUrl\n\n";

try {
    // Use the debug method to see step-by-step extraction
    $debug = $crawler->debugChapterExtraction($chapterUrl);
    
    echo "Debug Results:\n";
    foreach ($debug as $key => $value) {
        if (is_string($value) && strlen($value) > 200) {
            echo "$key: " . substr($value, 0, 200) . "... (truncated)\n";
        } else {
            echo "$key: $value\n";
        }
    }
    
    echo "\n=== Manual Content Extraction Test ===\n";
    
    // Get raw HTML and convert encoding manually
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $chapterUrl);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    
    $rawHtml = curl_exec($ch);
    curl_close($ch);
    
    // Convert from GBK to UTF-8
    $html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
    $html = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $html);
    
    // Parse with DOMDocument
    $dom = new DOMDocument();
    libxml_use_internal_errors(true);
    $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
    libxml_clear_errors();
    
    $xpath = new DOMXPath($dom);
    
    // Find the mybox div
    $myboxElements = $xpath->query('//div[contains(@class, "mybox")]');
    if ($myboxElements->length > 0) {
        $mybox = $myboxElements->item(0);
        $allText = $mybox->textContent;
        
        echo "Mybox text content length: " . strlen($allText) . "\n";
        echo "Chinese chars in mybox: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $allText) . "\n";
        
        // Try to extract just the story content using a simple pattern
        // Look for content after the date and chapter title
        if (preg_match('/\d{4}-\d{1,2}-\d{1,2}\s*第\d+章[^\n]*\n(.+?)(?:上一章|下一章|目录|$)/s', $allText, $matches)) {
            $storyContent = trim($matches[1]);
            
            // Clean up the content
            $storyContent = preg_replace('/var\s+bookinfo[^}]+}/s', '', $storyContent);
            $storyContent = preg_replace('/\n{3,}/', "\n\n", $storyContent);
            $storyContent = trim($storyContent);
            
            echo "\n=== EXTRACTED STORY CONTENT ===\n";
            echo "Length: " . strlen($storyContent) . " bytes\n";
            echo "Chinese chars: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $storyContent) . "\n";
            echo "Valid UTF-8: " . (mb_check_encoding($storyContent, 'UTF-8') ? 'Yes' : 'No') . "\n";
            
            if (preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $storyContent) > 0) {
                echo "\nFirst 1000 characters:\n";
                echo "---\n";
                echo mb_substr($storyContent, 0, 1000, 'UTF-8') . "\n";
                echo "---\n";
            } else {
                echo "\nRaw content (first 500 chars):\n";
                echo substr($storyContent, 0, 500) . "\n";
            }
        } else {
            echo "Could not match story content pattern\n";
            echo "First 1000 chars of mybox content:\n";
            echo substr($allText, 0, 1000) . "\n";
        }
    } else {
        echo "No mybox element found\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
