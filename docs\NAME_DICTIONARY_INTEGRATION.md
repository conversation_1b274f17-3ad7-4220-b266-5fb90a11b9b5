# Name Dictionary Integration in Novel Translation

## Overview

The name dictionary system ensures character name consistency throughout novel translations by maintaining a database of character names with their preferred translations or romanizations.

## How It Works

### 1. Name Dictionary Storage

Names are stored in the `name_dictionary` table with the following structure:
- `original_name`: The original character name (e.g., "田中")
- `romanization`: Romanized version (e.g., "<PERSON>")
- `translation`: Preferred English translation (e.g., "<PERSON><PERSON> <PERSON>")
- `name_type`: Type of name (character, location, etc.)
- `frequency`: How often the name appears
- `novel_id`: Links to specific novel

### 2. Translation Process Integration

#### Step 1: Name Dictionary Retrieval
When translating a chapter, the system:
1. Retrieves all name dictionary entries for the novel
2. Orders by frequency (most common names first)
3. Includes names with translation, romanization, or original form

#### Step 2: Context Preparation
For each translation request, the name dictionary is included in the context:
```php
$context = [
    'type' => 'chapter',
    'names' => $nameDictionary,
    'chunk_number' => 1,
    'total_chunks' => 3
];
```

#### Step 3: Prompt Enhancement
The translation prompt includes specific instructions:
```
IMPORTANT: Use these consistent name translations throughout the text:
- 田中 → Tanaka
- 山田 → Yamada  
- 佐藤 → <PERSON>. Sato
Always use these exact name forms for consistency across all translations.
```

#### Step 4: Fallback Logic
The system uses a priority order for name selection:
1. **Translation** (if available): Use the explicit translation
2. **Romanization** (if no translation): Use the romanized form
3. **Original** (if neither): Keep the original name

## Implementation Details

### DeepSeekTranslationService
- Enhanced `buildTranslationPrompt()` method with proper fallback logic
- Added detailed logging for name dictionary usage
- Improved prompt instructions for better AI understanding

### TranslationService  
- Enhanced name dictionary retrieval with better logging
- Consistent fallback logic matching DeepSeek implementation
- Improved error handling and debugging

### Key Files Modified
1. `classes/DeepSeekTranslationService.php` - Fixed name dictionary handling
2. `classes/TranslationService.php` - Enhanced logging and consistency
3. `test_name_dictionary.php` - Test script for verification

## Testing the Integration

### Manual Testing
1. Run `test_name_dictionary.php` in your browser
2. Check that name dictionary entries are loaded
3. Verify translation includes consistent name usage
4. Review debug.log for detailed process logging

### Expected Behavior
- Names with translations use the translation
- Names without translations use romanization
- Names without either use original form
- All chunks of a chapter use consistent names
- Debug logs show name dictionary loading and usage

## Troubleshooting

### Common Issues

1. **No name dictionary entries found**
   - Check if the novel ID exists
   - Verify names have been extracted and stored
   - Run name extraction on existing chapters

2. **Names not being used consistently**
   - Check debug.log for name dictionary loading
   - Verify prompt includes name instructions
   - Ensure DeepSeek API is receiving the context

3. **Translation quality issues**
   - Review name dictionary entries for accuracy
   - Update translations manually if needed
   - Check for conflicting name variations

### Debug Information
The system logs detailed information to `debug.log`:
- Name dictionary retrieval count
- Individual name mappings
- Translation context preparation
- API request details

## Benefits

1. **Consistency**: Character names remain consistent across all chapters
2. **Quality**: Proper name handling improves translation quality
3. **Flexibility**: Supports translation, romanization, or original forms
4. **Scalability**: Works with the existing chunking system
5. **Maintainability**: Easy to update name preferences manually

## Future Enhancements

1. **Automatic Name Learning**: Extract and learn new names during translation
2. **Name Validation**: Detect and flag inconsistent name usage
3. **Bulk Name Management**: Tools for managing names across multiple novels
4. **Name Relationship Mapping**: Track relationships between characters
5. **Cultural Context**: Preserve honorifics and cultural naming conventions

## Configuration

The name dictionary integration is automatically enabled for all translations. No additional configuration is required. The system will:
- Use existing name dictionary entries when available
- Fall back gracefully when no entries exist
- Continue to extract and store new names during translation
- Maintain backward compatibility with existing translations
