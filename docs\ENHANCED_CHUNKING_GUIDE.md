# Enhanced Chapter Chunking System

## Overview

The enhanced chapter chunking system provides intelligent content segmentation to prevent translation timeouts and improve translation quality for long chapters. This system automatically detects when chapters are too long for single translation requests and splits them into optimal chunks while preserving narrative context.

## Key Features

### 1. Intelligent Content Analysis
- **Complexity Detection**: Analyzes dialogue density, kanji density, and scene transitions
- **Timeout Risk Assessment**: Predicts potential timeout issues based on content characteristics
- **Dynamic Sizing**: Adjusts chunk sizes based on content complexity

### 2. Natural Break Point Detection
- **Scene Transitions**: Prioritizes breaks at scene separators (＊＊＊, ---, etc.)
- **Chapter Breaks**: Recognizes chapter/section boundaries
- **Dialogue Transitions**: Splits at natural dialogue boundaries
- **Paragraph Breaks**: Uses paragraph boundaries as fallback split points

### 3. Context Preservation
- **Overlap Management**: Maintains narrative continuity between chunks
- **Character Consistency**: Preserves character name translations across chunks
- **Context Injection**: Provides previous/next chunk context for better translation

### 4. Adaptive Performance Optimization
- **Performance Monitoring**: Tracks translation success rates and timing
- **Automatic Adjustment**: Reduces chunk sizes when timeouts occur
- **Recovery Mechanisms**: Automatically retries failed chunks with smaller sizes

## Configuration

### Default Settings (Optimized for DeepSeek API)
```php
CHUNK_SIZE_LIMIT = 15000        // Reduced from 25000 for better compatibility
CHUNK_OVERLAP_SIZE = 300        // Reduced overlap for efficiency
MIN_CHUNK_SIZE = 5000          // Minimum chunk size
MAX_CHUNK_SIZE = 20000         // Maximum chunk size
CONTEXT_PRESERVATION_SIZE = 200 // Characters preserved for context
```

### User Preferences
The system supports the following configurable preferences:

- `chunk_size_limit`: Base chunk size (5000-20000 characters)
- `chunk_overlap_size`: Overlap between chunks (100-1000 characters)
- `enable_smart_chunking`: Enable intelligent break point detection
- `dynamic_chunk_sizing`: Enable adaptive sizing based on complexity
- `adaptive_timeout_handling`: Enable automatic timeout recovery
- `chunk_recovery_enabled`: Enable failed chunk recovery

## How It Works

### 1. Content Analysis Phase
```
Chapter Content → Complexity Analysis → Risk Assessment → Optimal Chunk Size
```

The system analyzes:
- **Content Length**: Total character count
- **Dialogue Density**: Frequency of dialogue markers
- **Kanji Density**: Complexity of vocabulary
- **Scene Breaks**: Natural division points
- **Estimated Translation Time**: Based on content characteristics

### 2. Intelligent Chunking Process
```
Content → Break Point Detection → Chunk Creation → Context Addition → Database Storage
```

Steps:
1. Identify natural break points (scene transitions, dialogue breaks, paragraphs)
2. Create chunks respecting break points and size limits
3. Add contextual information for translation continuity
4. Store chunks with metadata in database

### 3. Translation Process
```
Chunk → Context Injection → Translation → Error Handling → Recovery (if needed)
```

For each chunk:
1. Inject context from previous/next chunks
2. Add character name consistency information
3. Translate with enhanced prompts
4. Handle errors with automatic recovery
5. Reassemble translated content

## Benefits

### For Users
- **Reduced Timeouts**: Intelligent chunking prevents API timeouts
- **Better Quality**: Context preservation improves translation consistency
- **Automatic Recovery**: Failed translations are automatically retried with smaller chunks
- **Progress Tracking**: Enhanced progress feedback during chunked translations

### For System Performance
- **Optimized API Usage**: Reduces wasted API calls from timeouts
- **Better Resource Management**: Adaptive sizing based on performance
- **Improved Success Rates**: Automatic optimization based on historical data
- **Scalable Processing**: Handles chapters of any length

## Usage

### Automatic Operation
The system works automatically when translating chapters:

1. **Content Detection**: System automatically detects if chunking is needed
2. **Intelligent Splitting**: Content is split using optimal break points
3. **Context-Aware Translation**: Each chunk is translated with full context
4. **Seamless Reassembly**: Translated chunks are reassembled into complete chapter

### Manual Optimization
Use the chunking optimizer tool for performance analysis:

```bash
php utils/chunking_optimizer.php
```

This tool provides:
- Performance analysis
- Optimization recommendations
- Automatic configuration adjustments

### Monitoring
Check chunking statistics through the API:

```javascript
// Get chunking performance data
fetch('/api/chunking.php')
  .then(response => response.json())
  .then(data => {
    console.log('Chunking stats:', data.statistics);
    console.log('Long chapters:', data.long_chapters);
  });
```

## Troubleshooting

### Common Issues

#### High Timeout Rate
**Symptoms**: Many chunks failing with timeout errors
**Solutions**:
- Reduce `chunk_size_limit` to 10000 or lower
- Enable `dynamic_chunk_sizing`
- Check API key and network connectivity

#### Poor Translation Quality
**Symptoms**: Inconsistent character names or narrative flow
**Solutions**:
- Increase `chunk_overlap_size` to 500
- Ensure `context_preservation_size` is at least 200
- Verify name dictionary is properly populated

#### Slow Processing
**Symptoms**: Long delays between chunk translations
**Solutions**:
- Optimize chunk sizes using the optimizer tool
- Check recent error rates and adjust accordingly
- Consider increasing `min_chunk_size` for faster processing

### Performance Optimization

#### For Large Novels
- Use smaller initial chunk sizes (10000-12000 characters)
- Enable all adaptive features
- Monitor performance regularly with optimizer tool

#### For Complex Content (High Dialogue/Technical Terms)
- Reduce chunk sizes further (8000-10000 characters)
- Increase context preservation size
- Use higher overlap for better continuity

## Migration

### From Previous Chunking System
Run the migration script to update preferences:

```bash
php migrations/add_dynamic_chunking_preferences.php
```

This will:
- Add new chunking preferences
- Update existing chunk size limits
- Enable enhanced features

### Existing Chapters
The system will automatically rechunk existing chapters when:
- Translation is attempted on a previously failed chapter
- Manual rechunking is requested via API
- Optimization tool recommends rechunking

## API Reference

### Chunking Management Endpoints

#### GET /api/chunking.php
Returns chunking configuration and statistics

#### POST /api/chunking.php
Updates chunking configuration

#### PUT /api/chunking.php
Forces rechunking of specific chapter

### Chapter Translation with Chunking

#### PUT /api/chapters.php
Translates chapter with automatic chunking if needed

Request body:
```json
{
  "novel_id": 123,
  "chapter_number": 5,
  "target_language": "en"
}
```

Response includes chunking information:
```json
{
  "success": true,
  "data": {
    "chunks_processed": 3,
    "complexity_analysis": {
      "score": 1.2,
      "factors": ["high_dialogue_density"]
    },
    "optimal_chunk_size": 12000
  }
}
```

## Best Practices

1. **Monitor Performance**: Regularly check chunking statistics
2. **Use Optimizer Tool**: Run optimization analysis weekly
3. **Adjust for Content Type**: Different novels may need different settings
4. **Enable All Features**: Use dynamic sizing and adaptive handling
5. **Test with Sample Chapters**: Verify settings with representative content

## Support

For issues or questions about the enhanced chunking system:
1. Check the troubleshooting section above
2. Run the optimizer tool for automated recommendations
3. Review chunking statistics via the API
4. Check debug logs for detailed error information
