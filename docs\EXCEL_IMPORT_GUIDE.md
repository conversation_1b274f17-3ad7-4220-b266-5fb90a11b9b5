# Excel Import Feature Guide

## Overview

The Excel Import feature allows users to bulk import names into the name dictionary from Excel files (.xlsx and .xls formats). This feature provides comprehensive duplicate detection, error handling, and a user-friendly interface for resolving conflicts.

## Features

### ✅ **Excel Import Interface**
- Tabbed interface with Manual Entry and Excel Import options
- Support for .xlsx and .xls file formats
- Template download functionality with sample data
- Progress indicators during file processing

### ✅ **Duplicate Detection Logic**
- Case-sensitive comparison based on "original_name" field
- Checks against existing entries in the database for the current novel
- Identifies duplicates before any data insertion occurs
- Comprehensive duplicate resolution interface

### ✅ **Import Process Flow**
- Parse Excel file and validate data format
- Separate data into valid entries, duplicates, and errors
- Import non-duplicate entries automatically
- Display detailed summary report with statistics

### ✅ **Duplicate Handling Interface**
- Detailed comparison table for each duplicate entry
- Individual action selection (Skip, Update, Keep Both)
- Bulk action options (Skip All, Update All)
- Clear display of existing vs. new entry data

## How to Use

### 1. Access the Excel Import Feature
1. Navigate to any novel's name dictionary management page
2. Click on the "Excel Import" tab in the "Add New Names" section
3. The interface will show file upload and template download options

### 2. Download Template (Recommended)
1. Click "Download Template" to get a sample Excel file
2. The template includes:
   - Proper column headers (Original Name, Romanization, Translation, Name Type)
   - Sample data showing correct format
   - Data validation for Name Type column
   - Styling and formatting guidelines

### 3. Prepare Your Excel File
**Required Columns:**
- **Original Name** (Column A): The name in its original language (required)
- **Romanization** (Column B): Romanized version (optional)
- **Translation** (Column C): English translation (optional)
- **Name Type** (Column D): Must be one of: character, location, organization, country, skill, monster, other (required)

**File Requirements:**
- Format: .xlsx or .xls
- Maximum size: 10MB
- First row must contain exact headers: "Original Name", "Romanization", "Translation", "Name Type"
- Empty rows are automatically skipped

### 4. Import Process
1. Select your prepared Excel file using the file input
2. Click "Import Excel" to start the process
3. The system will:
   - Validate file format and structure
   - Process all data rows
   - Check for duplicates and errors
   - Show progress indicator

### 5. Review Results
**Automatic Import (No Duplicates):**
- If no duplicates are found, valid entries are imported automatically
- Summary shows imported count, errors, and details

**Duplicate Resolution Required:**
- Modal dialog appears showing all duplicates
- For each duplicate, you can choose:
  - **Skip**: Don't import this entry (default)
  - **Update**: Replace existing entry with new data
- Use "Skip All" or "Update All" for bulk actions
- Click "Process Import" to finalize

## Excel File Format

### Column Structure
```
| Original Name | Romanization | Translation | Name Type    |
|---------------|--------------|-------------|--------------|
| 田中太郎      | Tanaka Tarou | Taro Tanaka | character    |
| 東京          | Tokyo        | Tokyo       | location     |
| 魔法学院      | Mahou Gakuin | Magic Academy| organization |
```

### Valid Name Types
- `character` - Character names
- `location` - Place names
- `organization` - Group/organization names
- `country` - Country names
- `skill` - Skill/ability names
- `monster` - Monster/creature names
- `other` - Other types

### Data Validation Rules
- **Original Name**: Required, cannot be empty
- **Romanization**: Optional, can be empty
- **Translation**: Optional, can be empty
- **Name Type**: Required, must be one of the valid types
- **Duplicates**: Checked case-sensitively against existing names

## Error Handling

### Common Errors
1. **Missing Original Name**: Row has empty original name field
2. **Invalid Name Type**: Name type not in valid list
3. **File Format Error**: Incorrect headers or file structure
4. **File Size Error**: File exceeds 10MB limit
5. **File Type Error**: Not .xlsx or .xls format

### Error Display
- Errors are shown in a detailed table with row numbers
- Each error includes specific description and problematic data
- Up to 10 errors shown in interface, with count of additional errors
- Import continues for valid entries even if some rows have errors

## Duplicate Resolution

### Duplicate Detection
- Compares "Original Name" field case-sensitively
- Shows both existing and new entry data side-by-side
- Displays frequency and other metadata for existing entries

### Resolution Options
**Skip (Default):**
- Keeps existing entry unchanged
- Does not import the new entry
- Safest option to avoid data loss

**Update Existing:**
- Replaces existing entry data with new data from Excel
- Updates romanization, translation, and name type
- Preserves frequency and other metadata
- Use when Excel data is more accurate/complete

### Bulk Actions
- **Skip All**: Sets all duplicates to "Skip"
- **Update All**: Sets all duplicates to "Update"
- Individual selections can be changed after bulk actions

## Technical Implementation

### API Endpoints

**Template Download:**
- **URL**: `/api/excel-template.php`
- **Method**: GET
- **Response**: Excel file download

**File Upload and Processing:**
- **URL**: `/api/excel-import.php`
- **Method**: POST
- **Content-Type**: multipart/form-data
- **Parameters**: excel_file (file), novel_id (integer)

**Duplicate Resolution:**
- **URL**: `/api/excel-import.php`
- **Method**: PUT
- **Content-Type**: application/json
- **Parameters**: novel_id, valid_entries, duplicate_actions

### File Processing
- Uses PhpSpreadsheet library for Excel file handling
- Supports both .xlsx and .xls formats
- Validates file structure and data types
- Processes data in memory for performance

### Database Integration
- Uses existing NovelManager and name dictionary infrastructure
- Maintains data integrity with transaction support
- Follows established naming conventions and constraints
- Immediate availability in translation system

## Best Practices

### Preparing Excel Files
1. **Use the Template**: Always start with the downloaded template
2. **Consistent Formatting**: Keep data format consistent throughout
3. **Validate Data**: Check name types and required fields before import
4. **Test Small Batches**: Import small batches first to verify format

### Managing Duplicates
1. **Review Carefully**: Check existing vs. new data before deciding
2. **Preserve Frequency**: Consider existing frequency when updating
3. **Backup First**: Consider exporting existing data before large updates
4. **Document Changes**: Keep track of what was updated

### Error Prevention
1. **Check Headers**: Ensure exact header names match template
2. **Validate Types**: Use only valid name types
3. **Clean Data**: Remove empty rows and invalid characters
4. **File Size**: Keep files under 10MB for best performance

## Troubleshooting

### Import Fails
1. **Check File Format**: Ensure .xlsx or .xls format
2. **Verify Headers**: Must match exactly: "Original Name", "Romanization", "Translation", "Name Type"
3. **Check File Size**: Must be under 10MB
4. **Browser Issues**: Try refreshing page or different browser

### Duplicates Not Detected
1. **Case Sensitivity**: Check exact capitalization and spacing
2. **Character Encoding**: Ensure proper Unicode encoding
3. **Hidden Characters**: Remove any hidden or special characters

### Performance Issues
1. **File Size**: Reduce file size by splitting into smaller batches
2. **Browser Memory**: Close other tabs and applications
3. **Network**: Ensure stable internet connection

## Future Enhancements

### Planned Features
- CSV import support
- Advanced duplicate resolution options
- Batch processing for very large files
- Import history and rollback functionality
- Integration with external name databases

---

## Summary

The Excel Import feature provides a comprehensive solution for bulk importing names into the novel translation system. It combines robust file processing with intelligent duplicate detection and user-friendly resolution interfaces, ensuring data integrity while maximizing import efficiency. The feature follows established user preferences for simple, database-like interfaces and integrates seamlessly with the existing name dictionary system.
