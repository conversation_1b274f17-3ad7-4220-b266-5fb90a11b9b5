<?php
/**
 * Simple Word Export Service
 * Ultra-minimal approach for maximum compatibility
 */

use PhpOffice\PhpWord\PhpWord;
use PhpOffice\PhpWord\IOFactory;

class SimpleWordExportService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Export a chapter to Word document - simple approach
     */
    public function exportChapter(int $novelId, int $chapterNumber): array {
        try {
            // Get novel information
            $novel = $this->db->fetchOne(
                "SELECT * FROM novels WHERE id = ?",
                [$novelId]
            );
            
            if (!$novel) {
                throw new Exception("Novel not found");
            }
            
            // Get chapter information
            $chapter = $this->db->fetchOne(
                "SELECT * FROM chapters WHERE novel_id = ? AND chapter_number = ?",
                [$novelId, $chapterNumber]
            );
            
            if (!$chapter) {
                throw new Exception("Chapter not found");
            }
            
            if (!$chapter['translated_content']) {
                throw new Exception("Chapter has no translated content to export");
            }
            
            // Generate the Word document
            $filename = $this->generateSimpleWordDocument($novel, $chapter);
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filename
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Generate Word document using minimal approach
     */
    private function generateSimpleWordDocument(array $novel, array $chapter): string {
        try {
            // Create new PHPWord instance
            $phpWord = new PhpWord();
            
            // Set basic document properties
            $properties = $phpWord->getDocInfo();
            $properties->setCreator('Novel Translator');
            $properties->setTitle($this->getDocumentTitle($novel, $chapter));
            
            // Add section
            $section = $phpWord->addSection();
            
            // Add novel title (original + translated format)
            $novelTitleLine = '';
            if ($novel['translated_title'] && $novel['original_title']) {
                // Both titles available - show translated first, then original in parentheses
                $cleanTranslatedTitle = $this->cleanText($novel['translated_title']);
                $cleanOriginalTitle = $this->cleanText($novel['original_title']);
                if ($cleanTranslatedTitle !== $cleanOriginalTitle) {
                    $novelTitleLine = $cleanTranslatedTitle . ' (' . $cleanOriginalTitle . ')';
                } else {
                    $novelTitleLine = $cleanTranslatedTitle;
                }
            } elseif ($novel['translated_title']) {
                $novelTitleLine = $this->cleanText($novel['translated_title']);
            } else {
                $novelTitleLine = $this->cleanText($novel['original_title']);
            }

            $section->addText($novelTitleLine, [
                'name' => 'Arial',
                'size' => 18,
                'bold' => true
            ], [
                'alignment' => 'center',
                'spaceAfter' => 300
            ]);

            // Add chapter number and title (original + translated format)
            $chapterTitleLine = 'Chapter ' . $chapter['chapter_number'];

            // Add chapter titles if available
            if ($chapter['translated_title'] && $chapter['original_title']) {
                // Both titles available - show translated first, then original in parentheses
                $cleanTranslatedChapterTitle = $this->cleanText($chapter['translated_title']);
                $cleanOriginalChapterTitle = $this->cleanText($chapter['original_title']);
                if ($cleanTranslatedChapterTitle !== $cleanOriginalChapterTitle) {
                    $chapterTitleLine .= ': ' . $cleanTranslatedChapterTitle . ' (' . $cleanOriginalChapterTitle . ')';
                } else {
                    $chapterTitleLine .= ': ' . $cleanTranslatedChapterTitle;
                }
            } elseif ($chapter['translated_title']) {
                $cleanTranslatedChapterTitle = $this->cleanText($chapter['translated_title']);
                $chapterTitleLine .= ': ' . $cleanTranslatedChapterTitle;
            } elseif ($chapter['original_title']) {
                $cleanOriginalChapterTitle = $this->cleanText($chapter['original_title']);
                $chapterTitleLine .= ': ' . $cleanOriginalChapterTitle;
            }

            $section->addText($chapterTitleLine, [
                'name' => 'Arial',
                'size' => 14,
                'bold' => true
            ], [
                'alignment' => 'center',
                'spaceAfter' => 400
            ]);
            
            // Add content
            $this->addSimpleContent($section, $chapter['translated_content']);
            
            // Generate filename and save
            $filename = $this->generateFilename($novel, $chapter);
            $filepath = APP_ROOT . '/temp/' . $filename;
            
            // Ensure temp directory exists
            $tempDir = dirname($filepath);
            if (!is_dir($tempDir)) {
                mkdir($tempDir, 0755, true);
            }
            
            // Save document
            $writer = IOFactory::createWriter($phpWord, 'Word2007');
            $writer->save($filepath);
            
            // Verify file was created
            if (!file_exists($filepath) || filesize($filepath) === 0) {
                throw new Exception("Failed to create Word document");
            }
            
            return $filepath;
            
        } catch (Exception $e) {
            throw new Exception("Error generating Word document: " . $e->getMessage());
        }
    }
    
    /**
     * Add content with ultra-simple approach
     */
    private function addSimpleContent($section, string $content): void {
        // Limit content length
        if (strlen($content) > 30000) {
            $content = substr($content, 0, 30000) . "\n\n[Content truncated for compatibility]";
        }
        
        // Split into paragraphs
        $paragraphs = explode("\n\n", $content);

        foreach ($paragraphs as $paragraph) {
            $paragraph = trim($paragraph);

            if (empty($paragraph)) {
                continue;
            }

            // Skip chunk markers
            if (strpos($paragraph, '<!-- CHUNK_BOUNDARY:') === 0) {
                continue;
            }

            // Clean text
            $cleanParagraph = $this->cleanText($paragraph);

            if (!empty($cleanParagraph)) {
                // Handle line breaks within paragraphs
                $this->addParagraphWithLineBreaks($section, $cleanParagraph);
            }
        }
    }

    /**
     * Add paragraph with proper line break handling
     */
    private function addParagraphWithLineBreaks($section, string $text): void {
        $lines = explode("\n", $text);

        if (count($lines) === 1) {
            // Single line - add as simple paragraph
            $section->addText($text, [
                'name' => 'Times New Roman',
                'size' => 12
            ], [
                'spaceAfter' => 120
            ]);
        } else {
            // Multiple lines - preserve line breaks
            $textRun = $section->addTextRun([
                'spaceAfter' => 120
            ]);

            foreach ($lines as $lineIndex => $line) {
                $line = trim($line);

                if ($lineIndex > 0 && !empty($line)) {
                    $textRun->addTextBreak();
                }

                if (!empty($line)) {
                    $textRun->addText($line, [
                        'name' => 'Times New Roman',
                        'size' => 12
                    ]);
                }
            }
        }
    }
    
    /**
     * Ultra-safe text cleaning with better formatting preservation
     */
    private function cleanText(string $text): string {
        // Convert to UTF-8 if needed
        if (!mb_check_encoding($text, 'UTF-8')) {
            $text = mb_convert_encoding($text, 'UTF-8', 'auto');
        }

        // Normalize line endings
        $text = str_replace(["\r\n", "\r"], "\n", $text);

        // Decode HTML entities first to restore proper characters
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Remove control characters but preserve formatting
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Remove HTML tags if any
        $text = strip_tags($text);

        // Very conservative character removal - only remove truly problematic ones
        // Preserve quotation marks and other formatting characters
        $text = str_replace(['<', '>'], ['', ''], $text);

        // Clean up excessive whitespace but preserve structure
        $text = preg_replace('/[ \t]{3,}/', '  ', $text);
        $text = preg_replace('/\n{4,}/', "\n\n\n", $text);

        // Trim only leading/trailing whitespace, preserve internal structure
        return trim($text);
    }
    
    /**
     * Generate document title
     */
    private function getDocumentTitle(array $novel, array $chapter): string {
        $title = $novel['translated_title'] ?: $novel['original_title'];
        return $title . ' - Chapter ' . $chapter['chapter_number'];
    }
    
    /**
     * Generate filename for the document
     */
    private function generateFilename(array $novel, array $chapter): string {
        $novelTitle = $novel['translated_title'] ?: $novel['original_title'];

        // Clean title for filename
        $cleanTitle = preg_replace('/[^\w\s\-_]/', '', $novelTitle);
        $cleanTitle = preg_replace('/\s+/', '_', $cleanTitle);
        $cleanTitle = trim($cleanTitle, '_');

        // Limit length
        if (strlen($cleanTitle) > 50) {
            $cleanTitle = substr($cleanTitle, 0, 50);
        }

        // Format: [NovelTitle]_Chapter[Number].docx (Simple version)
        return '[' . $cleanTitle . ']_Chapter' . $chapter['chapter_number'] . '_Simple.docx';
    }
    
    /**
     * Clean up temporary files
     */
    public function cleanupTempFiles(): void {
        $tempDir = APP_ROOT . '/temp/';
        if (is_dir($tempDir)) {
            $files = glob($tempDir . 'Simple_*.docx');
            $cutoff = time() - (24 * 60 * 60);
            
            foreach ($files as $file) {
                if (filemtime($file) < $cutoff) {
                    unlink($file);
                }
            }
        }
    }
}
