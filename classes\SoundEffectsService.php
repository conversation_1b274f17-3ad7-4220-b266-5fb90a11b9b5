<?php
/**
 * Sound Effects Service for Novel Translation Application
 * Handles detection and preservation of onomatopoeia/sound effects
 */

class SoundEffectsService {
    private $translationService;
    
    // Sound effect marker for preservation during translation
    const SOUND_EFFECT_MARKER_START = '【SOUND_EFFECT:';
    const SOUND_EFFECT_MARKER_END = '】';
    
    public function __construct() {
        // We'll inject TranslationService when needed to avoid circular dependency
        $this->translationService = null;
    }
    
    /**
     * Detect and mark sound effects in text for preservation during translation
     */
    public function preprocessTextForTranslation(string $text): array {
        $soundEffects = [];
        $integratedEffects = [];
        $processedText = $text;

        // Detect sound effects using multiple methods
        $detectedEffects = $this->detectSoundEffects($text);

        // Process each sound effect based on its integration type
        foreach ($detectedEffects as $index => $effect) {
            // Convert byte position to character position for proper multibyte string handling
            $charPosition = mb_strlen(substr($text, 0, $effect['position']));
            $context = $this->analyzeContextualPosition($text, $charPosition, $effect['original']);

            if ($context['integration_type'] === 'integrated') {
                // For integrated effects, create a special marker for translation
                $markerId = 'IE_' . $index; // IE = Integrated Effect
                $marker = self::SOUND_EFFECT_MARKER_START . $markerId . self::SOUND_EFFECT_MARKER_END;

                $integratedEffects[$markerId] = [
                    'original' => $effect['original'],
                    'romanized' => $effect['romanized'],
                    'position' => $effect['position'],
                    'context' => $context,
                    'type' => 'integrated'
                ];

                // Replace in text with integrated marker
                $processedText = str_replace($effect['original'], $marker, $processedText);
            } else {
                // For standalone effects, use the original preservation system
                $markerId = 'SE_' . $index;
                $marker = self::SOUND_EFFECT_MARKER_START . $markerId . self::SOUND_EFFECT_MARKER_END;

                $soundEffects[$markerId] = [
                    'original' => $effect['original'],
                    'romanized' => $effect['romanized'],
                    'position' => $effect['position'],
                    'context' => $context,
                    'type' => 'standalone'
                ];

                // Replace in text with standalone marker
                $processedText = str_replace($effect['original'], $marker, $processedText);
            }
        }

        return [
            'text' => $processedText,
            'sound_effects' => $soundEffects,
            'integrated_effects' => $integratedEffects
        ];
    }
    
    /**
     * Restore romanized sound effects in translated text with context awareness
     */
    public function postprocessTranslatedText(string $translatedText, array $soundEffects, array $integratedEffects = []): string {
        $restoredText = $translatedText;

        // Process standalone sound effects (preserve as romanized)
        if (!empty($soundEffects)) {
            // Sort sound effects by position to process them in order
            $sortedEffects = $soundEffects;
            uasort($sortedEffects, function($a, $b) {
                return ($a['position'] ?? 0) <=> ($b['position'] ?? 0);
            });

            // Replace each marker with the romanized sound effect
            foreach ($sortedEffects as $markerId => $effect) {
                $marker = self::SOUND_EFFECT_MARKER_START . $markerId . self::SOUND_EFFECT_MARKER_END;
                $romanizedEffect = $this->formatRomanizedSoundEffect($effect);

                $restoredText = str_replace($marker, $romanizedEffect, $restoredText);

                // Also check for AI-converted markers (Unicode brackets converted to ASCII)
                $asciiMarker = '[SOUND_EFFECT:' . $markerId . ']';
                $restoredText = str_replace($asciiMarker, $romanizedEffect, $restoredText);
            }
        }

        // Process integrated effects (should already be translated by AI, just clean up any remaining markers)
        if (!empty($integratedEffects)) {
            foreach ($integratedEffects as $markerId => $effect) {
                $marker = self::SOUND_EFFECT_MARKER_START . $markerId . self::SOUND_EFFECT_MARKER_END;
                $asciiMarker = '[SOUND_EFFECT:' . $markerId . ']';

                // If markers still exist, it means AI didn't translate them properly
                // In this case, fall back to descriptive translation
                if (strpos($restoredText, $marker) !== false || strpos($restoredText, $asciiMarker) !== false) {
                    $descriptiveTranslation = $this->getDescriptiveTranslation($effect['original'], $effect['romanized']);
                    $restoredText = str_replace($marker, $descriptiveTranslation, $restoredText);
                    $restoredText = str_replace($asciiMarker, $descriptiveTranslation, $restoredText);
                }
            }
        }

        // Clean up any remaining formatting issues
        $restoredText = $this->cleanupRestoredText($restoredText);

        return $restoredText;
    }
    
    /**
     * Detect sound effects in text using pattern matching and AI assistance
     */
    public function detectSoundEffects(string $text): array {
        $soundEffects = [];
        
        // Method 1: Pattern-based detection for common Japanese onomatopoeia
        $patternEffects = $this->detectSoundEffectsWithPatterns($text);
        $soundEffects = array_merge($soundEffects, $patternEffects);
        
        // Method 2: AI-assisted detection for complex cases
        $aiEffects = $this->detectSoundEffectsWithAI($text);
        $soundEffects = array_merge($soundEffects, $aiEffects);
        
        // Remove duplicates and sort by position
        $soundEffects = $this->deduplicateSoundEffects($soundEffects);
        
        return $soundEffects;
    }
    
    /**
     * Detect sound effects using regex patterns
     */
    private function detectSoundEffectsWithPatterns(string $text): array {
        $effects = [];

        // More precise Japanese onomatopoeia patterns
        $patterns = [
            // Sound effects in quotes (most reliable indicator)
            '/「([\p{Katakana}\p{Hiragana}ー・ッっ！？]+)」/u',

            // Sound effects in parentheses
            '/[（(]([\p{Katakana}\p{Hiragana}ー・ッっ！？\s]+)[）)]/u',

            // Specific common sound effect patterns (expanded list) - Katakana
            '/(?:ガーン|ズドドド|キャー|ドーン|シーン|ドキドキ|バタン|ガタン|ガタガタ|ドンドン|バンバン|ガンガン|ズンズン|ドカドカ|バキバキ|ガシガシ|ズバズバ|ビシビシ|パシパシ|ダンダン|ボンボン|ポンポン|キンキン|ギンギン|チンチン|リンリン|ピンピン|ビンビン|ジンジン|シンシン|カンカン|タンタン|ファンファン|パンパン|マンマン|ランラン|サンサン|ザンザン|ザーザー|ナンナン|ハンハン|ヤンヤン|ワンワン|フワフワ)(?:ー|ッ|っ|！)*(?:！！！|！！|！)*/u',

            // Specific common sound effect patterns - Hiragana
            '/(?:ねちねち|ふらふら|どきどき|わくわく|いらいら|ざらざら|つるつる|ぺたぺた|ぺらぺら|しとしと|ざーざー|ぐいぐい|じりじり|よろよろ|すたすた|ぺちゃぺちゃ)(?:ー|っ|ッ|！)*(?:！！！|！！|！)*/u',

            // Specific pattern for emphasized sound effects at sentence start
            '/^(ガーン！！！|ガーン！！|ガーン！)/u',

            // Katakana with elongation marks (conservative)
            '/[\p{Katakana}]{2,}(?:ー|ッ){2,}/u',

            // Exclamatory sound patterns (conservative)
            '/(?:あ|う|お|え|い){3,}(?:ー|っ|ッ|！)*/u',

            // Standalone katakana sound effects (more aggressive detection)
            '/(?:^|[\s。、！？])([\p{Katakana}]{2,}(?:ー|ッ|っ|！)*)(?=[\s。、！？]|$)/u',

            // Sound effects with multiple exclamation marks (at start or after punctuation)
            '/(?:^|[\s。、])([\p{Katakana}]{2,}(?:！！！|！！|！)+)/u',
        ];

        foreach ($patterns as $pattern) {
            if (preg_match_all($pattern, $text, $matches, PREG_OFFSET_CAPTURE)) {
                foreach ($matches[0] as $match) {
                    $original = $match[0];
                    $position = $match[1];

                    // Extract content from quotes/parentheses if needed
                    $content = $original;
                    if (preg_match('/^[「（(](.*)[」）)]$/u', $original, $contentMatch)) {
                        $content = $contentMatch[1];
                    }

                    // Handle sound effects with exclamation marks
                    if (preg_match('/^([\p{Katakana}]+)(?:！！！|！！|！)+$/u', $original, $exclamationMatch)) {
                        $content = $exclamationMatch[1];
                    }

                    // For quoted content, be more lenient as quotes often indicate sound effects
                    $isQuoted = preg_match('/^[「（(].*[」）)]$/', $original);

                    // Filter out common words that aren't sound effects
                    if ($this->isSoundEffect($content, $isQuoted)) {
                        $romanized = $this->romanizeSoundEffect($content);

                        $effects[] = [
                            'original' => $original,
                            'romanized' => $romanized,
                            'position' => $position,
                            'method' => 'pattern'
                        ];
                    }
                }
            }
        }

        return $effects;
    }
    
    /**
     * Detect sound effects using AI assistance
     */
    private function detectSoundEffectsWithAI(string $text): array {
        // Only use AI detection for longer texts to avoid unnecessary API calls
        if (strlen($text) < 100) {
            return [];
        }
        
        try {
            if (!$this->translationService) {
                $this->translationService = new TranslationService();
            }
            
            $prompt = $this->buildSoundEffectDetectionPrompt($text);
            $response = $this->translationService->makeApiRequest($prompt);
            
            if ($response['success']) {
                $aiResponse = $this->translationService->extractTranslationFromResponse($response['data']);
                return $this->parseAISoundEffectResponse($aiResponse, $text);
            }
        } catch (Exception $e) {
            // Fallback to pattern-only detection if AI fails
            error_log("SoundEffectsService: AI detection failed: " . $e->getMessage());
        }
        
        return [];
    }
    
    /**
     * Check if a detected pattern is actually a sound effect
     */
    private function isSoundEffect(string $text, bool $isQuoted = false): bool {
        $text = trim($text);

        // Skip if too short or too long
        if (mb_strlen($text) < 2 || mb_strlen($text) > 15) {
            return false;
        }

        // Skip common words that aren't sound effects
        $excludeWords = [
            'です', 'ます', 'した', 'する', 'ある', 'いる', 'なる', 'くる', 'いく',
            'もの', 'こと', 'とき', 'ところ', 'ひと', 'みんな', 'だれ', 'なに',
            'どこ', 'いつ', 'どう', 'なぜ', 'どの', 'この', 'その', 'あの',
            'カメラ', 'テレビ', 'ラジオ', 'コンピュータ', 'インターネット',
            'ドア', 'ドアが', 'タン', 'ドアが閉', 'バタン', '。「バタン'
        ];

        if (in_array($text, $excludeWords)) {
            return false;
        }

        // Skip if it contains common non-sound-effect patterns (unless quoted)
        // Use word boundaries to avoid matching parts of sound effects
        if (!$isQuoted && preg_match('/(?:^|[^ァ-ヶ])(が|を|に|は|で|と|の|から|まで|より|へ)(?:[^ァ-ヶ]|$)/', $text)) {
            return false;
        }

        // Check for sound effect characteristics
        // 1. Known sound effect patterns (most reliable)
        $knownSoundEffects = [
            // Katakana onomatopoeia
            'ドキドキ', 'バタン', 'ガタン', 'ドンドン', 'バンバン', 'ガンガン',
            'ズンズン', 'ドカドカ', 'バキバキ', 'ガシガシ', 'ズバズバ', 'ビシビシ',
            'パシパシ', 'ダンダン', 'ボンボン', 'ポンポン', 'キンキン', 'ギンギン',
            'チンチン', 'リンリン', 'ピンピン', 'ビンビン', 'ジンジン', 'シンシン',
            'カンカン', 'タンタン', 'ファンファン', 'パンパン', 'ワンワン',
            'ガーン', 'ズドドド', 'キャー', 'ドーン', 'シーン',
            // Hiragana onomatopoeia
            'ねちねち', 'ふらふら', 'どきどき', 'わくわく', 'いらいら', 'ざらざら',
            'つるつる', 'ぺたぺた', 'ぺらぺら', 'しとしと', 'ざーざー', 'ぐいぐい',
            'じりじり', 'よろよろ', 'すたすた', 'ぺちゃぺちゃ'
        ];

        if (in_array($text, $knownSoundEffects)) {
            return true;
        }

        // 2. If quoted, be more lenient with katakana patterns
        if ($isQuoted && preg_match('/^[\p{Katakana}ー・ッっ！？]+$/u', $text)) {
            return true;
        }

        // 3. Contains repetition marks or elongation
        if (preg_match('/[ー々ッっ]{2,}/', $text)) {
            return true;
        }

        // 4. Repetitive katakana patterns
        if (preg_match('/^([\p{Katakana}]{1,3})\1+$/u', $text)) {
            return true;
        }

        // 5. Exclamatory patterns (3+ repetitions)
        if (preg_match('/^(?:あ|う|お|え|い){3,}(?:ー|っ|ッ)*$/u', $text)) {
            return true;
        }

        return false;
    }
    
    /**
     * Romanize a sound effect
     */
    private function romanizeSoundEffect(string $soundEffect): string {
        // Remove quotes and parentheses if present
        $cleaned = str_replace(['「', '」', '（', '）', '(', ')'], '', $soundEffect);

        // Handle multiple exclamation marks specially
        $hasExclamation = false;
        if (preg_match('/！！！|！！|！/', $cleaned)) {
            $hasExclamation = true;
        }

        // Remove only specific punctuation marks, preserve elongation marks (ー)
        $cleaned = str_replace(['！', '？', '!', '?', '。', '、'], '', $cleaned);
        $cleaned = trim($cleaned);

        // Remove any null bytes that might have been introduced
        $cleaned = str_replace("\0", '', $cleaned);

        // Comprehensive katakana to romaji mapping for sound effects
        $mappings = [
            // Common sound effects
            'ドキドキ' => 'dokidoki', 'バタン' => 'batan', 'ガタン' => 'gatan',
            'ドン' => 'don', 'バン' => 'ban', 'ガン' => 'gan', 'ズン' => 'zun',
            'ドカ' => 'doka', 'バキ' => 'baki', 'ガシ' => 'gashi', 'ズバ' => 'zuba',
            'ビシ' => 'bishi', 'パシ' => 'pashi', 'ダン' => 'dan', 'ボン' => 'bon',
            'ポン' => 'pon', 'キン' => 'kin', 'ギン' => 'gin', 'チン' => 'chin',
            'リン' => 'rin', 'ピン' => 'pin', 'ビン' => 'bin', 'ジン' => 'jin',
            'シン' => 'shin', 'カン' => 'kan', 'タン' => 'tan', 'ファン' => 'fan',
            'パン' => 'pan', 'マン' => 'man', 'ラン' => 'ran', 'サン' => 'san',
            'ザン' => 'zan', 'ナン' => 'nan', 'ハン' => 'han', 'ヤン' => 'yan',
            'ワン' => 'wan', 'ドンドン' => 'dondon', 'バンバン' => 'banban',
            'ガンガン' => 'gangan', 'ズンズン' => 'zunzun', 'ワンワン' => 'wanwan',

            // Missing sound effects from test cases
            'ガーン' => 'gaan', 'ズドドド' => 'zudododo', 'キャー' => 'kyaa',
            'ドーン' => 'doon', 'シーン' => 'shiin',

            // Variations with exclamation marks
            'ガーン！' => 'gaan', 'ガーン！！' => 'gaan', 'ガーン！！！' => 'gaan',
            'キャー！' => 'kyaa', 'バタン！' => 'batan', 'ドーン！' => 'doon',

            // Additional common onomatopoeia
            'ガタガタ' => 'gatagata', 'ザーザー' => 'zaazaa', 'ゴロゴロ' => 'gorogoro',
            'パタパタ' => 'patapatata', 'ペラペラ' => 'perapera', 'ヒラヒラ' => 'hirahira',
            'フワフワ' => 'fuwafuwa', 'サラサラ' => 'sarasara', 'ツルツル' => 'tsurutsuru',
            'ザワザワ' => 'zawazawa', 'ワイワイ' => 'waiwai', 'ガヤガヤ' => 'gayagaya',

            // Hiragana onomatopoeia
            'ねちねち' => 'nechinechi', 'ふらふら' => 'furafura', 'どきどき' => 'dokidoki',
            'わくわく' => 'wakuwaku', 'いらいら' => 'iraira', 'ざらざら' => 'zarazara',
            'つるつる' => 'tsurutsuru', 'ぺたぺた' => 'petapeta', 'ぺらぺら' => 'perapera',
            'しとしと' => 'shitoshito', 'ざーざー' => 'zaazaa', 'ぐいぐい' => 'guigui',
            'じりじり' => 'jirijiri', 'よろよろ' => 'yoroyoro', 'すたすた' => 'sutasuta',
            'ぺちゃぺちゃ' => 'pechapeche',

            // Hiragana common patterns
            'わかり' => 'wakari', 'そうそう' => 'sousou', 'はいはい' => 'haihai',
            'えーっ' => 'eeh', 'うーん' => 'uun', 'あーあ' => 'aaa',
            'おーい' => 'ooi', 'やれやれ' => 'yareyare'
        ];

        // Try direct mapping first (exact match)
        if (isset($mappings[$cleaned])) {
            $result = $mappings[$cleaned];
            // Clean the result to ensure no null bytes or invisible characters
            $result = preg_replace('/[\x00-\x1F\x7F]/', '', $result);
            return trim($result);
        }

        // Try mapping without quotes for quoted sound effects
        $cleanedNoQuotes = str_replace(['「', '」'], '', $cleaned);
        if (isset($mappings[$cleanedNoQuotes])) {
            $result = $mappings[$cleanedNoQuotes];
            // Clean the result to ensure no null bytes or invisible characters
            $result = preg_replace('/[\x00-\x1F\x7F]/', '', $result);
            return trim($result);
        }

        // Try romanization service for complex cases
        try {
            if (!$this->translationService) {
                $this->translationService = new TranslationService();
            }

            $result = $this->translationService->romanizeText($cleaned);
            if ($result['success'] && !empty($result['romanized_text'])) {
                $romanized = strtolower(trim($result['romanized_text']));
                // Clean the result to ensure no null bytes or invisible characters
                $romanized = preg_replace('/[\x00-\x1F\x7F]/', '', $romanized);
                // Ensure we got actual romanization, not the original text
                if ($romanized !== $cleaned && preg_match('/^[a-z\s\-]+$/i', $romanized)) {
                    return trim($romanized);
                }
            }
        } catch (Exception $e) {
            error_log("SoundEffectsService: Romanization failed for '$cleaned': " . $e->getMessage());
        }

        // Manual romanization for basic katakana/hiragana patterns
        $manualRomanized = $this->manualRomanize($cleaned);
        if ($manualRomanized !== $cleaned) {
            // Clean the result to ensure no null bytes or invisible characters
            $manualRomanized = preg_replace('/[\x00-\x1F\x7F]/', '', $manualRomanized);
            return trim($manualRomanized);
        }

        // Final fallback: if we can't romanize, return a placeholder
        // This prevents Japanese characters from appearing in the final output
        return '[' . $cleaned . ']';
    }

    /**
     * Manual romanization for basic Japanese characters
     */
    private function manualRomanize(string $text): string {
        // Basic katakana mapping
        $katakanaMap = [
            'ア' => 'a', 'イ' => 'i', 'ウ' => 'u', 'エ' => 'e', 'オ' => 'o',
            'カ' => 'ka', 'キ' => 'ki', 'ク' => 'ku', 'ケ' => 'ke', 'コ' => 'ko',
            'ガ' => 'ga', 'ギ' => 'gi', 'グ' => 'gu', 'ゲ' => 'ge', 'ゴ' => 'go',
            'サ' => 'sa', 'シ' => 'shi', 'ス' => 'su', 'セ' => 'se', 'ソ' => 'so',
            'ザ' => 'za', 'ジ' => 'ji', 'ズ' => 'zu', 'ゼ' => 'ze', 'ゾ' => 'zo',
            'タ' => 'ta', 'チ' => 'chi', 'ツ' => 'tsu', 'テ' => 'te', 'ト' => 'to',
            'ダ' => 'da', 'ヂ' => 'ji', 'ヅ' => 'zu', 'デ' => 'de', 'ド' => 'do',
            'ナ' => 'na', 'ニ' => 'ni', 'ヌ' => 'nu', 'ネ' => 'ne', 'ノ' => 'no',
            'ハ' => 'ha', 'ヒ' => 'hi', 'フ' => 'fu', 'ヘ' => 'he', 'ホ' => 'ho',
            'バ' => 'ba', 'ビ' => 'bi', 'ブ' => 'bu', 'ベ' => 'be', 'ボ' => 'bo',
            'パ' => 'pa', 'ピ' => 'pi', 'プ' => 'pu', 'ペ' => 'pe', 'ポ' => 'po',
            'マ' => 'ma', 'ミ' => 'mi', 'ム' => 'mu', 'メ' => 'me', 'モ' => 'mo',
            'ヤ' => 'ya', 'ユ' => 'yu', 'ヨ' => 'yo',
            'ラ' => 'ra', 'リ' => 'ri', 'ル' => 'ru', 'レ' => 're', 'ロ' => 'ro',
            'ワ' => 'wa', 'ヲ' => 'wo', 'ン' => 'n',
            'ー' => '', 'ッ' => '', 'っ' => ''
        ];

        // Basic hiragana mapping
        $hiraganaMap = [
            'あ' => 'a', 'い' => 'i', 'う' => 'u', 'え' => 'e', 'お' => 'o',
            'か' => 'ka', 'き' => 'ki', 'く' => 'ku', 'け' => 'ke', 'こ' => 'ko',
            'が' => 'ga', 'ぎ' => 'gi', 'ぐ' => 'gu', 'げ' => 'ge', 'ご' => 'go',
            'さ' => 'sa', 'し' => 'shi', 'す' => 'su', 'せ' => 'se', 'そ' => 'so',
            'ざ' => 'za', 'じ' => 'ji', 'ず' => 'zu', 'ぜ' => 'ze', 'ぞ' => 'zo',
            'た' => 'ta', 'ち' => 'chi', 'つ' => 'tsu', 'て' => 'te', 'と' => 'to',
            'だ' => 'da', 'ぢ' => 'ji', 'づ' => 'zu', 'で' => 'de', 'ど' => 'do',
            'な' => 'na', 'に' => 'ni', 'ぬ' => 'nu', 'ね' => 'ne', 'の' => 'no',
            'は' => 'ha', 'ひ' => 'hi', 'ふ' => 'fu', 'へ' => 'he', 'ほ' => 'ho',
            'ば' => 'ba', 'び' => 'bi', 'ぶ' => 'bu', 'べ' => 'be', 'ぼ' => 'bo',
            'ぱ' => 'pa', 'ぴ' => 'pi', 'ぷ' => 'pu', 'ぺ' => 'pe', 'ぽ' => 'po',
            'ま' => 'ma', 'み' => 'mi', 'む' => 'mu', 'め' => 'me', 'も' => 'mo',
            'や' => 'ya', 'ゆ' => 'yu', 'よ' => 'yo',
            'ら' => 'ra', 'り' => 'ri', 'る' => 'ru', 'れ' => 're', 'ろ' => 'ro',
            'わ' => 'wa', 'を' => 'wo', 'ん' => 'n'
        ];

        $result = '';
        $length = mb_strlen($text);
        $lastVowel = '';

        for ($i = 0; $i < $length; $i++) {
            $char = mb_substr($text, $i, 1);

            if ($char === 'ー') {
                // Elongation mark - repeat the last vowel
                if ($lastVowel) {
                    $result .= $lastVowel;
                }
            } elseif (isset($katakanaMap[$char])) {
                $romanized = $katakanaMap[$char];
                $result .= $romanized;
                // Remember the last vowel for elongation marks
                if (preg_match('/[aiueo]$/', $romanized)) {
                    $lastVowel = substr($romanized, -1);
                }
            } elseif (isset($hiraganaMap[$char])) {
                $romanized = $hiraganaMap[$char];
                $result .= $romanized;
                // Remember the last vowel for elongation marks
                if (preg_match('/[aiueo]$/', $romanized)) {
                    $lastVowel = substr($romanized, -1);
                }
            } else {
                // Only keep ASCII characters, skip Japanese characters
                if (preg_match('/^[a-zA-Z0-9\s\-]$/', $char)) {
                    $result .= $char;
                }
            }
        }

        // Clean the result to ensure no null bytes or invisible characters
        $result = preg_replace('/[\x00-\x1F\x7F]/', '', $result);
        return trim($result);
    }

    /**
     * Build AI prompt for sound effect detection
     */
    private function buildSoundEffectDetectionPrompt(string $text): string {
        return "Identify all sound effects (onomatopoeia) in the following Japanese text. " .
               "Sound effects are words that represent sounds, often written in katakana, " .
               "and include things like impact sounds (ドン, バン), movement sounds (ザッ, シュッ), " .
               "emotional sounds (ガーン, ショック), and environmental sounds (ザーザー, ゴロゴロ). " .
               "Return only the sound effects found, one per line, in the exact form they appear in the text. " .
               "If no sound effects are found, return 'NONE'.\n\n" .
               "Text: " . $text;
    }

    /**
     * Parse AI response for sound effect detection
     */
    private function parseAISoundEffectResponse(string $response, string $originalText): array {
        $effects = [];

        if (trim(strtoupper($response)) === 'NONE') {
            return $effects;
        }

        $lines = explode("\n", $response);
        foreach ($lines as $line) {
            $line = trim($line);
            if (empty($line)) continue;

            // Remove any formatting or numbering
            $line = preg_replace('/^\d+\.\s*/', '', $line);
            $line = preg_replace('/^[\*\-]\s*/', '', $line);

            // Find position in original text
            $position = strpos($originalText, $line);
            if ($position !== false) {
                $romanized = $this->romanizeSoundEffect($line);

                $effects[] = [
                    'original' => $line,
                    'romanized' => $romanized,
                    'position' => $position,
                    'method' => 'ai'
                ];
            }
        }

        return $effects;
    }

    /**
     * Remove duplicate sound effects and sort by position, preferring longer matches
     */
    private function deduplicateSoundEffects(array $effects): array {
        $unique = [];
        $seen = [];

        // Sort by position first, then by length (longer first) for better deduplication
        usort($effects, function($a, $b) {
            $positionCompare = $a['position'] <=> $b['position'];
            if ($positionCompare !== 0) {
                return $positionCompare;
            }
            // If same position, prefer longer original text
            return mb_strlen($b['original']) <=> mb_strlen($a['original']);
        });

        foreach ($effects as $effect) {
            // Use original text and position for deduplication
            $key = $effect['original'] . '_' . $effect['position'];

            // Check for overlapping positions (one effect contained within another)
            $isOverlapping = false;
            $overlappingIndex = -1;

            foreach ($unique as $index => $existingEffect) {
                $existingStart = $existingEffect['position'];
                $existingEnd = $existingStart + mb_strlen($existingEffect['original']);
                $currentStart = $effect['position'];
                $currentEnd = $currentStart + mb_strlen($effect['original']);

                // Check if current effect overlaps with existing one
                if (($currentStart >= $existingStart && $currentStart < $existingEnd) ||
                    ($currentEnd > $existingStart && $currentEnd <= $existingEnd) ||
                    ($currentStart <= $existingStart && $currentEnd >= $existingEnd)) {

                    // If overlapping, prefer the longer one
                    if (mb_strlen($effect['original']) > mb_strlen($existingEffect['original'])) {
                        // Replace the existing shorter effect with the longer one
                        $overlappingIndex = $index;
                        $isOverlapping = false; // Allow this longer one to be added
                        break;
                    } else {
                        $isOverlapping = true;
                        break;
                    }
                }
            }

            if (!isset($seen[$key]) && !$isOverlapping) {
                // Remove the shorter overlapping effect if we found one
                if ($overlappingIndex >= 0) {
                    unset($unique[$overlappingIndex]);
                    $unique = array_values($unique); // Re-index array
                }

                $seen[$key] = true;
                $unique[] = $effect;
            }
        }

        // Final sort by position
        usort($unique, function($a, $b) {
            return $a['position'] <=> $b['position'];
        });

        return $unique;
    }

    /**
     * Get translation prompt instructions for sound effects preservation
     */
    public function getSoundEffectPreservationInstructions(): string {
        return "CRITICAL SOUND EFFECT INSTRUCTIONS:\n" .
               "1. STANDALONE SOUND EFFECTS: When you see " . self::SOUND_EFFECT_MARKER_START . "SE_X" . self::SOUND_EFFECT_MARKER_END . " markers, keep them EXACTLY as they are - these represent standalone sound effects that will be preserved in romanized form.\n" .
               "2. INTEGRATED ONOMATOPOEIA: When you see " . self::SOUND_EFFECT_MARKER_START . "IE_X" . self::SOUND_EFFECT_MARKER_END . " markers, TRANSLATE these into natural English descriptive words that fit the sentence context (e.g., 'persistently', 'roughly', 'smoothly', 'nervously', etc.).\n" .
               "3. PRESERVE BRACKET FORMAT: Keep the Unicode brackets 【】 exactly as shown for SE_ markers - do not change to [].\n" .
               "4. NATURAL INTEGRATION: For IE_ markers, replace them with appropriate English adverbs or descriptive words that maintain the sentence flow.\n" .
               "5. NO META-TEXT: Do not add any explanations, notes, or comments about sound effects or markers.\n" .
               "6. CONTEXTUAL TRANSLATION: IE_ markers represent onomatopoeia that should be translated to descriptive English words, while SE_ markers should remain untouched.\n\n";
    }

    /**
     * Fix any existing translations that have incorrect marker formats
     */
    public function fixIncorrectMarkers(string $text): string {
        // Convert ASCII brackets back to Unicode brackets for any remaining markers
        $text = preg_replace('/\[SOUND_EFFECT:(SE_\d+)\]/', '【SOUND_EFFECT:$1】', $text);
        return $text;
    }

    /**
     * Analyze the contextual position of a sound effect within the text
     */
    private function analyzeContextualPosition(string $text, int $position, string $soundEffect): array {
        $context = [
            'sentence_position' => 'middle',
            'punctuation_context' => 'none',
            'dialogue_context' => false,
            'emphasis_level' => 'normal',
            'integration_type' => 'standalone' // New field to determine handling type
        ];

        // Get surrounding text for analysis
        $beforeText = mb_substr($text, max(0, $position - 50), 50);
        $afterText = mb_substr($text, $position + mb_strlen($soundEffect), 50);

        // Analyze sentence position
        if (preg_match('/[。！？][\s]*$/u', $beforeText)) {
            $context['sentence_position'] = 'beginning';
        } elseif (preg_match('/^[\s]*[。！？]/u', $afterText)) {
            $context['sentence_position'] = 'end';
        }

        // Analyze punctuation context
        if (preg_match('/！/', $soundEffect) || preg_match('/^[\s]*！/u', $afterText)) {
            $context['punctuation_context'] = 'exclamation';
            $context['emphasis_level'] = 'high';
        } elseif (preg_match('/？/', $soundEffect) || preg_match('/^[\s]*？/u', $afterText)) {
            $context['punctuation_context'] = 'question';
        }

        // Check if within dialogue
        $dialogueStart = mb_strrpos($beforeText, '「');
        $dialogueEnd = mb_strpos($afterText, '」');
        if ($dialogueStart !== false && $dialogueEnd !== false) {
            $context['dialogue_context'] = true;
        }

        // Analyze emphasis level based on repetition and formatting
        if (preg_match('/(.)\1{2,}/', $soundEffect)) {
            $context['emphasis_level'] = 'high';
        }

        // Determine integration type - key new logic
        $context['integration_type'] = $this->determineIntegrationType($text, $position, $soundEffect, $beforeText, $afterText);

        return $context;
    }

    /**
     * Determine if onomatopoeia is integrated within a sentence or standalone
     */
    private function determineIntegrationType(string $text, int $position, string $soundEffect, string $beforeText, string $afterText): string {
        // Clean the sound effect for analysis (remove quotes and punctuation)
        $cleanSoundEffect = str_replace(['「', '」', '！', '？', '!', '?'], '', $soundEffect);

        // Check for integrated patterns FIRST (these should be translated to descriptive words)

        // Pattern 1: Sound effect is followed by と particle (adverbial usage) - HIGHEST PRIORITY
        if (preg_match('/^[\s]*と[\s]*[ぁ-んァ-ヶ一-龯]/u', $afterText)) {
            return 'integrated';
        }

        // Pattern 2: Sound effect is followed by に particle (adverbial usage) - HIGHEST PRIORITY
        if (preg_match('/^[\s]*に[\s]*[ぁ-んァ-ヶ一-龯]/u', $afterText)) {
            return 'integrated';
        }

        // Check for standalone patterns (these should remain romanized)

        // Pattern 1: Sound effect is in quotes by itself (「ドン！」)
        if (preg_match('/^「.*[！？]*」$/', $soundEffect)) {
            return 'standalone';
        }

        // Pattern 2: Sound effect appears to be a standalone exclamation (ends with ! or ?)
        if (preg_match('/[！？!?]$/', $soundEffect)) {
            return 'standalone';
        }

        // Pattern 3: Sound effect is at the beginning or end of text
        if (empty(trim($beforeText)) || empty(trim($afterText))) {
            return 'standalone';
        }

        // Pattern 4: Sound effect is surrounded by sentence-ending punctuation
        if (preg_match('/[。！？][\s]*$/u', $beforeText) && preg_match('/^[\s]*[。！？]/u', $afterText)) {
            return 'standalone';
        }

        // Pattern 5: Sound effect is on its own line or paragraph
        if (preg_match('/[\n\r][\s]*$/u', $beforeText) && preg_match('/^[\s]*[\n\r]/u', $afterText)) {
            return 'standalone';
        }

        // Additional integrated patterns (lower priority)

        // Pattern 3: Sound effect is preceded by a verb/adjective and followed by more text (descriptive usage)
        $beforeHasWords = preg_match('/[ぁ-んァ-ヶ一-龯][\s]*$/u', $beforeText);
        $afterHasWords = preg_match('/^[\s]*[ぁ-んァ-ヶ一-龯]/u', $afterText);

        if ($beforeHasWords && $afterHasWords && !preg_match('/[！？!?「」]/', $soundEffect)) {
            return 'integrated';
        }

        // Pattern 4: Sound effect appears within a sentence with multiple words (not quoted, not exclamatory)
        if (!preg_match('/[「」！？!?]/', $soundEffect)) {
            $sentenceStart = mb_strrpos($beforeText, '。');
            $sentenceEnd = mb_strpos($afterText, '。');

            if ($sentenceStart === false) $sentenceStart = 0;
            if ($sentenceEnd === false) $sentenceEnd = mb_strlen($afterText);

            $fullSentence = mb_substr($beforeText, $sentenceStart) . $soundEffect . mb_substr($afterText, 0, $sentenceEnd);
            $wordCount = preg_match_all('/[ぁ-んァ-ヶ一-龯]+/u', $fullSentence);

            // If the sentence has multiple words and the sound effect is embedded, it's likely integrated
            if ($wordCount > 4) {
                return 'integrated';
            }
        }

        // Default to standalone for safety (preserves original behavior)
        return 'standalone';
    }

    /**
     * Get descriptive English translation for integrated onomatopoeia
     */
    private function getDescriptiveTranslation(string $original, string $romanized): string {
        // Map common Japanese onomatopoeia to descriptive English words
        $descriptiveMap = [
            // Persistent/continuous actions
            'ねちねち' => 'persistently',
            'ネチネチ' => 'persistently',
            'nechinechi' => 'persistently',
            'じりじり' => 'gradually',
            'ジリジリ' => 'gradually',
            'jirijiri' => 'gradually',
            'ぐいぐい' => 'forcefully',
            'グイグイ' => 'forcefully',
            'guigui' => 'forcefully',

            // Movement descriptions
            'ふらふら' => 'unsteadily',
            'フラフラ' => 'unsteadily',
            'furafura' => 'unsteadily',
            'よろよろ' => 'shakily',
            'ヨロヨロ' => 'shakily',
            'yoroyoro' => 'shakily',
            'すたすた' => 'briskly',
            'スタスタ' => 'briskly',
            'sutasuta' => 'briskly',

            // Emotional/physical sensations
            'どきどき' => 'nervously',
            'ドキドキ' => 'nervously',
            'dokidoki' => 'nervously',
            'わくわく' => 'excitedly',
            'ワクワク' => 'excitedly',
            'wakuwaku' => 'excitedly',
            'いらいら' => 'irritably',
            'イライラ' => 'irritably',
            'iraira' => 'irritably',

            // Texture/touch descriptions
            'ざらざら' => 'roughly',
            'ザラザラ' => 'roughly',
            'zarazara' => 'roughly',
            'つるつる' => 'smoothly',
            'ツルツル' => 'smoothly',
            'tsurutsuru' => 'smoothly',
            'ぺたぺた' => 'stickily',
            'ペタペタ' => 'stickily',
            'petapeta' => 'stickily',

            // Sound/manner descriptions
            'ぺらぺら' => 'fluently',
            'ペラペラ' => 'fluently',
            'perapera' => 'fluently',
            'ぺちゃぺちゃ' => 'chattily',
            'ペチャペチャ' => 'chattily',
            'pechapeche' => 'chattily',

            // Weather/environment
            'しとしと' => 'gently',
            'シトシト' => 'gently',
            'shitoshito' => 'gently',
            'ざーざー' => 'heavily',
            'ザーザー' => 'heavily',
            'zaazaa' => 'heavily',
        ];

        // Try exact match first
        if (isset($descriptiveMap[$original])) {
            return $descriptiveMap[$original];
        }

        // Try romanized version
        if (isset($descriptiveMap[$romanized])) {
            return $descriptiveMap[$romanized];
        }

        // Try lowercase romanized version
        $lowerRomanized = strtolower($romanized);
        if (isset($descriptiveMap[$lowerRomanized])) {
            return $descriptiveMap[$lowerRomanized];
        }

        // Fallback: try to infer from patterns
        if (preg_match('/(.)\1+/', $original)) {
            // Repetitive patterns often indicate continuous action
            return 'continuously';
        }

        // Final fallback: use a generic descriptive word
        return 'expressively';
    }

    /**
     * Format romanized sound effect based on context
     */
    private function formatRomanizedSoundEffect(array $effect): string {
        $romanized = $effect['romanized'];

        // Apply context-based formatting if available
        if (isset($effect['context'])) {
            $context = $effect['context'];

            // Add emphasis for high-emphasis sound effects
            if ($context['emphasis_level'] === 'high') {
                // Keep the romanized form but ensure it maintains impact
                $romanized = strtoupper(substr($romanized, 0, 1)) . substr($romanized, 1);
            }

            // Handle punctuation context
            if ($context['punctuation_context'] === 'exclamation') {
                // The exclamation should be handled by the surrounding translation
                // Just ensure the sound effect itself is properly formatted
                $romanized = trim($romanized, '!');
            }
        }

        return $romanized;
    }

    /**
     * Clean up the restored text to fix any formatting issues
     */
    private function cleanupRestoredText(string $text): string {
        // VERY conservative cleanup - only fix issues that sound effect restoration might have caused
        // Do NOT modify line breaks or paragraph structure

        // Only remove excessive horizontal spaces (3 or more spaces/tabs in a row)
        $text = preg_replace('/[ \t]{3,}/', '  ', $text);

        // Fix spacing around punctuation only if there are excessive spaces
        $text = preg_replace('/[ \t]{2,}([.!?])/', ' $1', $text);

        // Ensure single space after punctuation only if there's no space or excessive space
        $text = preg_replace('/([.!?])([A-Za-z])/', '$1 $2', $text);
        $text = preg_replace('/([.!?])[ \t]{2,}([A-Za-z])/', '$1 $2', $text);

        // Remove only truly problematic control characters, preserve newlines and formatting
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Do NOT trim - preserve leading/trailing whitespace that might be intentional
        return $text;
    }
}
