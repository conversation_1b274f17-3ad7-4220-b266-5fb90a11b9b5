<?php
/**
 * API Monitoring and Debugging Tool
 * Use this to monitor API calls and identify JSON issues
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

require_once 'config/config.php';

echo "=== API Monitoring Tool ===\n";
echo "This tool helps monitor and debug API responses\n\n";

// Function to test API endpoint
function testApiEndpoint($url, $data, $description) {
    echo "=== Testing: $description ===\n";
    echo "URL: $url\n";
    echo "Data: " . json_encode($data) . "\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json',
        'Content-Length: ' . strlen(json_encode($data))
    ]);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HEADER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);
    
    if (curl_error($ch)) {
        echo "❌ cURL Error: " . curl_error($ch) . "\n\n";
        curl_close($ch);
        return false;
    }
    
    curl_close($ch);
    
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    echo "HTTP Code: $httpCode\n";
    echo "Response Length: " . strlen($body) . " characters\n";
    
    // Test JSON parsing
    $decoded = json_decode($body, true);
    if ($decoded === null && json_last_error() !== JSON_ERROR_NONE) {
        echo "❌ JSON Parse Error: " . json_last_error_msg() . "\n";
        echo "Response Preview: " . substr($body, 0, 200) . "\n";
        
        // Analyze the issue
        if (strpos($body, '<html>') !== false) {
            echo "❌ Response contains HTML\n";
        } elseif (trim($body) === '') {
            echo "❌ Empty response\n";
        } elseif (!mb_check_encoding($body, 'UTF-8')) {
            echo "❌ Invalid UTF-8 encoding\n";
        }
        
        return false;
    } else {
        echo "✅ JSON parsing successful\n";
        if (isset($decoded['success'])) {
            echo "Success: " . ($decoded['success'] ? 'true' : 'false') . "\n";
            if (!$decoded['success'] && isset($decoded['error'])) {
                echo "Error: " . $decoded['error'] . "\n";
            }
        }
        return true;
    }
    
    echo "\n";
}

// Test various scenarios
$baseUrl = 'http://localhost/wc/api/chapters.php';

$tests = [
    [
        'description' => 'Save existing chapter (should return "already saved")',
        'data' => ['novel_id' => 1, 'chapter_number' => 3]
    ],
    [
        'description' => 'Save new chapter (if available)',
        'data' => ['novel_id' => 1, 'chapter_number' => 200]
    ],
    [
        'description' => 'Invalid novel ID',
        'data' => ['novel_id' => 99999, 'chapter_number' => 1]
    ],
    [
        'description' => 'Invalid chapter number',
        'data' => ['novel_id' => 1, 'chapter_number' => 0]
    ],
    [
        'description' => 'Missing parameters',
        'data' => ['novel_id' => 1]
    ]
];

$successCount = 0;
$totalTests = count($tests);

foreach ($tests as $test) {
    if (testApiEndpoint($baseUrl, $test['data'], $test['description'])) {
        $successCount++;
    }
}

echo "=== Summary ===\n";
echo "Tests passed: $successCount/$totalTests\n";

if ($successCount === $totalTests) {
    echo "✅ All tests passed - API is working correctly\n";
} else {
    echo "⚠️  Some tests failed - check the output above for details\n";
}

echo "\n=== Recommendations ===\n";
echo "1. If you encounter JSON errors in the browser:\n";
echo "   - Check the browser's Network tab for the actual response\n";
echo "   - Look for PHP errors in the response body\n";
echo "   - Check server error logs\n\n";

echo "2. Common causes of JSON errors:\n";
echo "   - PHP fatal errors or warnings\n";
echo "   - Output buffering issues\n";
echo "   - Invalid UTF-8 characters in content\n";
echo "   - Server timeouts\n\n";

echo "3. To debug in real-time:\n";
echo "   - Enable browser developer tools\n";
echo "   - Monitor the Network tab during chapter save\n";
echo "   - Check the Response tab for the actual server response\n";
echo "   - Look for any HTML error pages instead of JSON\n\n";

echo "=== Monitoring Complete ===\n";
?>
