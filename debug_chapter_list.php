<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$url = 'https://69shuba.cx/book/44425.htm';

try {
    echo "Getting chapter list from: $url\n";
    $chapters = $crawler->getChapterList($url);
    
    echo "Chapters array structure:\n";
    var_dump(array_slice($chapters, 0, 3)); // Show first 3 chapters
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
