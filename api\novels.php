<?php
/**
 * API Endpoint: Novel Management
 * POST /api/novels.php - Save novel
 * GET /api/novels.php - Get saved novels
 * PUT /api/novels.php - Update novel
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];
$novelManager = new NovelManager();

try {
    switch ($method) {
        case 'GET':
            handleGetNovels($novelManager);
            break;
            
        case 'POST':
            handleSaveNovel($novelManager);
            break;
            
        case 'PUT':
            handleUpdateNovel($novelManager);
            break;

        case 'DELETE':
            handleDeleteNovel($novelManager);
            break;

        case 'PATCH':
            handleTranslateSynopsis($novelManager);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
    
} catch (Exception $e) {
    logError('Novels API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);
    
    jsonResponse([
        'success' => false,
        'error' => 'An error occurred while processing your request'
    ], 500);
}

/**
 * Handle GET request - Get saved novels
 */
function handleGetNovels($novelManager) {
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? max(1, min(100, (int)$_GET['limit'])) : 20;

    try {
        $result = $novelManager->getSavedNovels($page, $limit);

        // Debug log
        error_log("Novels API: getSavedNovels result: " . json_encode([
            'novels_count' => count($result['novels'] ?? []),
            'has_pagination' => isset($result['pagination']),
            'structure' => array_keys($result)
        ]));

        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } catch (Exception $e) {
        error_log("Novels API: Error in handleGetNovels: " . $e->getMessage());
        jsonResponse([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * Handle POST request - Save novel
 */
function handleSaveNovel($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }
    
    // Validate required fields based on platform
    if (isset($input['platform']) && $input['platform'] === 'manual') {
        // Manual novels have different requirements
        $requiredFields = ['original_title', 'original_synopsis'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty(trim($input[$field]))) {
                jsonResponse(['error' => "Field '{$field}' is required for manual novels"], 400);
            }
        }

        // Use manual novel creation method
        $result = $novelManager->createManualNovel($input);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'data' => $result
            ], 201);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error']
            ], 400);
        }
        return;
    } else {
        // Regular crawled novels
        $requiredFields = ['url', 'platform', 'original_title'];
        foreach ($requiredFields as $field) {
            if (!isset($input[$field]) || empty($input[$field])) {
                jsonResponse(['error' => "Field '{$field}' is required"], 400);
            }
        }
    }
    
    // Sanitize input with proper title processing
    $novelData = [
        'url' => sanitizeInput($input['url']),
        'platform' => sanitizeInput($input['platform']),
        'original_title' => processTitleText($input['original_title']),
        'translated_title' => isset($input['translated_title']) ? processTitleText($input['translated_title']) : null,
        'author' => isset($input['author']) ? sanitizeInput($input['author'], true) : null,
        'publication_date' => isset($input['publication_date']) ? $input['publication_date'] : null,
        'total_chapters' => isset($input['total_chapters']) ? (int)$input['total_chapters'] : 0,
        'original_synopsis' => isset($input['original_synopsis']) ? $input['original_synopsis'] : null,
        'translated_synopsis' => isset($input['translated_synopsis']) ? $input['translated_synopsis'] : null,
        'chapters' => isset($input['chapters']) ? $input['chapters'] : []
    ];
    
    $result = $novelManager->saveNovel($novelData);
    
    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ], 201);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}

/**
 * Handle PUT request - Update novel
 */
function handleUpdateNovel($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }
    
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }
    
    $novelId = (int)$input['novel_id'];
    unset($input['novel_id']);
    
    $result = $novelManager->updateNovel($novelId, $input);
    
    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}

/**
 * Handle DELETE request - Delete novel
 */
function handleDeleteNovel($novelManager) {
    if (!isset($_GET['id']) || !is_numeric($_GET['id'])) {
        jsonResponse(['error' => 'Valid novel ID is required'], 400);
    }

    $novelId = (int)$_GET['id'];

    $result = $novelManager->deleteNovel($novelId);

    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}

/**
 * Handle PATCH request - Translate synopsis
 */
function handleTranslateSynopsis($novelManager) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input || !isset($input['novel_id'])) {
        jsonResponse(['error' => 'Novel ID is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $targetLanguage = isset($input['target_language']) ? $input['target_language'] : 'en';

    $result = $novelManager->translateSynopsis($novelId, $targetLanguage);

    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}
