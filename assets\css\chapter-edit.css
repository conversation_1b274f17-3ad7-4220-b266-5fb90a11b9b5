/**
 * Chapter Edit Page Styles
 * Specific styling for the chapter translation editor page
 */

/* Editor Container */
.editor-toolbar {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.editor-toolbar .btn-group {
    margin-right: 0.5rem;
}

.editor-toolbar .btn-group:last-child {
    margin-right: 0;
}

/* Editor Stats */
.editor-stats small {
    line-height: 1.4;
}

/* Original Text Content */
.original-text-content {
    max-height: 600px;
    overflow-y: auto;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 0.375rem;
    font-family: 'Georgia', serif;
    line-height: 1.6;
}

.original-text-content p {
    margin-bottom: 1rem;
}

.original-text-content p:last-child {
    margin-bottom: 0;
}

/* TinyMCE Editor Customization */
.tox-tinymce {
    border-radius: 0 0 0.375rem 0.375rem !important;
    border-top: none !important;
}

.tox-editor-header {
    border-radius: 0 !important;
}

/* Fullscreen Mode */
.editor-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: white;
    padding: 1rem;
    overflow: auto;
}

.editor-fullscreen .card {
    height: calc(100vh - 2rem);
}

.editor-fullscreen .tox-tinymce {
    height: calc(100vh - 200px) !important;
}

/* Side-by-side Layout */
.side-by-side #editor-panel {
    transition: all 0.3s ease;
}

.side-by-side #original-panel {
    transition: all 0.3s ease;
}

/* Auto-save Indicator */
.auto-save-indicator {
    position: fixed;
    top: 20px;
    right: 20px;
    z-index: 1050;
    padding: 0.5rem 1rem;
    background: rgba(40, 167, 69, 0.9);
    color: white;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.auto-save-indicator.show {
    opacity: 1;
}

.auto-save-indicator.error {
    background: rgba(220, 53, 69, 0.9);
}

/* Loading States */
.editor-loading {
    position: relative;
}

.editor-loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

/* Responsive Design */
@media (max-width: 768px) {
    .editor-toolbar {
        padding: 0.75rem;
    }
    
    .editor-toolbar .btn-group {
        margin-bottom: 0.5rem;
        margin-right: 0.25rem;
    }
    
    .editor-toolbar .btn {
        font-size: 0.875rem;
        padding: 0.375rem 0.75rem;
    }
    
    .original-text-content {
        max-height: 400px;
        padding: 0.75rem;
    }
    
    .editor-stats {
        margin-top: 1rem;
        text-align: left !important;
    }
}

@media (max-width: 576px) {
    .container-fluid {
        padding-left: 0.75rem;
        padding-right: 0.75rem;
    }
    
    .breadcrumb {
        font-size: 0.875rem;
    }
    
    .breadcrumb-item {
        margin-right: 0.25rem;
    }
    
    .editor-toolbar .btn-group {
        width: 100%;
        margin-bottom: 0.5rem;
    }
    
    .editor-toolbar .btn {
        flex: 1;
        font-size: 0.8rem;
    }
}

/* Dark Mode Support (if implemented) */
@media (prefers-color-scheme: dark) {
    .original-text-content {
        background: #2d3748;
        color: #e2e8f0;
    }
    
    .editor-toolbar {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
}

/* Print Styles */
@media print {
    .editor-toolbar,
    .btn,
    .modal,
    #original-panel {
        display: none !important;
    }
    
    .tox-tinymce {
        border: none !important;
    }
}

/* Accessibility Improvements */
.btn:focus,
.btn:focus-visible {
    box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.original-text-content:focus {
    outline: 2px solid #0d6efd;
    outline-offset: 2px;
}

/* Animation for smooth transitions */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Status indicators */
.status-indicator {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    margin-right: 0.5rem;
}

.status-indicator.saving {
    background: #ffc107;
    animation: pulse 1s infinite;
}

.status-indicator.saved {
    background: #28a745;
}

.status-indicator.error {
    background: #dc3545;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Ruby text (furigana) support in original content */
.original-text-content ruby {
    ruby-align: center;
}

.original-text-content rt {
    font-size: 0.6em;
    color: #6c757d;
}

/* Word count styling */
#word-count {
    font-weight: 600;
    color: #0d6efd;
}

/* Help modal customization */
.modal-body kbd {
    background-color: #e9ecef;
    border: 1px solid #adb5bd;
    border-radius: 0.25rem;
    color: #495057;
    font-size: 0.875em;
    padding: 0.2rem 0.4rem;
}

/* Breadcrumb customization */
.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    color: #6c757d;
}

.breadcrumb-item.active {
    color: #6c757d;
}

.breadcrumb a {
    text-decoration: none;
    color: #0d6efd;
}

.breadcrumb a:hover {
    text-decoration: underline;
}
