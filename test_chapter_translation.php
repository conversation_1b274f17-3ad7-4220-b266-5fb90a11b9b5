<?php
/**
 * Test script to verify name dictionary integration in actual chapter translation
 */

require_once 'config/config.php';
require_once 'config/database.php';
require_once 'classes/TranslationService.php';

// Test configuration
$novelId = 6; // Use an existing novel with name dictionary entries
$chapterId = 480; // Use an existing chapter

echo "=== Chapter Translation Test ===\n\n";

try {
    $db = Database::getInstance();
    $translationService = new TranslationService();
    
    // Get chapter info
    $chapter = $db->fetchOne(
        "SELECT id, chapter_number, original_title, original_content FROM chapters WHERE id = ? AND novel_id = ?",
        [$chapterId, $novelId]
    );
    
    if (!$chapter) {
        echo "ERROR: Chapter {$chapterId} not found for novel {$novelId}\n";
        exit(1);
    }
    
    echo "Chapter: {$chapter['chapter_number']} - {$chapter['original_title']}\n";
    echo "Content length: " . strlen($chapter['original_content']) . " characters\n\n";
    
    // Get name dictionary
    $nameDictionary = $translationService->getNameDictionary($novelId);
    echo "Name Dictionary Entries: " . count($nameDictionary) . "\n\n";
    
    // Show first few character names that might appear in the chapter
    echo "Character names in dictionary:\n";
    $characterCount = 0;
    foreach ($nameDictionary as $name) {
        if ($name['name_type'] === 'character' && $characterCount < 5) {
            $targetName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
            echo "  - {$name['original_name']} → {$targetName}\n";
            $characterCount++;
        }
    }
    
    echo "\n=== Starting Translation ===\n";
    
    // Translate the chapter
    $result = $translationService->translateChapter($novelId, $chapterId, 'en');
    
    if ($result['success']) {
        echo "✓ Translation successful!\n";
        echo "Title: {$result['title_translation']['translated_text']}\n";
        echo "Content length: " . strlen($result['content_translation']['translated_text']) . " characters\n";
        echo "Execution time: {$result['content_translation']['execution_time']} seconds\n";
        
        // Check if character names from dictionary appear in the translated content
        echo "\n=== Name Dictionary Usage Verification ===\n";
        $translatedContent = $result['content_translation']['translated_text'];
        $namesFound = 0;
        
        foreach ($nameDictionary as $name) {
            if ($name['name_type'] === 'character') {
                $originalName = $name['original_name'];
                $targetName = $name['translation'] ?? $name['romanization'] ?? $name['original_name'];
                
                // Check if original name appears in original content
                if (strpos($chapter['original_content'], $originalName) !== false) {
                    echo "Original name '{$originalName}' found in chapter\n";
                    
                    // Check if target name appears in translation
                    if (strpos($translatedContent, $targetName) !== false) {
                        echo "  ✓ Target name '{$targetName}' found in translation\n";
                        $namesFound++;
                    } else {
                        echo "  ✗ Target name '{$targetName}' NOT found in translation\n";
                        
                        // Check if original was left untranslated
                        if (strpos($translatedContent, $originalName) !== false) {
                            echo "    ⚠️  Original name '{$originalName}' left untranslated\n";
                        }
                    }
                }
            }
        }
        
        echo "\nCharacter names correctly translated: {$namesFound}\n";
        
        if ($namesFound > 0) {
            echo "✓ Name dictionary integration is working in chapter translation!\n";
        } else {
            echo "⚠️  No character names were verified (may not be present in this chapter)\n";
        }
        
    } else {
        echo "✗ Translation failed: {$result['error']}\n";
    }
    
} catch (Exception $e) {
    echo "Test failed with exception: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Test Complete ===\n";
?>
