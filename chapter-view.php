<?php
/**
 * Chapter View Page
 * Displays individual chapter content with editing capabilities
 */

require_once 'config/config.php';
require_once 'includes/header.php';

// Get parameters from URL
$novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;
$chapterNumber = isset($_GET['chapter']) ? (int)$_GET['chapter'] : 0;

if (!$novelId || !$chapterNumber) {
    header('Location: index.php');
    exit;
}

renderHeader('Chapter View');
?>

<?php 
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php'; 
renderNavigation();
?>

<div class="container mt-4">
    <!-- Back Navigation -->
    <div class="mb-3">
        <a href="novel-details.php?id=<?= $novelId ?>" class="btn btn-outline-secondary">
            <i class="fas fa-arrow-left me-2"></i>
            Back to Novel Details
        </a>
    </div>

    <!-- Chapter Content -->
    <div id="chapter-content">
        <div class="text-center">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-3">Loading chapter...</p>
        </div>
    </div>
</div>

<?php renderFooter(['assets/js/chapter-view.js']); ?>

<script>
// Pass parameters to JavaScript
window.novelId = <?= $novelId ?>;
window.chapterNumber = <?= $chapterNumber ?>;
</script>
