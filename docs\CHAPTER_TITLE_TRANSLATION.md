# Chapter Title Translation Feature

## Overview

This document describes the chapter title translation functionality that allows users to translate chapter titles independently of chapter content, with both bulk and individual translation options.

## Features

### 1. Bulk Title Translation
- **"Translate All Chapter Titles"** button on the novel details page
- Translates all chapter titles that have original titles but no translated titles
- Shows progress and results for each title translation
- Uses the same DeepSeek API with name dictionary consistency

### 2. Individual Title Translation
- **"Translate Title"** buttons next to each chapter entry
- Only appears for chapters that have original titles but no translated titles
- Allows selective translation of specific chapter titles
- Immediate feedback and interface refresh

### 3. Automatic Title Translation in Chunk Interface
- When using chunk-by-chunk translation, titles are automatically translated when all chunks are completed
- Ensures consistency between content and title translation
- Uses the same name dictionary for character name consistency

## Implementation Details

### API Endpoints

#### New Endpoint: `/api/chapter-titles.php`
- **Method**: PUT
- **Purpose**: Dedicated endpoint for chapter title translation
- **Supports**:
  - Single chapter title translation
  - Bulk title translation
  - "Translate all" functionality

**Request Format:**
```json
{
  "novel_id": 123,
  "chapter_number": 5,          // For single chapter
  "chapter_numbers": [1,2,3],   // For multiple chapters
  "translate_all": true,        // For all chapters
  "target_language": "en"
}
```

**Response Format:**
```json
{
  "success": true,
  "data": {
    "total_chapters": 3,
    "successful_translations": 3,
    "failed_translations": 0,
    "results": [
      {
        "chapter_number": 1,
        "success": true,
        "original_title": "第一章：始まり",
        "translated_title": "Chapter 1: The Beginning"
      }
    ],
    "message": "All 3 chapter titles translated successfully"
  }
}
```

### Backend Methods

#### NovelManager::translateChapterTitles()
- Handles bulk and individual title translation
- Supports "all chapters" mode
- Uses name dictionary for consistency
- Provides detailed results for each translation

#### NovelManager::ensureChapterTitleTranslated()
- Automatically called when chunk translation completes
- Ensures titles are translated in chunk-based workflows
- Silent operation with debug logging

### Frontend Interface

#### Novel Details Page Updates
- New title translation controls section
- Shows count of chapters needing title translation
- "Translate All Titles" button for bulk operations
- Individual "Title" buttons for selective translation

#### JavaScript Methods
- `translateAllChapterTitles()`: Handles bulk translation
- `translateChapterTitle(chapterNumber)`: Handles individual translation
- `renderTitleTranslationControls()`: Displays translation controls
- Automatic interface refresh after translations

## User Interface

### Title Translation Controls
```
┌─────────────────────────────────────────────────────────────┐
│ 📝 5 chapter titles need translation                       │
│                                    [Translate All Titles]  │
└─────────────────────────────────────────────────────────────┘
```

### Individual Chapter Actions
```
Ch. 1  Original Title Here                    [Save] [Title] [View]
Ch. 2  Translated Title Here                  [Translate] [View]
```

## Translation Quality

### Name Consistency
- Uses the same name dictionary as content translation
- Ensures character names are translated consistently
- Maintains continuity between titles and content

### API Configuration
- Lower temperature (0.1) for more consistent title translations
- Reduced max tokens (100) for concise results
- Same DeepSeek API integration as content translation

### Error Handling
- Graceful handling of API failures
- Detailed error messages for troubleshooting
- Retry logic for temporary service issues

## Usage Scenarios

### 1. New Novel Setup
1. Add novel and save chapters
2. Use "Translate All Titles" for quick setup
3. Proceed with content translation

### 2. Selective Translation
1. Browse chapter list
2. Click "Title" button for specific chapters
3. Titles are translated individually

### 3. Chunk-based Translation
1. Translate chapters using chunk interface
2. Titles are automatically translated when chunks complete
3. No additional action required

## Benefits

### Efficiency
- Faster title translation without full content processing
- Bulk operations for multiple chapters
- Automatic integration with chunk workflows

### Flexibility
- Choose between bulk and individual translation
- Works independently of content translation status
- Maintains existing translation workflows

### Consistency
- Uses same translation service and settings
- Maintains name dictionary consistency
- Preserves user preferences for clean translations

## Technical Notes

### Database Updates
- Updates `chapters.translated_title` field
- Maintains existing schema compatibility
- No migration required

### Performance
- Lightweight API calls for title-only translation
- Efficient bulk processing
- Minimal impact on existing functionality

### Compatibility
- Works with existing chunk translation system
- Compatible with all novel platforms (Kakuyomu, 69shuba, etc.)
- Maintains backward compatibility

## Error Handling

### Common Scenarios
- **No original title**: Gracefully skipped with informative message
- **Already translated**: Detected and skipped automatically
- **API failures**: Detailed error reporting with retry suggestions
- **Network issues**: Timeout handling with user feedback

### Logging
- Debug logs for translation operations
- Error tracking for troubleshooting
- Performance metrics for optimization

## Future Enhancements

### Potential Improvements
- Title translation preview before applying
- Batch editing of translated titles
- Integration with novel title translation
- Custom translation templates for titles

### User Feedback Integration
- Translation quality ratings
- Manual title correction interface
- Preferred translation style settings
