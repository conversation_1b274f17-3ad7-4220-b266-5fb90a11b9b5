<?php
/**
 * Excel Import Service for Name Dictionary
 * Handles Excel file processing and name import functionality
 */

// Load Composer autoloader with correct path resolution
$vendorPath = defined('APP_ROOT') ? APP_ROOT . '/vendor/autoload.php' : __DIR__ . '/../vendor/autoload.php';
if (file_exists($vendorPath)) {
    require_once $vendorPath;
} else {
    // Fallback paths
    $fallbackPaths = [
        __DIR__ . '/../vendor/autoload.php',
        dirname(__DIR__) . '/vendor/autoload.php',
        'vendor/autoload.php'
    ];

    $loaded = false;
    foreach ($fallbackPaths as $path) {
        if (file_exists($path)) {
            require_once $path;
            $loaded = true;
            break;
        }
    }

    if (!$loaded) {
        throw new Exception('Composer autoloader not found. Please run "composer install".');
    }
}

use PhpOffice\PhpSpreadsheet\IOFactory;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ExcelImportService {
    private $db;
    private $validNameTypes = ['character', 'location', 'organization', 'country', 'skill', 'monster', 'other', 'item', 'class/profession'];
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Generate and download Excel template
     */
    public function generateTemplate(): array {
        try {
            $spreadsheet = new Spreadsheet();
            $sheet = $spreadsheet->getActiveSheet();
            
            // Set headers
            $headers = ['Original Name', 'Romanization', 'Translation', 'Name Type'];
            $sheet->fromArray($headers, null, 'A1');
            
            // Add sample data
            $sampleData = [
                ['田中太郎', 'Tanaka Tarou', 'Taro Tanaka', 'character'],
                ['東京', 'Tokyo', 'Tokyo', 'location'],
                ['魔法学院', 'Mahou Gakuin', 'Magic Academy', 'organization'],
                ['火球術', 'Kakyuu-jutsu', 'Fireball Technique', 'skill'],
                ['聖剣エクスカリバー', 'Seiken Excalibur', 'Holy Sword Excalibur', 'item'],
                ['戦士', 'Senshi', 'Warrior', 'class/profession']
            ];
            $sheet->fromArray($sampleData, null, 'A2');
            
            // Style the header row
            $headerStyle = [
                'font' => ['bold' => true],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['rgb' => 'E6E6FA']
                ],
                'borders' => [
                    'allBorders' => [
                        'borderStyle' => \PhpOffice\PhpSpreadsheet\Style\Border::BORDER_THIN
                    ]
                ]
            ];
            $sheet->getStyle('A1:D1')->applyFromArray($headerStyle);
            
            // Auto-size columns
            foreach (range('A', 'D') as $column) {
                $sheet->getColumnDimension($column)->setAutoSize(true);
            }
            
            // Add data validation for Name Type column
            $validation = $sheet->getCell('D2')->getDataValidation();
            $validation->setType(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::TYPE_LIST);
            $validation->setErrorStyle(\PhpOffice\PhpSpreadsheet\Cell\DataValidation::STYLE_STOP);
            $validation->setAllowBlank(false);
            $validation->setShowInputMessage(true);
            $validation->setShowErrorMessage(true);
            $validation->setShowDropDown(true);
            $validation->setErrorTitle('Invalid Name Type');
            $validation->setError('Please select a valid name type from the dropdown.');
            $validation->setPromptTitle('Name Type');
            $validation->setPrompt('Select the type of name: character, location, organization, country, skill, monster, other, item, or class/profession');
            $validation->setFormula1('"' . implode(',', $this->validNameTypes) . '"');
            
            // Apply validation to the entire column
            $sheet->setDataValidation('D2:D1000', clone $validation);
            
            // Generate filename
            $filename = 'name_dictionary_template_' . date('Y-m-d_H-i-s') . '.xlsx';
            $filepath = APP_ROOT . '/temp/' . $filename;
            
            // Save file
            $writer = new Xlsx($spreadsheet);
            $writer->save($filepath);
            
            return [
                'success' => true,
                'filename' => $filename,
                'filepath' => $filepath
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Failed to generate template: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Process uploaded Excel file
     */
    public function processExcelFile(string $filePath, int $novelId): array {
        try {
            // Verify novel exists
            $novel = $this->db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
            if (!$novel) {
                throw new Exception("Novel not found");
            }
            
            // Load Excel file
            $spreadsheet = IOFactory::load($filePath);
            $sheet = $spreadsheet->getActiveSheet();
            $data = $sheet->toArray();
            
            // Validate file structure
            if (empty($data) || count($data) < 2) {
                throw new Exception("Excel file is empty or has no data rows");
            }
            
            // Check headers
            $expectedHeaders = ['Original Name', 'Romanization', 'Translation', 'Name Type'];
            $actualHeaders = array_map('trim', $data[0]);
            
            if ($actualHeaders !== $expectedHeaders) {
                throw new Exception("Invalid Excel format. Expected headers: " . implode(', ', $expectedHeaders));
            }
            
            // Process data rows
            $results = $this->processDataRows(array_slice($data, 1), $novelId);
            
            return [
                'success' => true,
                'data' => $results
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
    
    /**
     * Process data rows from Excel
     */
    private function processDataRows(array $rows, int $novelId): array {
        $validEntries = [];
        $duplicateEntries = [];
        $errorEntries = [];
        
        // Get existing names for duplicate checking
        $existingNames = $this->getExistingNames($novelId);
        
        foreach ($rows as $index => $row) {
            $rowNumber = $index + 2; // +2 because we start from row 2 (after header)
            
            // Skip empty rows
            if (empty(array_filter($row))) {
                continue;
            }
            
            $originalName = trim($row[0] ?? '');
            $romanization = trim($row[1] ?? '') ?: null;
            $translation = trim($row[2] ?? '') ?: null;
            $nameType = trim($row[3] ?? '');
            
            // Validate required fields
            if (empty($originalName)) {
                $errorEntries[] = [
                    'row' => $rowNumber,
                    'error' => 'Original Name is required',
                    'data' => $row
                ];
                continue;
            }
            
            if (empty($nameType) || !in_array($nameType, $this->validNameTypes)) {
                $errorEntries[] = [
                    'row' => $rowNumber,
                    'error' => 'Invalid or missing Name Type. Must be one of: ' . implode(', ', $this->validNameTypes),
                    'data' => $row
                ];
                continue;
            }
            
            // Check for duplicates (case-sensitive)
            if (isset($existingNames[$originalName])) {
                $existing = $existingNames[$originalName];
                $duplicateEntries[] = [
                    'row' => $rowNumber,
                    'existing' => array_merge($existing, ['id' => $existing['id'] ?? null]),
                    'new' => [
                        'original_name' => $originalName,
                        'romanization' => $romanization,
                        'translation' => $translation,
                        'name_type' => $nameType
                    ]
                ];
                continue;
            }
            
            // Valid entry
            $validEntries[] = [
                'row' => $rowNumber,
                'original_name' => $originalName,
                'romanization' => $romanization,
                'translation' => $translation,
                'name_type' => $nameType
            ];
        }
        
        return [
            'valid_entries' => $validEntries,
            'duplicate_entries' => $duplicateEntries,
            'error_entries' => $errorEntries,
            'total_processed' => count($rows),
            'valid_count' => count($validEntries),
            'duplicate_count' => count($duplicateEntries),
            'error_count' => count($errorEntries)
        ];
    }
    
    /**
     * Get existing names for duplicate checking
     */
    private function getExistingNames(int $novelId): array {
        $names = $this->db->fetchAll(
            "SELECT id, original_name, romanization, translation, name_type, frequency
             FROM name_dictionary
             WHERE novel_id = ?",
            [$novelId]
        );

        $existingNames = [];
        foreach ($names as $name) {
            $existingNames[$name['original_name']] = $name;
        }

        return $existingNames;
    }
    
    /**
     * Import valid entries to database
     */
    public function importValidEntries(array $validEntries, int $novelId): array {
        try {
            $novelManager = new NovelManager();
            $importedCount = 0;
            $errors = [];
            
            foreach ($validEntries as $entry) {
                $result = $novelManager->addNameToDictionary($novelId, [
                    'original_name' => $entry['original_name'],
                    'romanization' => $entry['romanization'],
                    'translation' => $entry['translation'],
                    'name_type' => $entry['name_type']
                ]);
                
                if ($result['success']) {
                    $importedCount++;
                } else {
                    $errors[] = [
                        'row' => $entry['row'],
                        'name' => $entry['original_name'],
                        'error' => $result['error']
                    ];
                }
            }
            
            return [
                'success' => true,
                'imported_count' => $importedCount,
                'errors' => $errors
            ];
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => $e->getMessage()
            ];
        }
    }
}
