<?php
/**
 * API Endpoint: Excel Template Download
 * GET /api/excel-template.php - Download Excel template for name dictionary import
 */

// Set the correct path for includes
$rootPath = dirname(__DIR__);
require_once $rootPath . '/config/config.php';
require_once $rootPath . '/classes/ExcelImportService.php';

// Don't set JSON content type initially - we'll set appropriate headers based on success/failure

try {
    if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
        header('Content-Type: application/json');
        jsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
        exit;
    }

    $excelService = new ExcelImportService();
    $result = $excelService->generateTemplate();

    if ($result['success']) {
        // Verify file exists and is readable
        if (!file_exists($result['filepath']) || !is_readable($result['filepath'])) {
            header('Content-Type: application/json');
            jsonResponse(['success' => false, 'error' => 'Generated file not accessible'], 500);
            exit;
        }

        // Clear any output buffers before setting headers
        while (ob_get_level()) {
            ob_end_clean();
        }

        // Set headers for file download
        header('Content-Type: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
        header('Content-Disposition: attachment; filename="' . $result['filename'] . '"');
        header('Content-Length: ' . filesize($result['filepath']));
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Expires: 0');

        // Output the file
        readfile($result['filepath']);

        // Clean up the temporary file after download
        unlink($result['filepath']);

        exit;
    } else {
        header('Content-Type: application/json');
        jsonResponse(['success' => false, 'error' => $result['error']], 500);
        exit;
    }

} catch (Exception $e) {
    error_log("Excel Template API Error: " . $e->getMessage());
    header('Content-Type: application/json');
    jsonResponse(['success' => false, 'error' => 'Internal server error: ' . $e->getMessage()], 500);
    exit;
}
?>
