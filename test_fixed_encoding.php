<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();

// Test with both domains
$testUrls = [
    'https://69shuba.cx/txt/44425/29853970',
    'https://69shuba.com/txt/44425/29853970'
];

foreach ($testUrls as $chapterUrl) {
    echo "=== Testing: $chapterUrl ===\n";
    
    try {
        $chapterData = $crawler->getChapterContent($chapterUrl);
        
        if (isset($chapterData['original_content'])) {
            $content = $chapterData['original_content'];
            $title = $chapterData['original_title'];
            
            echo "✓ Successfully extracted chapter content!\n";
            echo "Title: $title\n";
            echo "Content length: " . strlen($content) . " bytes\n";
            echo "Content length (UTF-8): " . mb_strlen($content, 'UTF-8') . " characters\n";
            echo "Character encoding: " . mb_detect_encoding($content) . "\n";
            echo "Is valid UTF-8: " . (mb_check_encoding($content, 'UTF-8') ? 'Yes' : 'No') . "\n";
            
            // Check for Chinese characters
            $chineseCharCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content);
            echo "Chinese character count: $chineseCharCount\n";
            
            if ($chineseCharCount > 0) {
                echo "✓ Chinese characters detected - extraction successful!\n";
                echo "\nFirst 500 characters:\n";
                echo "---\n";
                echo mb_substr($content, 0, 500, 'UTF-8') . "\n";
                echo "---\n";
                
                // Check for garbled characters
                $hasQuestionMarks = strpos($content, '?') !== false;
                $hasGarbledChars = preg_match('/[^\x20-\x7E\x{4E00}-\x{9FFF}\x{3000}-\x{303F}\x{FF00}-\x{FFEF}\s\n\r]/u', $content);
                
                echo "\nQuality check:\n";
                echo "Contains question marks: " . ($hasQuestionMarks ? 'Yes' : 'No') . "\n";
                echo "Contains garbled characters: " . ($hasGarbledChars ? 'Yes' : 'No') . "\n";
                
                if (!$hasGarbledChars && $chineseCharCount > 50) {
                    echo "✓ Content quality looks good!\n";
                    break; // Success, no need to test other URLs
                } else {
                    echo "⚠ Content quality issues detected\n";
                }
            } else {
                echo "✗ No Chinese characters found - extraction may have failed\n";
            }
        } else {
            echo "✗ No content in response\n";
            print_r($chapterData);
        }
    } catch (Exception $e) {
        echo "✗ Error: " . $e->getMessage() . "\n";
    }
    
    echo "\n";
}

// Test chapter list extraction too
echo "=== Testing Chapter List ===\n";
try {
    $chapters = $crawler->getChapterList('https://69shuba.cx/book/44425.htm');
    echo "Found " . count($chapters) . " chapters\n";
    
    if (!empty($chapters)) {
        echo "First 3 chapters:\n";
        for ($i = 0; $i < min(3, count($chapters)); $i++) {
            echo "Chapter " . ($i + 1) . ": " . $chapters[$i]['original_title'] . " - " . $chapters[$i]['chapter_url'] . "\n";
        }
    }
} catch (Exception $e) {
    echo "Chapter list error: " . $e->getMessage() . "\n";
}
?>
