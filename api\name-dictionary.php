<?php
/**
 * API Endpoint: Name Dictionary Management
 * GET /api/name-dictionary.php - Get all names for a novel
 * POST /api/name-dictionary.php - Add new name to dictionary
 * PUT /api/name-dictionary.php - Bulk update operations
 * DELETE /api/name-dictionary.php - Bulk delete operations
 */

require_once '../config/config.php';
require_once '../config/database.php';
require_once '../classes/TranslationService.php';
require_once '../classes/NovelManager.php';

header('Content-Type: application/json');

$method = $_SERVER['REQUEST_METHOD'];
$db = Database::getInstance();

try {
    switch ($method) {
        case 'GET':
            handleGetNames($db);
            break;
        case 'POST':
            handleAddName($db);
            break;
        case 'PUT':
            handleBulkUpdate($db);
            break;
        case 'DELETE':
            handleBulkDelete($db);
            break;
        default:
            jsonResponse(['success' => false, 'error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    error_log("Name Dictionary API Error: " . $e->getMessage());
    jsonResponse(['success' => false, 'error' => 'Internal server error'], 500);
}

function handleGetNames($db) {
    $novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;
    
    if (!$novelId) {
        jsonResponse(['success' => false, 'error' => 'Novel ID is required'], 400);
    }

    // Verify novel exists
    $novel = $db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
    if (!$novel) {
        jsonResponse(['success' => false, 'error' => 'Novel not found'], 404);
    }

    // Get all names for the novel
    $names = $db->fetchAll(
        "SELECT id, original_name, romanization, translation, name_type, frequency, 
                first_appearance_chapter, is_verified, created_at, updated_at
         FROM name_dictionary 
         WHERE novel_id = ? 
         ORDER BY frequency DESC, name_type ASC, original_name ASC",
        [$novelId]
    );

    // Get statistics
    $stats = [
        'total_names' => count($names),
        'by_type' => [],
        'translated_count' => 0,
        'romanized_count' => 0
    ];

    $typeCount = [];
    foreach ($names as $name) {
        // Count by type
        $type = $name['name_type'];
        $typeCount[$type] = ($typeCount[$type] ?? 0) + 1;
        
        // Count translated and romanized
        if (!empty($name['translation'])) {
            $stats['translated_count']++;
        }
        if (!empty($name['romanization'])) {
            $stats['romanized_count']++;
        }
    }
    $stats['by_type'] = $typeCount;

    jsonResponse([
        'success' => true,
        'data' => [
            'names' => $names,
            'statistics' => $stats
        ]
    ]);
}

function handleAddName($db) {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['success' => false, 'error' => 'Invalid JSON input'], 400);
    }

    $novelId = isset($input['novel_id']) ? (int)$input['novel_id'] : 0;
    $originalName = isset($input['original_name']) ? trim(sanitizeInput($input['original_name'])) : '';
    $romanization = isset($input['romanization']) ? trim(sanitizeInput($input['romanization'])) : '';
    $translation = isset($input['translation']) ? trim(sanitizeInput($input['translation'])) : '';
    $nameType = isset($input['name_type']) ? sanitizeInput($input['name_type']) : 'other';

    // Validate required fields
    if (!$novelId) {
        jsonResponse(['success' => false, 'error' => 'Novel ID is required'], 400);
    }

    if (empty($originalName)) {
        jsonResponse(['success' => false, 'error' => 'Original name is required'], 400);
    }

    // Verify novel exists
    $novel = $db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
    if (!$novel) {
        jsonResponse(['success' => false, 'error' => 'Novel not found'], 404);
    }

    // Validate name type
    $validTypes = ['character', 'location', 'organization', 'country', 'skill', 'monster', 'other', 'item', 'class/profession'];
    if (!in_array($nameType, $validTypes)) {
        jsonResponse(['success' => false, 'error' => 'Invalid name type'], 400);
    }

    // Check for duplicate names
    $existingName = $db->fetchOne(
        "SELECT id FROM name_dictionary WHERE novel_id = ? AND original_name = ?",
        [$novelId, $originalName]
    );

    if ($existingName) {
        jsonResponse(['success' => false, 'error' => 'A name with this original text already exists in the dictionary'], 400);
    }

    // Use NovelManager to add the name
    $novelManager = new NovelManager();
    $result = $novelManager->addNameToDictionary($novelId, [
        'original_name' => $originalName,
        'romanization' => !empty($romanization) ? $romanization : null,
        'translation' => !empty($translation) ? $translation : null,
        'name_type' => $nameType
    ]);

    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => [
                'message' => 'Name added successfully',
                'name_id' => $result['name_id']
            ]
        ]);
    } else {
        jsonResponse(['success' => false, 'error' => $result['error']], 400);
    }
}

function handleBulkUpdate($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['success' => false, 'error' => 'Invalid JSON input'], 400);
    }

    $novelId = isset($input['novel_id']) ? (int)$input['novel_id'] : 0;
    $action = isset($input['action']) ? sanitizeInput($input['action']) : '';
    $nameIds = isset($input['name_ids']) ? $input['name_ids'] : [];

    if (!$novelId || !$action || empty($nameIds)) {
        jsonResponse(['success' => false, 'error' => 'Missing required parameters'], 400);
    }

    // Verify novel exists
    $novel = $db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
    if (!$novel) {
        jsonResponse(['success' => false, 'error' => 'Novel not found'], 404);
    }

    // Sanitize name IDs
    $nameIds = array_map('intval', $nameIds);
    $nameIds = array_filter($nameIds, function($id) { return $id > 0; });

    if (empty($nameIds)) {
        jsonResponse(['success' => false, 'error' => 'No valid name IDs provided'], 400);
    }

    switch ($action) {
        case 'bulk_update_type':
            handleBulkUpdateType($db, $novelId, $nameIds, $input);
            break;
        case 'bulk_romanize':
            handleBulkRomanize($db, $novelId, $nameIds);
            break;
        default:
            jsonResponse(['success' => false, 'error' => 'Unknown action'], 400);
    }
}

function handleBulkUpdateType($db, $novelId, $nameIds, $input) {
    $nameType = isset($input['name_type']) ? sanitizeInput($input['name_type']) : '';
    
    $validTypes = ['character', 'location', 'organization', 'country', 'skill', 'monster', 'other', 'item', 'class/profession'];
    if (!in_array($nameType, $validTypes)) {
        jsonResponse(['success' => false, 'error' => 'Invalid name type'], 400);
    }

    // Build placeholders for IN clause
    $placeholders = str_repeat('?,', count($nameIds) - 1) . '?';
    $params = array_merge([$nameType, $novelId], $nameIds);

    $sql = "UPDATE name_dictionary 
            SET name_type = ?, updated_at = CURRENT_TIMESTAMP 
            WHERE novel_id = ? AND id IN ($placeholders)";

    $updatedCount = $db->execute($sql, $params);

    jsonResponse([
        'success' => true,
        'data' => [
            'updated_count' => $updatedCount,
            'message' => "Updated {$updatedCount} names to type '{$nameType}'"
        ]
    ]);
}

function handleBulkRomanize($db, $novelId, $nameIds) {
    // Get names that need romanization
    $placeholders = str_repeat('?,', count($nameIds) - 1) . '?';
    $params = array_merge([$novelId], $nameIds);

    $names = $db->fetchAll(
        "SELECT id, original_name FROM name_dictionary 
         WHERE novel_id = ? AND id IN ($placeholders) AND (romanization IS NULL OR romanization = '')",
        $params
    );

    if (empty($names)) {
        jsonResponse([
            'success' => true,
            'data' => [
                'processed_count' => 0,
                'message' => 'No names need romanization',
                'romanizations' => []
            ]
        ]);
    }

    $translationService = new TranslationService();
    $romanizations = [];
    $processedCount = 0;

    foreach ($names as $name) {
        try {
            $result = $translationService->romanizeText($name['original_name']);
            
            if ($result['success']) {
                $romanization = trim($result['romanized_text']);
                
                // Update the database
                $updated = $db->update(
                    'name_dictionary',
                    ['romanization' => $romanization, 'updated_at' => date('Y-m-d H:i:s')],
                    'id = ?',
                    [$name['id']]
                );

                if ($updated > 0) {
                    $romanizations[] = [
                        'id' => $name['id'],
                        'original_name' => $name['original_name'],
                        'romanization' => $romanization
                    ];
                    $processedCount++;
                }
            }
            
            // Add small delay to avoid overwhelming the API
            usleep(500000); // 0.5 second delay
            
        } catch (Exception $e) {
            error_log("Bulk romanization error for name ID {$name['id']}: " . $e->getMessage());
            continue;
        }
    }

    jsonResponse([
        'success' => true,
        'data' => [
            'processed_count' => $processedCount,
            'message' => "Generated romanization for {$processedCount} names",
            'romanizations' => $romanizations
        ]
    ]);
}

function handleBulkDelete($db) {
    $input = json_decode(file_get_contents('php://input'), true);
    
    if (!$input) {
        jsonResponse(['success' => false, 'error' => 'Invalid JSON input'], 400);
    }

    $novelId = isset($input['novel_id']) ? (int)$input['novel_id'] : 0;
    $nameIds = isset($input['name_ids']) ? $input['name_ids'] : [];

    if (!$novelId || empty($nameIds)) {
        jsonResponse(['success' => false, 'error' => 'Missing required parameters'], 400);
    }

    // Verify novel exists
    $novel = $db->fetchOne("SELECT id FROM novels WHERE id = ?", [$novelId]);
    if (!$novel) {
        jsonResponse(['success' => false, 'error' => 'Novel not found'], 404);
    }

    // Sanitize name IDs
    $nameIds = array_map('intval', $nameIds);
    $nameIds = array_filter($nameIds, function($id) { return $id > 0; });

    if (empty($nameIds)) {
        jsonResponse(['success' => false, 'error' => 'No valid name IDs provided'], 400);
    }

    // Build placeholders for IN clause
    $placeholders = str_repeat('?,', count($nameIds) - 1) . '?';
    $params = array_merge([$novelId], $nameIds);

    $sql = "DELETE FROM name_dictionary WHERE novel_id = ? AND id IN ($placeholders)";
    $deletedCount = $db->execute($sql, $params);

    jsonResponse([
        'success' => true,
        'data' => [
            'deleted_count' => $deletedCount,
            'message' => "Deleted {$deletedCount} names"
        ]
    ]);
}
?>
