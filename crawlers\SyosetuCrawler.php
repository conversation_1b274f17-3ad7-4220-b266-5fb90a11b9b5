<?php
/**
 * Syosetu (ncode.syosetu.com) Crawler
 * Novel Translation Application
 */

class SyosetuCrawler extends BaseCrawler {
    
    private const BASE_URL = 'https://ncode.syosetu.com';
    
    /**
     * Validate Syosetu URL
     */
    protected function validateUrl(string $url): bool {
        return preg_match('/ncode\.syosetu\.com\/n\w+/', $url) === 1;
    }
    
    /**
     * Get novel information from Syosetu
     */
    public function getNovelInfo(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Syosetu URL: {$url}");
        }
        
        $this->log("Fetching novel info from: {$url}");
        
        try {
            $html = $this->makeRequest($url);
            $dom = $this->parseHtml($html);
            
            // Extract novel information
            $title = $this->extractTitle($dom);
            $author = $this->extractAuthor($dom);
            $synopsis = $this->extractSynopsis($dom);
            $publishDate = $this->extractPublishDate($dom);
            $totalChapters = $this->extractTotalChapters($dom);
            
            $this->log("Successfully extracted novel info: {$title}");
            
            return [
                'platform' => 'syosetu',
                'url' => $url,
                'original_title' => $title,
                'author' => $author,
                'original_synopsis' => $synopsis,
                'publication_date' => $publishDate,
                'total_chapters' => $totalChapters,
                'language' => 'ja'
            ];
            
        } catch (Exception $e) {
            $this->log("Error fetching novel info: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Get chapter list from Syosetu
     */
    public function getChapterList(string $url): array {
        if (!$this->validateUrl($url)) {
            throw new Exception("Invalid Syosetu URL: {$url}");
        }

        $this->log("Fetching chapter list from: {$url}");

        try {
            $chapters = [];
            $currentPage = 1;
            $hasMorePages = true;

            // Extract all chapters from all pages
            while ($hasMorePages) {
                $pageUrl = $currentPage === 1 ? $url : $url . '?p=' . $currentPage;
                $this->log("Fetching page {$currentPage}: {$pageUrl}");

                $html = $this->makeRequest($pageUrl);
                $dom = $this->parseHtml($html);

                // Find chapter links on current page
                $pageChapters = $this->extractChaptersFromPage($dom);

                if (empty($pageChapters)) {
                    // No chapters found on this page, stop pagination
                    $hasMorePages = false;
                } else {
                    // Add chapters from this page
                    $chapters = array_merge($chapters, $pageChapters);

                    // Check if there's a next page
                    $hasMorePages = $this->hasNextPage($dom, $currentPage);
                    $currentPage++;

                    // Safety limit to prevent infinite loops
                    if ($currentPage > 100) {
                        $this->log("Reached maximum page limit (100), stopping pagination", 'warning');
                        break;
                    }
                }
            }

            // Re-number chapters sequentially
            foreach ($chapters as $index => &$chapter) {
                $chapter['chapter_number'] = $index + 1;
            }

            $this->log("Found " . count($chapters) . " chapters across {$currentPage} pages");

            return $chapters;

        } catch (Exception $e) {
            $this->log("Error fetching chapter list: " . $e->getMessage(), 'error');
            throw $e;
        }
    }

    /**
     * Extract chapters from a single page
     */
    private function extractChaptersFromPage(DOMDocument $dom): array {
        $chapters = [];

        // Try multiple selectors for chapter links
        $selectors = [
            '.p-eplist__sublist a',
            '.index_box a',
            '.novel_sublist2 a',
            '.p-eplist__episode a',
            '.episode-list a'
        ];

        $chapterElements = null;
        foreach ($selectors as $selector) {
            $chapterElements = $this->querySelectorAll($dom, $selector);
            if ($chapterElements->length > 0) {
                $this->log("Found chapters using selector: {$selector}");
                break;
            }
        }

        if (!$chapterElements || $chapterElements->length === 0) {
            return $chapters;
        }

        foreach ($chapterElements as $element) {
            $chapterUrl = $element->getAttribute('href');
            $chapterTitle = $this->cleanText($element->textContent);

            // Filter out non-chapter links (navigation, info pages, etc.)
            if ($chapterUrl && $chapterTitle &&
                !preg_match('/\?(p=|page=)/', $chapterUrl) && // Skip pagination links
                !preg_match('/\/info/', $chapterUrl) && // Skip info pages
                !preg_match('/^#/', $chapterUrl) && // Skip anchor links
                preg_match('/\/n\w+\/\d+\/?$/', $chapterUrl)) { // Must be chapter format

                $fullChapterUrl = $this->normalizeUrl($chapterUrl, self::BASE_URL);

                $chapters[] = [
                    'chapter_number' => 0, // Will be renumbered later
                    'chapter_url' => $fullChapterUrl,
                    'original_title' => $chapterTitle
                ];
            }
        }

        return $chapters;
    }

    /**
     * Check if there's a next page
     */
    private function hasNextPage(DOMDocument $dom, int $currentPage): bool {
        // Look for pagination indicators
        $paginationSelectors = [
            '.p-pager__item--next a',
            '.pager-next a',
            '.next a',
            'a[href*="?p=' . ($currentPage + 1) . '"]'
        ];

        foreach ($paginationSelectors as $selector) {
            $nextLink = $this->querySelector($dom, $selector);
            if ($nextLink) {
                $this->log("Found next page link using selector: {$selector}");
                return true;
            }
        }

        // Also check for page numbers
        $pageLinks = $this->querySelectorAll($dom, 'a[href*="?p="]');
        foreach ($pageLinks as $link) {
            $href = $link->getAttribute('href');
            if (preg_match('/\?p=(\d+)/', $href, $matches)) {
                $pageNum = (int)$matches[1];
                if ($pageNum > $currentPage) {
                    $this->log("Found higher page number: {$pageNum}");
                    return true;
                }
            }
        }

        return false;
    }
    
    /**
     * Get chapter content from Syosetu
     */
    public function getChapterContent(string $chapterUrl): array {
        $this->log("Fetching chapter content from: {$chapterUrl}");

        try {
            $html = $this->makeRequest($chapterUrl);
            $dom = $this->parseHtml($html);

            // Extract chapter title with multiple fallback selectors
            $title = $this->extractChapterTitle($dom);

            // Extract chapter content with multiple fallback selectors
            $contentElement = $this->findContentElement($dom);
            if (!$contentElement) {
                throw new Exception("Chapter content not found");
            }

            $content = $this->extractChapterText($contentElement);

            $this->log("Successfully extracted chapter content");

            return [
                'original_title' => $title,
                'original_content' => $content,
                'word_count' => mb_strlen($content)
            ];

        } catch (Exception $e) {
            $this->log("Error fetching chapter content: " . $e->getMessage(), 'error');
            throw $e;
        }
    }
    
    /**
     * Extract novel title
     */
    private function extractTitle(DOMDocument $dom): string {
        $titleElement = $this->querySelector($dom, '.novel_title');
        if (!$titleElement) {
            $titleElement = $this->querySelector($dom, 'h1');
        }
        
        return $titleElement ? $this->cleanText($titleElement->textContent) : '';
    }
    
    /**
     * Extract author name
     */
    private function extractAuthor(DOMDocument $dom): string {
        $authorSelectors = [
            '.novel_writername a',
            '.novel_writername',
            '.p-novel__author a',
            '.p-novel__author',
            '.author-name',
            '.writer-name',
            'a[href*="/user/"]'
        ];

        foreach ($authorSelectors as $selector) {
            $authorElement = $this->querySelector($dom, $selector);
            if ($authorElement) {
                $authorText = $this->cleanText($authorElement->textContent);
                if (!empty($authorText)) {
                    $this->log("Author extracted using selector '{$selector}': {$authorText}");
                    return $authorText;
                }
            }
        }

        $this->log("Author extraction failed - no matching elements found", 'warning');
        return '';
    }
    
    /**
     * Extract synopsis
     */
    private function extractSynopsis(DOMDocument $dom): string {
        $synopsisSelectors = [
            '#novel_ex',
            '.novel_ex',
            '.p-novel__summary',
            '.novel-summary',
            '.summary',
            '.description',
            '.intro',
            '.synopsis'
        ];

        foreach ($synopsisSelectors as $selector) {
            $synopsisElement = $this->querySelector($dom, $selector);
            if ($synopsisElement) {
                $synopsisText = $this->cleanText($synopsisElement->textContent);
                if (!empty($synopsisText) && strlen($synopsisText) > 10) {
                    $this->log("Synopsis extracted using selector '{$selector}': " . substr($synopsisText, 0, 100) . "...");
                    return $synopsisText;
                }
            }
        }

        $this->log("Synopsis extraction failed - no matching elements found", 'warning');
        return '';
    }
    
    /**
     * Extract publication date
     */
    private function extractPublishDate(DOMDocument $dom): ?string {
        // First try to extract from the first chapter's publication date
        $firstChapterDate = $this->extractFirstChapterDate($dom);
        if ($firstChapterDate) {
            $this->log("Publication date extracted from first chapter: {$firstChapterDate}");
            return $firstChapterDate;
        }

        // Fallback to traditional selectors
        $dateSelectors = [
            '.novel_update',
            '.p-novel__date',
            '.novel-date',
            '.publish-date',
            '.created-date',
            '.date'
        ];

        foreach ($dateSelectors as $selector) {
            $dateElements = $this->querySelectorAll($dom, $selector);

            foreach ($dateElements as $element) {
                $dateText = $this->cleanText($element->textContent);
                $date = $this->extractDate($dateText);
                if ($date) {
                    $this->log("Publication date extracted using selector '{$selector}': {$date}");
                    return $date;
                }
            }
        }

        // Try to find date in meta tags
        $metaElements = $this->querySelectorAll($dom, 'meta[property*="date"], meta[name*="date"]');
        foreach ($metaElements as $element) {
            $content = $element->getAttribute('content');
            if ($content) {
                $date = $this->extractDate($content);
                if ($date) {
                    $this->log("Publication date extracted from meta tag: {$date}");
                    return $date;
                }
            }
        }

        $this->log("Publication date extraction failed - no matching elements found", 'warning');
        return null;
    }

    /**
     * Extract publication date from the first chapter in the chapter list
     */
    private function extractFirstChapterDate(DOMDocument $dom): ?string {
        // Look for chapter links with dates
        $chapterSelectors = [
            'a[href*="/n4830bu/"]', // Specific to this novel format
            'a[href*="/n"][href*="bu/"]', // General Syosetu chapter pattern
            '.p-eplist__sublist a',
            '.index_box a',
            '.novel_sublist2 a'
        ];

        foreach ($chapterSelectors as $selector) {
            $chapterElements = $this->querySelectorAll($dom, $selector);

            if ($chapterElements->length > 0) {
                // Get the first chapter element
                $firstChapter = $chapterElements->item(0);

                // Look for date text near the chapter link
                $parent = $firstChapter->parentNode;
                if ($parent) {
                    $dateText = $this->cleanText($parent->textContent);

                    // Extract date from the text that contains both chapter title and date
                    $date = $this->extractDate($dateText);
                    if ($date) {
                        return $date;
                    }
                }

                // Also check the next sibling for date information
                $nextSibling = $firstChapter->nextSibling;
                while ($nextSibling) {
                    if ($nextSibling->nodeType === XML_TEXT_NODE || $nextSibling->nodeType === XML_ELEMENT_NODE) {
                        $dateText = $this->cleanText($nextSibling->textContent);
                        $date = $this->extractDate($dateText);
                        if ($date) {
                            return $date;
                        }
                    }
                    $nextSibling = $nextSibling->nextSibling;

                    // Only check a few siblings to avoid going too far
                    if (!$nextSibling || $nextSibling->nodeType === XML_ELEMENT_NODE) {
                        break;
                    }
                }
            }
        }

        // Try to find dates in the entire page content by looking for the Syosetu date pattern
        $xpath = new DOMXPath($dom);
        $textNodes = $xpath->query('//text()[contains(., "/")]');

        foreach ($textNodes as $textNode) {
            $text = $textNode->textContent;
            // Look for Syosetu's specific date format: YYYY/MM/DD HH:MM
            if (preg_match('/\d{4}\/\d{1,2}\/\d{1,2}\s+\d{1,2}:\d{2}/', $text)) {
                $date = $this->extractDate($text);
                if ($date) {
                    return $date;
                }
            }
        }

        return null;
    }
    
    /**
     * Extract total chapters
     */
    private function extractTotalChapters(DOMDocument $dom): int {
        // Try to find chapter count in various places
        $countElements = $this->querySelectorAll($dom, '.index_box');
        if ($countElements->length > 0) {
            return $countElements->length;
        }
        
        // Alternative: count from navigation
        $navElements = $this->querySelectorAll($dom, '.novel_sublist2');
        if ($navElements->length > 0) {
            return $navElements->length;
        }
        
        // Try to extract from text
        $allText = $dom->textContent;
        if (preg_match('/全(\d+)話/', $allText, $matches)) {
            return (int) $matches[1];
        }
        
        return 0;
    }
    
    /**
     * Extract chapter title with fallback selectors
     */
    private function extractChapterTitle(DOMDocument $dom): string {
        $titleSelectors = [
            '.p-novel__title',
            '.p-novel__number',
            '.novel_subtitle',
            'h1',
            '.chapter-title'
        ];

        foreach ($titleSelectors as $selector) {
            $titleElement = $this->querySelector($dom, $selector);
            if ($titleElement) {
                $title = $this->cleanText($titleElement->textContent);
                if (!empty($title)) {
                    return $title;
                }
            }
        }

        return '';
    }

    /**
     * Find content element with fallback selectors
     */
    private function findContentElement(DOMDocument $dom): ?DOMElement {
        $contentSelectors = [
            '.p-novel__body',
            '#novel_honbun',
            '.novel_view',
            '.novel_body',
            '#novel_color',
            '.chapter-content',
            '.content'
        ];

        foreach ($contentSelectors as $selector) {
            $contentElement = $this->querySelector($dom, $selector);
            if ($contentElement) {
                // Check if element has meaningful content
                $text = trim($contentElement->textContent);
                if (!empty($text) && strlen($text) > 50) {
                    return $contentElement;
                }
            }
        }

        return null;
    }

    /**
     * Extract chapter text content
     */
    private function extractChapterText(DOMElement $contentElement): string {
        // Use the furigana-aware text extraction
        $content = $this->getTextContentWithFurigana($contentElement);

        // Clean up the content while preserving furigana
        $content = $this->cleanTextWithFurigana($content);
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        $content = trim($content);

        return $content;
    }

    /**
     * Clean text while preserving furigana markup and paragraph structure
     */
    private function cleanTextWithFurigana(string $text): string {
        // Remove HTML entities first
        $text = html_entity_decode($text, ENT_QUOTES | ENT_HTML5, 'UTF-8');

        // Clean up furigana markup spacing
        $text = preg_replace('/\s*\{\s*([^|]+)\s*\|\s*([^}]+)\s*\}\s*/', '{$1|$2}', $text);

        // Preserve paragraph breaks while cleaning up other whitespace
        // Split by paragraph breaks (double newlines), clean each paragraph, then rejoin
        $paragraphs = preg_split('/\n\s*\n/', $text);
        $cleanedParagraphs = [];

        foreach ($paragraphs as $paragraph) {
            // Clean whitespace within each paragraph (but preserve single newlines)
            $paragraph = preg_replace('/[ \t]+/', ' ', $paragraph); // Only collapse spaces and tabs
            $paragraph = preg_replace('/\n[ \t]*\n/', "\n", $paragraph); // Clean up line breaks with spaces
            $paragraph = trim($paragraph);

            if (!empty($paragraph)) {
                $cleanedParagraphs[] = $paragraph;
            }
        }

        // Rejoin paragraphs with double newlines
        $text = implode("\n\n", $cleanedParagraphs);

        return $text;
    }
}
