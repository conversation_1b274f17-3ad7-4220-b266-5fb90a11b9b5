<?php
/**
 * Furigana API Endpoint
 * Handles furigana-related operations
 */

require_once '../config/config.php';
require_once '../classes/FuriganaService.php';

header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $furiganaService = new FuriganaService();
    $method = $_SERVER['REQUEST_METHOD'];
    $input = json_decode(file_get_contents('php://input'), true);
    
    switch ($method) {
        case 'GET':
            handleGet($furiganaService);
            break;
            
        case 'POST':
            handlePost($furiganaService, $input);
            break;
            
        case 'PUT':
            handlePut($furiganaService, $input);
            break;
            
        default:
            http_response_code(405);
            echo json_encode(['error' => 'Method not allowed']);
            break;
    }
    
} catch (Exception $e) {
    http_response_code(500);
    echo json_encode(['error' => $e->getMessage()]);
}

/**
 * Handle GET requests
 */
function handleGet($furiganaService) {
    if (isset($_GET['novel_id'])) {
        $novelId = (int)$_GET['novel_id'];
        
        if (isset($_GET['action']) && $_GET['action'] === 'dictionary') {
            // Get furigana dictionary for novel
            $dictionary = $furiganaService->getFuriganaDictionary($novelId);
            echo json_encode([
                'success' => true,
                'dictionary' => $dictionary
            ]);
        } else {
            // Get furigana statistics for novel
            $db = Database::getInstance();
            $stats = $db->fetchOne(
                "SELECT 
                    COUNT(*) as total_chapters,
                    COUNT(CASE WHEN furigana_processing_status = 'processed' THEN 1 END) as processed_chapters,
                    SUM(furigana_count) as total_furigana
                 FROM chapters WHERE novel_id = ?",
                [$novelId]
            );
            
            echo json_encode([
                'success' => true,
                'stats' => $stats
            ]);
        }
    } else {
        http_response_code(400);
        echo json_encode(['error' => 'Novel ID required']);
    }
}

/**
 * Handle POST requests
 */
function handlePost($furiganaService, $input) {
    if (!isset($input['action'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Action required']);
        return;
    }
    
    switch ($input['action']) {
        case 'process_chapter':
            if (!isset($input['chapter_id']) || !isset($input['text_with_furigana'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Chapter ID and text required']);
                return;
            }
            
            $result = $furiganaService->processChapterFurigana(
                (int)$input['chapter_id'],
                $input['text_with_furigana']
            );
            
            echo json_encode($result);
            break;
            
        case 'convert_format':
            if (!isset($input['text']) || !isset($input['format'])) {
                http_response_code(400);
                echo json_encode(['error' => 'Text and format required']);
                return;
            }
            
            $text = $input['text'];
            $format = $input['format'];
            
            switch ($format) {
                case 'html':
                    $converted = $furiganaService->convertToHtmlRuby($text);
                    break;
                case 'parentheses':
                    $converted = $furiganaService->convertToParentheses($text);
                    break;
                case 'remove':
                    $converted = $furiganaService->removeFurigana($text);
                    break;
                default:
                    $converted = $text;
                    break;
            }
            
            echo json_encode([
                'success' => true,
                'original_text' => $text,
                'converted_text' => $converted,
                'format' => $format
            ]);
            break;
            
        default:
            http_response_code(400);
            echo json_encode(['error' => 'Unknown action']);
            break;
    }
}

/**
 * Handle PUT requests
 */
function handlePut($furiganaService, $input) {
    if (!isset($input['novel_id']) || !isset($input['kanji_text']) || 
        !isset($input['furigana_text']) || !isset($input['preference'])) {
        http_response_code(400);
        echo json_encode(['error' => 'Novel ID, kanji text, furigana text, and preference required']);
        return;
    }
    
    $success = $furiganaService->updateFuriganaPreference(
        (int)$input['novel_id'],
        $input['kanji_text'],
        $input['furigana_text'],
        $input['preference']
    );
    
    if ($success) {
        echo json_encode([
            'success' => true,
            'message' => 'Furigana preference updated successfully'
        ]);
    } else {
        http_response_code(500);
        echo json_encode(['error' => 'Failed to update furigana preference']);
    }
}
?>
