# Word Export Troubleshooting Guide

## Overview
This guide helps troubleshoot issues with the Word export functionality in the Novel Translation Application.

## NEW: Simple Export Option
We've added a **Simple Export** option that provides better compatibility with various Word processors. This version:
- Limits content size for better compatibility
- Uses simplified formatting
- Has better XML validation
- Works with older Word versions

**Recommendation: Try the Simple Export first if you're having issues with file compatibility.**

## Common Issues and Solutions

### 1. "File tidak bisa dibuka" (File cannot be opened)

**Possible Causes:**
- Corrupted file generation
- Encoding issues
- Missing PHP extensions
- Browser download issues

**Solutions:**

#### A. Check PHP Extensions
Ensure these PHP extensions are installed and enabled:
```bash
php -m | grep -E "(zip|xml|dom|xmlwriter)"
```

Required extensions:
- `zip` - For creating DOCX files (ZIP-based format)
- `xml` - For XML processing
- `dom` - For DOM manipulation
- `xmlwriter` - For writing XML content

#### B. Check File Generation
1. Look at the error logs in `logs/error.log`
2. Check if temp directory exists and is writable: `temp/`
3. Verify PHPWord is properly installed in `vendor/phpoffice/phpword/`

#### C. Browser Issues
- Try downloading with a different browser
- Clear browser cache and cookies
- Disable browser extensions that might interfere with downloads

#### D. File Corruption Check
If you can download the file but can't open it:
1. Check file size - should be > 7000 bytes typically
2. Try opening with different Word processors (LibreOffice, Google Docs)
3. Rename file extension from `.docx` to `.zip` and try to extract

### 2. Empty or Invalid Files

**Check these items:**
- Ensure the chapter has translated content
- Verify database connection is working
- Check temp directory permissions (should be writable)

### 3. Character Encoding Issues

**Symptoms:**
- Garbled text in the Word document
- Special characters not displaying correctly

**Solutions:**
- The system now includes automatic UTF-8 encoding handling
- HTML entities are automatically decoded
- Control characters are stripped

## Testing the Export Feature

### Prerequisites
1. Have at least one novel in your database
2. Have at least one chapter with translated content
3. Access to the chapter view page

### Steps to Test
1. Go to `http://localhost/wc`
2. Navigate to a novel with translated chapters
3. Open a chapter that has been translated
4. Look for the "Export Word" button (blue button with Word icon and dropdown)
5. **Try Simple Export first**: Click the main button or select "Simple Export (Recommended)" from dropdown
6. If Simple Export doesn't work, try "Full Export (Advanced)" from the dropdown
7. Try opening the downloaded file in Microsoft Word or LibreOffice

### Export Options Available:
- **Simple Export (Recommended)**: Limited content size, better compatibility
- **Full Export (Advanced)**: Complete content, advanced formatting

### Manual Verification
If automatic testing fails, you can manually verify:

1. **Check Database:**
```sql
SELECT n.original_title, n.translated_title, c.chapter_number, 
       LENGTH(c.translated_content) as content_length
FROM novels n 
JOIN chapters c ON n.id = c.novel_id 
WHERE c.translated_content IS NOT NULL 
  AND c.translated_content != ''
LIMIT 5;
```

2. **Check File Generation:**
- Navigate to `temp/` directory
- Look for generated `.docx` files
- Check file sizes and timestamps

## Technical Details

### File Structure
Generated DOCX files contain:
- Novel title (original and translated)
- Chapter number and title
- Formatted translated content
- Proper document metadata

### Compatibility
- Compatible with Microsoft Word 2007+
- Compatible with LibreOffice Writer
- Compatible with Google Docs (upload)
- Compatible with most modern word processors

### File Naming Convention
Files are named as: `[NovelTitle]_Chapter[Number].docx`
- Special characters are removed for filesystem compatibility
- Titles are truncated to 50 characters if too long

## Error Codes and Messages

### Common Error Messages:
- `"Novel not found"` - Invalid novel ID
- `"Chapter not found"` - Invalid chapter number
- `"Chapter has no translated content to export"` - Chapter not translated
- `"Failed to generate Word document"` - File generation error
- `"Generated Word document is empty"` - Empty file created

### HTTP Status Codes:
- `400` - Bad request (missing parameters or no content)
- `404` - Novel or chapter not found
- `500` - Server error during file generation

## Logs and Debugging

### Check Error Logs:
```bash
tail -f logs/error.log
```

### Enable Debug Mode:
The system automatically logs export attempts with details:
- Request parameters
- File generation status
- File sizes
- Error messages

## Performance Considerations

### File Size Limits:
- No hard limit on content length
- Typical file sizes: 7-50 KB for normal chapters
- Large chapters (>100KB content) may take longer to process

### Timeout Settings:
- Default PHP execution time should be sufficient
- For very large chapters, consider increasing `max_execution_time`

## Support

If issues persist after following this guide:
1. Check the error logs for specific error messages
2. Verify all PHP extensions are installed
3. Test with a simple chapter first
4. Ensure the database contains translated content

## Version Information
- PHPWord Version: Check `vendor/phpoffice/phpword/composer.json`
- PHP Version: Requires PHP 7.1+
- Required Extensions: zip, xml, dom, xmlwriter
