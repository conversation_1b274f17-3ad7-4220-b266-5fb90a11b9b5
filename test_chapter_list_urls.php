<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$novelUrl = 'https://69shuba.cx/book/44425.htm';

echo "=== Testing Chapter List URLs ===\n";
echo "Novel URL: $novelUrl\n\n";

try {
    $chapters = $crawler->getChapterList($novelUrl);
    
    echo "Found " . count($chapters) . " chapters\n\n";
    
    if (!empty($chapters)) {
        echo "First 5 chapters:\n";
        for ($i = 0; $i < min(5, count($chapters)); $i++) {
            $chapter = $chapters[$i];
            echo "Chapter " . ($i + 1) . ":\n";
            echo "  Title: " . $chapter['original_title'] . "\n";
            echo "  URL: " . $chapter['chapter_url'] . "\n";
            echo "\n";
        }
        
        // Test the first chapter with the correct URL
        $firstChapter = $chapters[0];
        echo "=== Testing First Chapter Content ===\n";
        echo "URL: " . $firstChapter['chapter_url'] . "\n";
        
        // Get raw HTML from the correct URL
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $firstChapter['chapter_url']);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_ENCODING, '');
        
        $rawHtml = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        curl_close($ch);
        
        echo "HTTP Code: $httpCode\n";
        echo "HTML Length: " . strlen($rawHtml) . "\n";
        
        if ($httpCode == 200 && !empty($rawHtml)) {
            // Convert encoding
            $html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
            $html = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $html);
            
            $dom = new DOMDocument();
            libxml_use_internal_errors(true);
            $dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
            libxml_clear_errors();
            
            $allText = $dom->textContent;
            
            echo "Text length: " . strlen($allText) . "\n";
            echo "Chinese chars: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $allText) . "\n";
            
            // Check for expected content
            $hasNovelTitle = strpos($allText, '从零开始缔造游戏帝国') !== false;
            $hasChapterTitle = strpos($allText, '第1章') !== false && strpos($allText, '怒气') !== false;
            
            echo "Has novel title: " . ($hasNovelTitle ? 'Yes' : 'No') . "\n";
            echo "Has chapter title: " . ($hasChapterTitle ? 'Yes' : 'No') . "\n";
            
            if ($hasNovelTitle && $hasChapterTitle) {
                echo "✓ Correct chapter page found!\n";
                
                // Try to extract content
                if (preg_match('/第1章.*?怒气.*?(\d{4}-\d{1,2}-\d{1,2}).*?第1章.*?怒气(.+?)(?:上一章|下一章|目录|Copyright|69书吧|$)/s', $allText, $matches)) {
                    $content = trim($matches[2]);
                    echo "Story content length: " . strlen($content) . "\n";
                    echo "Chinese chars in story: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content) . "\n";
                    
                    if (preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $content) > 50) {
                        echo "First 1000 characters:\n";
                        echo "---\n";
                        echo mb_substr($content, 0, 1000, 'UTF-8') . "\n";
                        echo "---\n";
                    }
                } else {
                    echo "Content extraction pattern failed\n";
                    echo "Sample text around chapter title:\n";
                    $pos = strpos($allText, '第1章');
                    if ($pos !== false) {
                        echo mb_substr($allText, max(0, $pos - 100), 1000, 'UTF-8') . "\n";
                    }
                }
            } else {
                echo "✗ Wrong chapter page\n";
                echo "Sample text (first 1000 chars):\n";
                echo mb_substr($allText, 0, 1000, 'UTF-8') . "\n";
            }
        } else {
            echo "Failed to fetch chapter content\n";
        }
    }
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
