# Manual Name Addition Feature Guide

## Overview

The Manual Name Addition feature allows users to manually add custom names to the name dictionary in the novel translation application. This feature provides a simple, database-like interface for adding names that will be immediately available for use in translations.

## Features

### ✅ **Simple Form Interface**
- Clean, database-like styling following user preferences
- Collapsible form to save space
- Clear field labels and validation

### ✅ **Comprehensive Form Fields**
- **Original Name** (required): The name in its original language
- **Romanization** (optional): Romanized version of the name
- **Translation** (optional): English translation of the name
- **Name Type** (required): Classification (character, location, organization, country, skill, monster, other)

### ✅ **Validation & Duplicate Prevention**
- Prevents duplicate entries based on original name
- Client-side and server-side validation
- Real-time feedback for errors

### ✅ **Seamless Integration**
- Immediate availability in translation system
- Follows established priority order: translated version > romanized form > original form
- Integrates with existing name dictionary management interface

## How to Use

### 1. Access the Feature
1. Navigate to any novel's name dictionary management page
2. Look for the "Add New Name" card at the top of the page
3. Click "Show Form" to expand the form interface

### 2. Add a New Name
1. **Original Name**: Enter the name in its original language (required)
2. **Romanization**: Enter the romanized version (optional)
3. **Translation**: Enter the English translation (optional)
4. **Type**: Select the appropriate classification (required)
5. Click "Add" to submit

### 3. Form Behavior
- Form validates required fields before submission
- Shows loading state during submission
- Displays success/error messages
- Automatically collapses and clears form on success
- Immediately refreshes the name dictionary list

## Technical Implementation

### API Endpoint
- **Method**: POST
- **URL**: `/api/name-dictionary.php`
- **Content-Type**: `application/json`

### Request Format
```json
{
    "novel_id": 1,
    "original_name": "テスト名前",
    "romanization": "Tesuto Namae",
    "translation": "Test Name",
    "name_type": "character"
}
```

### Response Format
```json
{
    "success": true,
    "data": {
        "message": "Name added successfully",
        "name_id": 123
    }
}
```

### Database Integration
- Names are stored in the `name_dictionary` table
- Manually added names have `frequency = 1` and `is_verified = true`
- `first_appearance_chapter` is set to `null` for manually added names
- Follows existing database schema and constraints

## Validation Rules

### Client-Side Validation
- Original name is required and cannot be empty
- Name type must be selected
- Checks for duplicates in local data before submission

### Server-Side Validation
- Novel ID must be valid and exist
- Original name is required and cannot be empty
- Name type must be one of the valid options
- Prevents duplicate names per novel
- Sanitizes all input data

## Integration with Translation System

### Priority Order
When translating text, the system uses names in this priority order:
1. **Translation** (if available)
2. **Romanization** (if available)
3. **Original form** (fallback)

### Immediate Availability
- Names added manually are immediately available for use in translations
- No need to restart or refresh the translation system
- Integrates seamlessly with existing name dictionary functionality

## User Interface Design

### Styling
- Follows user preference for simple, database-like table interfaces
- Minimal styling with basic borders and standard appearance
- No modern web styling (gradients, shadows, animations)
- Clean, functional design similar to phpMyAdmin

### Form Layout
- Responsive design that works on different screen sizes
- Clear field labels with required field indicators
- Helpful form text for guidance
- Collapsible interface to save space

## Error Handling

### Common Errors
- **Duplicate Name**: "A name with this original text already exists in the dictionary"
- **Missing Required Fields**: "Original name is required" / "Name type is required"
- **Invalid Novel**: "Novel not found"
- **Network Errors**: Handled gracefully with user-friendly messages

### Error Display
- Toast notifications for user feedback
- Form field focus for validation errors
- Clear error messages without technical jargon

## Best Practices

### When to Use Manual Addition
- Adding character names not detected by AI
- Pre-populating important location names
- Adding organization or skill names
- Correcting missed names from automatic extraction

### Naming Conventions
- Use consistent romanization standards
- Provide translations when possible
- Choose appropriate name types for better organization
- Verify spelling before submission

## Troubleshooting

### Form Not Submitting
1. Check that all required fields are filled
2. Verify network connection
3. Check browser console for JavaScript errors
4. Ensure novel ID is valid

### Duplicate Error When Name Doesn't Exist
1. Check if name exists with different capitalization
2. Verify you're in the correct novel's dictionary
3. Refresh the page to reload current data

### Names Not Appearing in Translations
1. Verify the name was added successfully
2. Check that translation system is using the correct novel ID
3. Ensure name dictionary integration is enabled

## Future Enhancements

### Potential Improvements
- Bulk import from CSV files
- Name suggestion based on existing patterns
- Integration with external name databases
- Advanced search and filtering in the form
- Name validation against common dictionaries

---

## Summary

The Manual Name Addition feature provides a comprehensive solution for adding custom names to the novel translation system. It combines user-friendly interface design with robust validation and seamless integration, ensuring that manually added names are immediately available for use in translations while maintaining data integrity and following established user preferences.
