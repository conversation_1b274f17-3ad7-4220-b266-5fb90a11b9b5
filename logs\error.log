[2025-06-07 09:25:52] Word Export Request | Context: {"novel_id":1,"chapter":149,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 09:25:52] Word Export Success | Context: {"novel_id":1,"chapter":149,"file_size":18389,"filename":"A_Middle-Aged_Sages_Otherworldly_Daily_Records_Chapter149.docx"}
[2025-06-07 09:27:00] Word Export Request | Context: {"novel_id":1,"chapter":149,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 09:27:00] Word Export Success | Context: {"novel_id":1,"chapter":149,"file_size":18389,"filename":"A_Middle-Aged_Sages_Otherworldly_Daily_Records_Chapter149.docx"}
[2025-06-07 09:43:20] Word Export Request | Context: {"novel_id":3,"chapter":12,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 09:43:20] Word Export Success | Context: {"novel_id":3,"chapter":12,"file_size":11633,"filename":"The_Unwittingly_Strongest_Pharmacist_Chapter12.docx"}
[2025-06-07 09:55:44] Word Export Request | Context: {"novel_id":3,"chapter":12,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 09:55:44] Word Export Success | Context: {"novel_id":3,"chapter":12,"file_size":11623,"filename":"The_Unwittingly_Strongest_Pharmacist_Chapter12.docx"}
[2025-06-07 10:12:10] Simple Word Export Request | Context: {"novel_id":1,"chapter":149}
[2025-06-07 10:12:10] Simple Word Export Success | Context: {"novel_id":1,"chapter":149,"file_size":18534,"filename":"Simple_A_Middle-Aged_Sages_Otherworldly_Daily_Records_Chapter149.docx"}
[2025-06-07 10:32:18] Word Export Request | Context: {"novel_id":1,"chapter":149,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 10:32:18] Word Export Success | Context: {"novel_id":1,"chapter":149,"file_size":18425,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter149.docx"}
[2025-06-07 11:05:37] Word Export Request | Context: {"novel_id":1,"chapter":149,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 11:05:37] Word Export Success | Context: {"novel_id":1,"chapter":149,"file_size":18452,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter149.docx"}
[2025-06-07 11:17:41] Chapter Chunks API: GET request received | Context: {"chapter_id":151,"method":"GET"}
[2025-06-07 11:17:53] Chapter Chunks API: Translation request received | Context: {"chunk_id":8,"target_language":"en","raw_input":"{\"chunk_id\":8,\"target_language\":\"en\"}"}
[2025-06-07 11:20:08] Chapter Chunks API: GET request received | Context: {"chapter_id":151,"method":"GET"}
[2025-06-07 11:20:47] Chapter Chunks API: Translation request received | Context: {"chunk_id":9,"target_language":"en","raw_input":"{\"chunk_id\":9,\"target_language\":\"en\"}"}
[2025-06-07 11:23:07] Chapter Chunks API: GET request received | Context: {"chapter_id":151,"method":"GET"}
[2025-06-07 12:09:33] Word Export Request | Context: {"novel_id":1,"chapter":150,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 12:09:33] Word Export Success | Context: {"novel_id":1,"chapter":150,"file_size":20957,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter150.docx"}
[2025-06-07 13:06:36] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:06:41] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:06:48] Chapter Chunks API: Translation request received | Context: {"chunk_id":4,"target_language":"en","raw_input":"{\"chunk_id\":4,\"target_language\":\"en\"}"}
[2025-06-07 13:09:28] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:09:37] Chapter Chunks API: Translation request received | Context: {"chunk_id":5,"target_language":"en","raw_input":"{\"chunk_id\":5,\"target_language\":\"en\"}"}
[2025-06-07 13:09:46] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:09:52] Chapter Chunks API: Translation request received | Context: {"chunk_id":6,"target_language":"en","raw_input":"{\"chunk_id\":6,\"target_language\":\"en\"}"}
[2025-06-07 13:11:42] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:11:52] Chapter Chunks API: Translation request received | Context: {"chunk_id":7,"target_language":"en","raw_input":"{\"chunk_id\":7,\"target_language\":\"en\"}"}
[2025-06-07 13:13:14] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:13:58] Word Export Request | Context: {"novel_id":1,"chapter":150,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 13:13:58] Word Export Success | Context: {"novel_id":1,"chapter":150,"file_size":20916,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter150.docx"}
[2025-06-07 13:55:57] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:56:04] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:56:32] Chapter Chunks API: Translation request received | Context: {"chunk_id":4,"target_language":"en","raw_input":"{\"chunk_id\":4,\"target_language\":\"en\"}"}
[2025-06-07 13:59:15] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:59:24] Chapter Chunks API: Translation request received | Context: {"chunk_id":5,"target_language":"en","raw_input":"{\"chunk_id\":5,\"target_language\":\"en\"}"}
[2025-06-07 13:59:39] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 13:59:48] Chapter Chunks API: Translation request received | Context: {"chunk_id":6,"target_language":"en","raw_input":"{\"chunk_id\":6,\"target_language\":\"en\"}"}
[2025-06-07 14:01:55] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 14:02:03] Chapter Chunks API: Translation request received | Context: {"chunk_id":7,"target_language":"en","raw_input":"{\"chunk_id\":7,\"target_language\":\"en\"}"}
[2025-06-07 14:03:17] Chapter Chunks API: GET request received | Context: {"chapter_id":150,"method":"GET"}
[2025-06-07 14:04:47] Word Export Request | Context: {"novel_id":1,"chapter":150,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 14:04:48] Word Export Success | Context: {"novel_id":1,"chapter":150,"file_size":20713,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter150.docx"}
[2025-06-07 14:26:57] Word Export Request | Context: {"novel_id":1,"chapter":150,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/136.0.0.0 Safari\/537.36"}
[2025-06-07 14:26:57] Word Export Success | Context: {"novel_id":1,"chapter":150,"file_size":20713,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter150.docx"}
[2025-06-08 02:07:54] Word Export Request | Context: {"novel_id":5,"chapter":1,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-08 02:07:54] Word Export Success | Context: {"novel_id":5,"chapter":1,"file_size":10954,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter1.docx"}
[2025-06-08 02:30:03] Word Export Request | Context: {"novel_id":5,"chapter":2,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-08 02:30:03] Word Export Success | Context: {"novel_id":5,"chapter":2,"file_size":13526,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter2.docx"}
[2025-06-08 03:36:52] Word Export Request | Context: {"novel_id":5,"chapter":2,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-08 03:36:52] Word Export Success | Context: {"novel_id":5,"chapter":2,"file_size":13398,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter2.docx"}
[2025-06-08 06:54:32] Word Export Request | Context: {"novel_id":3,"chapter":13,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-08 06:54:32] Word Export Success | Context: {"novel_id":3,"chapter":13,"file_size":11686,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter13.docx"}
[2025-06-08 09:59:46] Word Export Request | Context: {"novel_id":3,"chapter":14,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-08 09:59:46] Word Export Success | Context: {"novel_id":3,"chapter":14,"file_size":11827,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter14.docx"}
[2025-06-08 10:57:35] Word Export Request | Context: {"novel_id":3,"chapter":15,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-08 10:57:35] Word Export Success | Context: {"novel_id":3,"chapter":15,"file_size":11612,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter15.docx"}
[2025-06-08 11:46:25] Word Export Request | Context: {"novel_id":2,"chapter":35,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-08 11:46:26] Word Export Success | Context: {"novel_id":2,"chapter":35,"file_size":10384,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter35.docx"}
[2025-06-08 23:51:14] Chapter Chunks API: GET request received | Context: {"chapter_id":151,"method":"GET"}
[2025-06-08 23:51:19] Chapter Chunks API: GET request received | Context: {"chapter_id":151,"method":"GET"}
[2025-06-08 23:51:35] Chapter Chunks API: Translation request received | Context: {"chunk_id":8,"target_language":"en","raw_input":"{\"chunk_id\":8,\"target_language\":\"en\"}"}
[2025-06-08 23:53:18] Chapter Chunks API: GET request received | Context: {"chapter_id":151,"method":"GET"}
[2025-06-08 23:53:44] Chapter Chunks API: Translation request received | Context: {"chunk_id":9,"target_language":"en","raw_input":"{\"chunk_id\":9,\"target_language\":\"en\"}"}
[2025-06-08 23:55:44] Chapter Chunks API: GET request received | Context: {"chapter_id":151,"method":"GET"}
[2025-06-09 00:11:16] Word Export Request | Context: {"novel_id":1,"chapter":151,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-09 00:11:17] Word Export Success | Context: {"novel_id":1,"chapter":151,"file_size":17960,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter151.docx"}
[2025-06-10 01:48:39] Word Export Request | Context: {"novel_id":2,"chapter":36,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 01:48:39] Word Export Success | Context: {"novel_id":2,"chapter":36,"file_size":10707,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter36.docx"}
[2025-06-10 02:20:05] Word Export Request | Context: {"novel_id":7,"chapter":18,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 02:20:05] Word Export Success | Context: {"novel_id":7,"chapter":18,"file_size":11552,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter18.docx"}
[2025-06-10 02:50:53] Word Export Request | Context: {"novel_id":7,"chapter":18,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 02:50:53] Word Export Success | Context: {"novel_id":7,"chapter":18,"file_size":11612,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter18.docx"}
[2025-06-10 03:09:38] Word Export Request | Context: {"novel_id":7,"chapter":19,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 03:09:38] Word Export Success | Context: {"novel_id":7,"chapter":19,"file_size":11467,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter19.docx"}
[2025-06-10 03:47:05] Word Export Request | Context: {"novel_id":5,"chapter":3,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 03:47:05] Word Export Success | Context: {"novel_id":5,"chapter":3,"file_size":12356,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter3.docx"}
[2025-06-10 06:17:48] Word Export Request | Context: {"novel_id":5,"chapter":4,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 06:17:48] Word Export Success | Context: {"novel_id":5,"chapter":4,"file_size":9182,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter4.docx"}
[2025-06-10 06:45:18] Word Export Request | Context: {"novel_id":5,"chapter":4,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 06:45:18] Word Export Success | Context: {"novel_id":5,"chapter":4,"file_size":12550,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter4.docx"}
[2025-06-10 08:33:44] Word Export Request | Context: {"novel_id":5,"chapter":4,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 08:33:45] Word Export Success | Context: {"novel_id":5,"chapter":4,"file_size":12980,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter4.docx"}
[2025-06-10 14:44:00] Word Export Request | Context: {"novel_id":5,"chapter":4,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 14:44:00] Word Export Success | Context: {"novel_id":5,"chapter":4,"file_size":13378,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter4.docx"}
[2025-06-10 14:44:46] Word Export Request | Context: {"novel_id":5,"chapter":4,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 14:44:46] Word Export Success | Context: {"novel_id":5,"chapter":4,"file_size":13378,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter4.docx"}
[2025-06-10 15:03:49] Chapter Chunks API: GET request received | Context: {"chapter_id":442,"method":"GET"}
[2025-06-10 15:04:00] Chapter Chunks API: Translation request received | Context: {"chunk_id":10,"target_language":"en","raw_input":"{\"chunk_id\":10,\"target_language\":\"en\"}"}
[2025-06-10 15:06:38] Chapter Chunks API: GET request received | Context: {"chapter_id":442,"method":"GET"}
[2025-06-10 15:07:45] Chapter Chunks API: Translation request received | Context: {"chunk_id":11,"target_language":"en","raw_input":"{\"chunk_id\":11,\"target_language\":\"en\"}"}
[2025-06-10 15:09:32] Chapter Chunks API: GET request received | Context: {"chapter_id":442,"method":"GET"}
[2025-06-10 15:11:11] Chapter Chunks API: GET request received | Context: {"chapter_id":442,"method":"GET"}
[2025-06-10 15:12:00] Word Export Request | Context: {"novel_id":5,"chapter":5,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 15:12:00] Word Export Success | Context: {"novel_id":5,"chapter":5,"file_size":15687,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter5.docx"}
[2025-06-10 23:34:18] Chapter Chunks API: GET request received | Context: {"chapter_id":152,"method":"GET"}
[2025-06-10 23:47:10] Chapter Chunks API: GET request received | Context: {"chapter_id":152,"method":"GET"}
[2025-06-10 23:47:22] Chapter Chunks API: Translation request received | Context: {"chunk_id":12,"target_language":"en","raw_input":"{\"chunk_id\":12,\"target_language\":\"en\"}"}
[2025-06-10 23:49:10] Chapter Chunks API: GET request received | Context: {"chapter_id":152,"method":"GET"}
[2025-06-10 23:49:20] Chapter Chunks API: Translation request received | Context: {"chunk_id":13,"target_language":"en","raw_input":"{\"chunk_id\":13,\"target_language\":\"en\"}"}
[2025-06-10 23:51:18] Chapter Chunks API: GET request received | Context: {"chapter_id":152,"method":"GET"}
[2025-06-10 23:51:22] Chapter Chunks API: Translation request received | Context: {"chunk_id":14,"target_language":"en","raw_input":"{\"chunk_id\":14,\"target_language\":\"en\"}"}
[2025-06-10 23:52:13] Chapter Chunks API: GET request received | Context: {"chapter_id":152,"method":"GET"}
[2025-06-10 23:55:35] Word Export Request | Context: {"novel_id":1,"chapter":152,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-10 23:55:35] Word Export Success | Context: {"novel_id":1,"chapter":152,"file_size":20430,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter152.docx"}
[2025-06-11 01:08:19] Chapter Chunks API: GET request received | Context: {"chapter_id":153,"method":"GET"}
[2025-06-11 01:09:10] Word Export Request | Context: {"novel_id":1,"chapter":153,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 01:09:10] Word Export Success | Context: {"novel_id":1,"chapter":153,"file_size":17330,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter153.docx"}
[2025-06-11 01:30:25] Word Export Request | Context: {"novel_id":1,"chapter":153,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 01:30:26] Word Export Success | Context: {"novel_id":1,"chapter":153,"file_size":18106,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter153.docx"}
[2025-06-11 02:39:52] Word Export Request | Context: {"novel_id":7,"chapter":20,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 02:39:52] Word Export Success | Context: {"novel_id":7,"chapter":20,"file_size":11303,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter20.docx"}
[2025-06-11 05:15:18] Word Export Request | Context: {"novel_id":7,"chapter":21,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 05:15:18] Word Export Success | Context: {"novel_id":7,"chapter":21,"file_size":12513,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter21.docx"}
[2025-06-11 06:01:35] Word Export Request | Context: {"novel_id":2,"chapter":37,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 06:01:35] Word Export Success | Context: {"novel_id":2,"chapter":37,"file_size":11741,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter37.docx"}
[2025-06-11 06:46:47] Word Export Request | Context: {"novel_id":2,"chapter":37,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 06:46:47] Word Export Success | Context: {"novel_id":2,"chapter":37,"file_size":11777,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter37.docx"}
[2025-06-11 07:41:19] Word Export Request | Context: {"novel_id":2,"chapter":37,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 07:41:19] Word Export Success | Context: {"novel_id":2,"chapter":37,"file_size":11585,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter37.docx"}
[2025-06-11 08:05:37] Word Export Request | Context: {"novel_id":2,"chapter":37,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 08:05:37] Word Export Success | Context: {"novel_id":2,"chapter":37,"file_size":11644,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter37.docx"}
[2025-06-11 08:23:16] Word Export Request | Context: {"novel_id":2,"chapter":38,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 08:23:16] Word Export Success | Context: {"novel_id":2,"chapter":38,"file_size":10649,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter38.docx"}
[2025-06-11 09:13:10] Word Export Request | Context: {"novel_id":7,"chapter":22,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 09:13:10] Word Export Success | Context: {"novel_id":7,"chapter":22,"file_size":11552,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter22.docx"}
[2025-06-11 09:42:31] Word Export Request | Context: {"novel_id":7,"chapter":23,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 09:42:31] Word Export Success | Context: {"novel_id":7,"chapter":23,"file_size":11500,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter23.docx"}
[2025-06-11 10:25:24] Word Export Request | Context: {"novel_id":3,"chapter":17,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 10:25:24] Word Export Success | Context: {"novel_id":3,"chapter":17,"file_size":11417,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter17.docx"}
[2025-06-11 10:58:53] Word Export Request | Context: {"novel_id":3,"chapter":17,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 10:58:53] Word Export Success | Context: {"novel_id":3,"chapter":17,"file_size":11417,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter17.docx"}
[2025-06-11 11:20:37] Word Export Request | Context: {"novel_id":3,"chapter":18,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-11 11:20:37] Word Export Success | Context: {"novel_id":3,"chapter":18,"file_size":10595,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter18.docx"}
[2025-06-12 01:44:26] Word Export Request | Context: {"novel_id":1,"chapter":154,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-12 01:44:26] Word Export Success | Context: {"novel_id":1,"chapter":154,"file_size":19771,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter154.docx"}
[2025-06-12 15:40:37] Preview API Error: Unsupported URL or invalid format | Context: {"url":"https:\/\/www.69shuba.com\/book\/57793.htm","trace":"#0 C:\\xampp82\\htdocs\\wc\\api\\preview.php(75): NovelManager->previewNovel('https:\/\/www.69s...')\n#1 {main}"}
[2025-06-13 07:36:32] Word Export Request | Context: {"novel_id":2,"chapter":39,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 07:36:32] Word Export Success | Context: {"novel_id":2,"chapter":39,"file_size":10691,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter39.docx"}
[2025-06-13 07:52:16] Word Export Request | Context: {"novel_id":2,"chapter":40,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 07:52:16] Word Export Success | Context: {"novel_id":2,"chapter":40,"file_size":10391,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter40.docx"}
[2025-06-13 09:06:52] Word Export Request | Context: {"novel_id":7,"chapter":24,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 09:06:52] Word Export Success | Context: {"novel_id":7,"chapter":24,"file_size":11381,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter24.docx"}
[2025-06-13 09:42:15] Word Export Request | Context: {"novel_id":7,"chapter":25,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 09:42:15] Word Export Success | Context: {"novel_id":7,"chapter":25,"file_size":12456,"filename":"[I_Was_Reincarnated_as_the_Villainesss_Brother_but_]_Chapter25.docx"}
[2025-06-13 12:47:52] Chapter Chunks API: GET request received | Context: {"chapter_id":443,"method":"GET"}
[2025-06-13 12:49:11] Word Export Request | Context: {"novel_id":5,"chapter":6,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 12:49:11] Word Export Success | Context: {"novel_id":5,"chapter":6,"file_size":10186,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter6.docx"}
[2025-06-13 13:15:33] Word Export Request | Context: {"novel_id":5,"chapter":6,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 13:15:33] Word Export Success | Context: {"novel_id":5,"chapter":6,"file_size":10185,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter6.docx"}
[2025-06-13 14:05:36] Chapter Chunks API: GET request received | Context: {"chapter_id":443,"method":"GET"}
[2025-06-13 14:05:50] Chapter Chunks API: GET request received | Context: {"chapter_id":443,"method":"GET"}
[2025-06-13 14:05:55] Chapter Chunks API: Translation request received | Context: {"chunk_id":34,"target_language":"en","raw_input":"{\"chunk_id\":34,\"target_language\":\"en\"}"}
[2025-06-13 14:07:54] Chapter Chunks API: GET request received | Context: {"chapter_id":443,"method":"GET"}
[2025-06-13 14:08:35] Chapter Chunks API: Translation request received | Context: {"chunk_id":35,"target_language":"en","raw_input":"{\"chunk_id\":35,\"target_language\":\"en\"}"}
[2025-06-13 14:08:55] Chapter Chunks API: GET request received | Context: {"chapter_id":443,"method":"GET"}
[2025-06-13 14:13:11] Word Export Request | Context: {"novel_id":5,"chapter":6,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 14:13:11] Word Export Success | Context: {"novel_id":5,"chapter":6,"file_size":13403,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter6.docx"}
[2025-06-13 14:14:01] Chapter Chunks API: GET request received | Context: {"chapter_id":444,"method":"GET"}
[2025-06-13 14:14:13] Chapter Chunks API: GET request received | Context: {"chapter_id":444,"method":"GET"}
[2025-06-13 14:14:22] Chapter Chunks API: Translation request received | Context: {"chunk_id":36,"target_language":"en","raw_input":"{\"chunk_id\":36,\"target_language\":\"en\"}"}
[2025-06-13 14:16:15] Chapter Chunks API: GET request received | Context: {"chapter_id":444,"method":"GET"}
[2025-06-13 14:26:54] Chapter Chunks API: GET request received | Context: {"chapter_id":444,"method":"GET"}
[2025-06-13 14:26:58] Chapter Chunks API: Translation request received | Context: {"chunk_id":37,"target_language":"en","raw_input":"{\"chunk_id\":37,\"target_language\":\"en\"}"}
[2025-06-13 14:27:06] Chapter Chunks API: GET request received | Context: {"chapter_id":444,"method":"GET"}
[2025-06-13 14:42:45] Word Export Request | Context: {"novel_id":5,"chapter":7,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 14:42:46] Word Export Success | Context: {"novel_id":5,"chapter":7,"file_size":12307,"filename":"[FLOWER_POT_MANI_was_just_taming_parasitic_plants_a]_Chapter7.docx"}
[2025-06-13 23:38:14] Word Export Request | Context: {"novel_id":3,"chapter":19,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-13 23:38:14] Word Export Success | Context: {"novel_id":3,"chapter":19,"file_size":11203,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter19.docx"}
[2025-06-14 01:31:22] Word Export Request | Context: {"novel_id":3,"chapter":20,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-14 01:31:22] Word Export Success | Context: {"novel_id":3,"chapter":20,"file_size":11473,"filename":"[The_Unwittingly_Strongest_Pharmacist]_Chapter20.docx"}
[2025-06-14 01:59:40] Chapter Chunks API: GET request received | Context: {"chapter_id":155,"method":"GET"}
[2025-06-14 01:59:52] Chapter Chunks API: Translation request received | Context: {"chunk_id":27,"target_language":"en","raw_input":"{\"chunk_id\":27,\"target_language\":\"en\"}"}
[2025-06-14 02:01:09] Chapter Chunks API: GET request received | Context: {"chapter_id":155,"method":"GET"}
[2025-06-14 02:03:06] Chapter Chunks API: Translation request received | Context: {"chunk_id":28,"target_language":"en","raw_input":"{\"chunk_id\":28,\"target_language\":\"en\"}"}
[2025-06-14 02:03:12] Chapter Chunks API: GET request received | Context: {"chapter_id":155,"method":"GET"}
[2025-06-14 02:03:23] Chapter Chunks API: Translation request received | Context: {"chunk_id":29,"target_language":"en","raw_input":"{\"chunk_id\":29,\"target_language\":\"en\"}"}
[2025-06-14 02:03:47] Chapter Chunks API: GET request received | Context: {"chapter_id":155,"method":"GET"}
[2025-06-14 02:03:55] Chapter Chunks API: Translation request received | Context: {"chunk_id":30,"target_language":"en","raw_input":"{\"chunk_id\":30,\"target_language\":\"en\"}"}
[2025-06-14 02:04:17] Chapter Chunks API: GET request received | Context: {"chapter_id":155,"method":"GET"}
[2025-06-14 02:06:44] Word Export Request | Context: {"novel_id":1,"chapter":155,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-14 02:06:44] Word Export Success | Context: {"novel_id":1,"chapter":155,"file_size":18914,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter155.docx"}
[2025-06-14 02:06:59] Chapter Chunks API: GET request received | Context: {"chapter_id":156,"method":"GET"}
[2025-06-14 02:07:08] Chapter Chunks API: Translation request received | Context: {"chunk_id":38,"target_language":"en","raw_input":"{\"chunk_id\":38,\"target_language\":\"en\"}"}
[2025-06-14 02:08:26] Chapter Chunks API: GET request received | Context: {"chapter_id":156,"method":"GET"}
[2025-06-14 02:13:04] Chapter Chunks API: Translation request received | Context: {"chunk_id":39,"target_language":"en","raw_input":"{\"chunk_id\":39,\"target_language\":\"en\"}"}
[2025-06-14 02:13:33] Chapter Chunks API: GET request received | Context: {"chapter_id":156,"method":"GET"}
[2025-06-14 04:05:51] Word Export Request | Context: {"novel_id":1,"chapter":156,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-14 04:05:51] Word Export Success | Context: {"novel_id":1,"chapter":156,"file_size":17361,"filename":"[A_Middle-Aged_Sages_Otherworldly_Daily_Records]_Chapter156.docx"}
[2025-06-14 05:24:33] Word Export Request | Context: {"novel_id":2,"chapter":41,"user_agent":"Mozilla\/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit\/537.36 (KHTML, like Gecko) Chrome\/137.0.0.0 Safari\/537.36"}
[2025-06-14 05:24:33] Word Export Success | Context: {"novel_id":2,"chapter":41,"file_size":11039,"filename":"[The_Saint_has_long_since_been_summoned_To_Japan]_Chapter41.docx"}
