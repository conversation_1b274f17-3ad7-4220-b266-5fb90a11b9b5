<?php
/**
 * Migration: Add Chapter Chunking Support
 * Adds chapter_chunks table and chunking configuration preferences
 */

require_once __DIR__ . '/../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Starting chunking support migration...\n";
    
    // Check if chapter_chunks table already exists
    $tableExists = $db->fetchOne("SHOW TABLES LIKE 'chapter_chunks'");
    
    if (!$tableExists) {
        echo "Creating chapter_chunks table...\n";
        
        $createTableSQL = "
        CREATE TABLE chapter_chunks (
            id INT AUTO_INCREMENT PRIMARY KEY,
            chapter_id INT NOT NULL,
            chunk_number INT NOT NULL,
            original_content TEXT NOT NULL,
            translated_content TEXT,
            character_count INT NOT NULL,
            word_count INT NOT NULL,
            chunk_type ENUM('paragraph', 'scene_break', 'dialogue', 'narrative') DEFAULT 'paragraph',
            translation_status ENUM('pending', 'translating', 'completed', 'error') DEFAULT 'pending',
            translation_date TIMESTAMP NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
            UNIQUE KEY unique_chunk (chapter_id, chunk_number),
            INDEX idx_chapter_chunks (chapter_id, chunk_number),
            INDEX idx_chunk_status (translation_status)
        )";
        
        $db->query($createTableSQL);
        echo "✓ chapter_chunks table created successfully\n";
    } else {
        echo "✓ chapter_chunks table already exists\n";
    }
    
    // Add chunking preferences if they don't exist
    $preferences = [
        'chunk_size_limit' => '25000',
        'chunk_overlap_size' => '500',
        'enable_smart_chunking' => 'true'
    ];
    
    foreach ($preferences as $key => $value) {
        $existing = $db->fetchOne(
            "SELECT id FROM user_preferences WHERE preference_key = ?",
            [$key]
        );
        
        if (!$existing) {
            $db->insert('user_preferences', [
                'preference_key' => $key,
                'preference_value' => $value
            ]);
            echo "✓ Added preference: {$key} = {$value}\n";
        } else {
            echo "✓ Preference already exists: {$key}\n";
        }
    }
    
    // Update existing chapters with proper word counts if needed
    echo "Updating chapter word counts...\n";
    $chaptersToUpdate = $db->fetchAll(
        "SELECT id, original_content FROM chapters 
         WHERE word_count = 0 AND original_content IS NOT NULL AND original_content != ''"
    );
    
    $updatedCount = 0;
    foreach ($chaptersToUpdate as $chapter) {
        $wordCount = str_word_count(strip_tags($chapter['original_content']));
        $db->update('chapters', 
            ['word_count' => $wordCount], 
            'id = ?', 
            [$chapter['id']]
        );
        $updatedCount++;
    }
    
    echo "✓ Updated word counts for {$updatedCount} chapters\n";
    
    // Identify chapters that might need chunking
    echo "Analyzing chapters for chunking needs...\n";
    $longChapters = $db->fetchAll(
        "SELECT id, chapter_number, novel_id, CHAR_LENGTH(original_content) as content_length
         FROM chapters
         WHERE CHAR_LENGTH(original_content) > 25000
         ORDER BY content_length DESC"
    );
    
    echo "Found " . count($longChapters) . " chapters that might benefit from chunking:\n";
    foreach ($longChapters as $chapter) {
        echo "  - Chapter {$chapter['chapter_number']} (Novel {$chapter['novel_id']}): " . 
             number_format($chapter['content_length']) . " characters\n";
    }
    
    if (count($longChapters) > 0) {
        echo "\nTo chunk these chapters automatically, you can:\n";
        echo "1. Use the chunking API: POST /api/chunking.php\n";
        echo "2. Or run the auto-chunking script: php scripts/auto_chunk_chapters.php\n";
    }
    
    echo "\n✅ Chunking support migration completed successfully!\n";
    echo "\nNew features available:\n";
    echo "- Automatic chapter chunking for content > 25,000 characters\n";
    echo "- Intelligent content splitting at natural break points\n";
    echo "- Configurable chunk size limits and overlap\n";
    echo "- Chunking management API at /api/chunking.php\n";
    
} catch (Exception $e) {
    echo "❌ Migration failed: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    exit(1);
}
