# Chapter Translation Editor Guide

## Overview

The Chapter Translation Editor is a dedicated page for editing novel chapter translations with advanced rich text editing capabilities powered by TinyMCE. This editor provides a professional writing environment with formatting tools, auto-save functionality, and side-by-side original text viewing.

## Features

### Rich Text Editing
- **TinyMCE Integration**: Professional WYSIWYG editor with full formatting capabilities
- **Custom Styling**: Optimized for novel translation with serif fonts and proper line spacing
- **Formatting Tools**: Bold, italic, underline, lists, alignment, and more
- **Character Support**: Full Unicode support for special characters and symbols

### Auto-Save Functionality
- **Automatic Saving**: Content is automatically saved every 30 seconds
- **Manual Save**: Ctrl+S or Save button for immediate saving
- **Save Indicators**: Visual feedback showing save status
- **Unsaved Changes Warning**: Prevents accidental data loss

### Original Text Reference
- **Side-by-Side View**: View original text alongside translation
- **Modal View**: Popup reference for smaller screens
- **Furigana Support**: Proper display of Japanese ruby text
- **Formatted Display**: Maintains paragraph structure and formatting

### Navigation & Usability
- **Breadcrumb Navigation**: Clear path back to novel and chapter view
- **Keyboard Shortcuts**: Efficient editing with hotkeys
- **Fullscreen Mode**: Distraction-free editing environment
- **Responsive Design**: Works on desktop, tablet, and mobile devices

## How to Access

### From Chapter View Page
1. Navigate to any chapter with translated content
2. Look for the "Rich Editor" button in the action buttons section
3. Click to open the dedicated editor page

### Direct URL
Access directly using: `chapter-edit.php?novel_id=X&chapter=Y`
- Replace `X` with the novel ID
- Replace `Y` with the chapter number

## Using the Editor

### Basic Editing
1. **Loading**: The editor automatically loads the chapter's translated content
2. **Editing**: Use the rich text editor to modify the translation
3. **Formatting**: Apply formatting using the toolbar or keyboard shortcuts
4. **Saving**: Content saves automatically or use Ctrl+S for manual save

### Viewing Original Text
1. **Show Original Button**: Click to display original text in side panel
2. **Modal View**: On smaller screens, original text opens in a popup
3. **Hide Original**: Click the X button to close the original text panel

### Keyboard Shortcuts
- **Ctrl+S**: Save changes
- **Ctrl+Z**: Undo
- **Ctrl+Y**: Redo
- **F11**: Toggle fullscreen mode
- **Ctrl+O**: Show/hide original text

### Auto-Save Features
- **Status Indicator**: Shows "Auto-saved", "Saving...", or "Auto-save failed"
- **Toggle Auto-Save**: Use the auto-save button to enable/disable
- **Frequency**: Saves every 30 seconds when changes are detected
- **Conflict Prevention**: Only saves when there are actual changes

## Editor Configuration

### TinyMCE Settings
The editor is configured with:
- **Plugins**: Advanced list, autolink, search/replace, word count, autosave
- **Toolbar**: Comprehensive formatting options optimized for text editing
- **Content Style**: Serif fonts, proper line spacing, novel-appropriate styling
- **Auto-Save**: Built-in TinyMCE autosave with local storage backup

### Responsive Behavior
- **Desktop**: Full-featured interface with side-by-side original text option
- **Tablet**: Adapted toolbar and modal original text view
- **Mobile**: Simplified interface with essential features

## Technical Details

### API Integration
- **Save Endpoint**: Uses existing `api/chapters.php` PATCH method
- **Data Format**: Saves HTML content from TinyMCE editor
- **Error Handling**: Comprehensive error reporting and retry mechanisms

### Browser Compatibility
- **Modern Browsers**: Chrome, Firefox, Safari, Edge (latest versions)
- **JavaScript Required**: Editor requires JavaScript to function
- **Local Storage**: Uses browser storage for auto-save backup

### Performance
- **Lazy Loading**: TinyMCE loads only when needed
- **Efficient Saving**: Only saves when content actually changes
- **Memory Management**: Proper cleanup when leaving the page

## Troubleshooting

### Common Issues

**Editor Not Loading**
- Check browser console for JavaScript errors
- Ensure internet connection for TinyMCE CDN
- Verify browser supports modern JavaScript features

**Auto-Save Not Working**
- Check if auto-save is enabled (toggle button)
- Verify network connectivity
- Look for error indicators in the save status

**Original Text Not Displaying**
- Ensure chapter has original content saved
- Check if content contains special characters or formatting
- Try refreshing the page

**Formatting Issues**
- Use the editor's built-in formatting tools
- Avoid pasting from external sources with complex formatting
- Use "Remove Formatting" tool if needed

### Getting Help
- Check browser console for detailed error messages
- Verify chapter and novel IDs are correct
- Ensure proper permissions and database connectivity

## Best Practices

### Translation Quality
- **Preserve Structure**: Maintain paragraph breaks and formatting
- **Character Names**: Keep character name translations consistent
- **Tone and Style**: Match the original work's tone and style
- **Proofreading**: Use the editor's spell check and review tools

### Workflow Efficiency
- **Use Shortcuts**: Learn keyboard shortcuts for faster editing
- **Reference Original**: Keep original text visible for context
- **Save Frequently**: Don't rely solely on auto-save for important changes
- **Fullscreen Mode**: Use for focused, distraction-free editing

### Content Management
- **Backup Important Work**: Save copies of significant translations
- **Version Control**: Use the chapter view page to compare versions
- **Collaborative Editing**: Coordinate with other translators if working in teams

## Integration with Existing Features

### Chapter Management
- **Seamless Integration**: Works with existing chapter viewing and management
- **Status Updates**: Automatically updates chapter translation status
- **Word Count**: Tracks and displays word count changes

### Translation Workflow
- **Compatible with AI Translation**: Can edit AI-generated translations
- **Chunk Support**: Works with chunked translations from large chapters
- **Name Dictionary**: Integrates with existing name translation features

### User Preferences
- **Settings Persistence**: Remembers user preferences across sessions
- **Display Options**: Respects existing furigana and formatting preferences
- **Theme Support**: Compatible with existing application theming
