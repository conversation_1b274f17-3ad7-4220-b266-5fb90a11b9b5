<?php
// Debug the HTML structure to find the correct content extraction method

$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "Analyzing HTML structure for: $chapterUrl\n\n";

// Get raw HTML and convert encoding
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $chapterUrl);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);
curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
curl_setopt($ch, CURLOPT_ENCODING, '');

$rawHtml = curl_exec($ch);
curl_close($ch);

// Convert from GBK to UTF-8
$html = mb_convert_encoding($rawHtml, 'UTF-8', 'GBK');
$html = preg_replace('/<meta[^>]*charset[^>]*>/i', '<meta charset="UTF-8">', $html);

// Parse with DOMDocument
$dom = new DOMDocument();
libxml_use_internal_errors(true);
$dom->loadHTML($html, LIBXML_HTML_NOIMPLIED | LIBXML_HTML_NODEFDTD | LIBXML_NOERROR | LIBXML_NOWARNING);
libxml_clear_errors();

$xpath = new DOMXPath($dom);

// Look for the main content structure
echo "=== Analyzing page structure ===\n";

// Check for mybox div
$myboxElements = $xpath->query('//div[contains(@class, "mybox")]');
echo "Found " . $myboxElements->length . " .mybox elements\n";

if ($myboxElements->length > 0) {
    $mybox = $myboxElements->item(0);
    echo "Mybox innerHTML length: " . strlen($mybox->textContent) . "\n";
    
    // Look for specific content patterns
    $textContent = $mybox->textContent;
    
    // Find where the actual chapter content starts
    if (preg_match('/第1章\s+怒气\s*(.+)/s', $textContent, $matches)) {
        $afterTitle = $matches[1];
        echo "Content after chapter title (first 500 chars):\n";
        echo substr($afterTitle, 0, 500) . "\n\n";
        
        // Look for the actual story content (should start with Chinese text)
        if (preg_match('/\d{4}-\d{1,2}-\d{1,2}\s*第\d+章[^\n]*\n(.+?)(?:上一章|下一章|目录|Copyright|$)/s', $afterTitle, $contentMatches)) {
            $storyContent = trim($contentMatches[1]);
            echo "=== EXTRACTED STORY CONTENT ===\n";
            echo "Length: " . strlen($storyContent) . " bytes\n";
            echo "Chinese chars: " . preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $storyContent) . "\n";
            echo "First 1000 characters:\n";
            echo "---\n";
            echo mb_substr($storyContent, 0, 1000, 'UTF-8') . "\n";
            echo "---\n";
        } else {
            echo "Could not find story content pattern\n";
        }
    }
}

// Also check for script tags that might contain content
echo "\n=== Checking for script content ===\n";
$scripts = $xpath->query('//script');
echo "Found " . $scripts->length . " script elements\n";

foreach ($scripts as $script) {
    $scriptContent = $script->textContent;
    if (strpos($scriptContent, 'bookinfo') !== false) {
        echo "Found bookinfo script:\n";
        echo substr($scriptContent, 0, 500) . "\n\n";
    }
}
?>
