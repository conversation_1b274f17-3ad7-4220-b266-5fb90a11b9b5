<?php
/**
 * API Endpoint: Direct Text Translation
 * POST /api/translate.php - Translate text directly
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

// Handle preflight requests
if ($method === 'OPTIONS') {
    http_response_code(200);
    exit;
}

try {
    $translationService = new TranslationService();
} catch (Exception $e) {
    logError('Failed to initialize TranslationService: ' . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'Translation service initialization failed'
    ], 500);
}

try {
    switch ($method) {
        case 'POST':
            handleTranslateText($translationService);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }

} catch (Exception $e) {
    logError('Translate API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'Translation failed: ' . $e->getMessage()
    ], 500);
}

/**
 * Handle POST request - Translate text
 */
function handleTranslateText($translationService) {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['text']) || empty(trim($input['text']))) {
        jsonResponse(['error' => 'Text to translate is required'], 400);
    }

    $text = trim($input['text']);
    $targetLanguage = isset($input['target_language']) ? sanitizeInput($input['target_language']) : DEFAULT_TARGET_LANGUAGE;
    $sourceLanguage = isset($input['source_language']) ? sanitizeInput($input['source_language']) : 'auto';
    $type = isset($input['type']) ? sanitizeInput($input['type']) : 'general';

    // Validate text length (prevent abuse)
    if (strlen($text) > 10000) {
        jsonResponse(['error' => 'Text too long. Maximum 10,000 characters allowed.'], 400);
    }

    // Build context based on type
    $context = ['type' => $type];
    
    // Add novel context if provided
    if (isset($input['novel_id']) && is_numeric($input['novel_id'])) {
        $context['novel_id'] = (int)$input['novel_id'];
    }

    try {
        $result = $translationService->translateText($text, $targetLanguage, $sourceLanguage, $context);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'translated_text' => $result['translated_text'],
                'original_text' => $text,
                'target_language' => $targetLanguage,
                'source_language' => $sourceLanguage,
                'type' => $type,
                'execution_time' => $result['execution_time'] ?? null
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => $result['error'] ?? 'Translation failed',
                'original_text' => $text
            ], 400);
        }

    } catch (Exception $e) {
        error_log("Direct translation error: " . $e->getMessage());
        
        jsonResponse([
            'success' => false,
            'error' => 'Translation service error: ' . $e->getMessage(),
            'original_text' => $text
        ], 500);
    }
}
?>
