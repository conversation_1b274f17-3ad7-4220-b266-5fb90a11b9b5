<?php
// Test both 69shuba domains to see which one works better

$testUrls = [
    'cx' => 'https://69shuba.cx/txt/44425/29853970',
    'com' => 'https://69shuba.com/txt/44425/29853970'
];

foreach ($testUrls as $domain => $url) {
    echo "=== Testing $domain domain ===\n";
    echo "URL: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_ENCODING, ''); // Let curl handle all encodings
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
        'Accept-Language: zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding: gzip, deflate, br',
        'Referer: https://69shuba.' . $domain . '/book/44425.htm'
    ]);
    
    $html = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    $contentType = curl_getinfo($ch, CURLINFO_CONTENT_TYPE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode\n";
    echo "CURL Error: " . ($error ?: 'None') . "\n";
    echo "Content Type: $contentType\n";
    echo "HTML length: " . strlen($html) . "\n";
    
    if ($httpCode == 200 && !empty($html)) {
        echo "HTML encoding: " . mb_detect_encoding($html) . "\n";
        echo "Is valid UTF-8: " . (mb_check_encoding($html, 'UTF-8') ? 'Yes' : 'No') . "\n";
        
        // Check if content looks like Chinese text
        if (mb_check_encoding($html, 'UTF-8')) {
            $chineseCharCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $html);
            echo "Chinese character count: $chineseCharCount\n";
            
            if ($chineseCharCount > 0) {
                echo "✓ Contains Chinese characters - looks good!\n";
                echo "First 500 characters:\n";
                echo mb_substr($html, 0, 500, 'UTF-8') . "\n";
            } else {
                echo "✗ No Chinese characters found\n";
                echo "First 200 characters (raw):\n";
                echo substr($html, 0, 200) . "\n";
            }
        } else {
            echo "✗ Invalid UTF-8 encoding\n";
            echo "First 200 characters (raw):\n";
            echo substr($html, 0, 200) . "\n";
        }
    } else {
        echo "✗ Failed to fetch content\n";
    }
    
    echo "\n";
}

// Test novel info pages too
echo "=== Testing Novel Info Pages ===\n";
$novelUrls = [
    'cx' => 'https://69shuba.cx/book/44425.htm',
    'com' => 'https://69shuba.com/book/44425.htm'
];

foreach ($novelUrls as $domain => $url) {
    echo "Testing $domain novel page: $url\n";
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 30);
    curl_setopt($ch, CURLOPT_USERAGENT, 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36');
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
    curl_setopt($ch, CURLOPT_ENCODING, '');
    
    $html = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    echo "HTTP Code: $httpCode, Length: " . strlen($html) . ", UTF-8: " . (mb_check_encoding($html, 'UTF-8') ? 'Yes' : 'No') . "\n";
}
?>
