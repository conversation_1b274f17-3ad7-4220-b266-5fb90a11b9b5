<?php
/**
 * Test the API endpoint via HTTP to see the exact response
 */

// Test data
$testData = [
    'novel_id' => 1,
    'chapter_number' => 2
];

$jsonData = json_encode($testData);

// Make HTTP request to the API
$url = 'http://localhost/wc/api/chapters.php';

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_POST, true);
curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonData);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'Content-Type: application/json',
    'Content-Length: ' . strlen($jsonData)
]);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HEADER, true); // Include headers in output
curl_setopt($ch, CURLOPT_VERBOSE, true);

echo "=== HTTP API Test ===\n";
echo "Request URL: $url\n";
echo "Request Data: $jsonData\n\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$headerSize = curl_getinfo($ch, CURLINFO_HEADER_SIZE);

if (curl_error($ch)) {
    echo "cURL Error: " . curl_error($ch) . "\n";
} else {
    echo "HTTP Status Code: $httpCode\n";
    
    // Separate headers and body
    $headers = substr($response, 0, $headerSize);
    $body = substr($response, $headerSize);
    
    echo "\n=== Response Headers ===\n";
    echo $headers;
    
    echo "\n=== Response Body ===\n";
    echo "Body Length: " . strlen($body) . " characters\n";
    
    // Check if body starts with valid JSON
    $firstChar = substr(ltrim($body), 0, 1);
    echo "First character: '$firstChar'\n";
    
    if ($firstChar !== '{' && $firstChar !== '[') {
        echo "ERROR: Response does not start with JSON!\n";
        echo "First 500 characters of response:\n";
        echo substr($body, 0, 500) . "\n";
        
        // Look for any non-JSON content before the JSON
        $jsonStart = strpos($body, '{');
        if ($jsonStart > 0) {
            echo "\nNon-JSON content before JSON:\n";
            echo "'" . substr($body, 0, $jsonStart) . "'\n";
        }
    } else {
        echo "Response appears to be valid JSON\n";
        
        // Try to decode JSON
        $decoded = json_decode($body, true);
        if ($decoded === null) {
            echo "JSON decode error: " . json_last_error_msg() . "\n";
            echo "First 200 characters:\n";
            echo substr($body, 0, 200) . "\n";
        } else {
            echo "JSON decoded successfully\n";
            echo "Success: " . ($decoded['success'] ? 'true' : 'false') . "\n";
            if (isset($decoded['data']['chapter']['id'])) {
                echo "Chapter ID: " . $decoded['data']['chapter']['id'] . "\n";
            }
        }
    }
}

curl_close($ch);

echo "\n=== Test Complete ===\n";
?>
