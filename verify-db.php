<?php
/**
 * Database Verification Script
 * Simple script to check database status without loading full application
 */

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Database Verification</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-info text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-database me-2"></i>
                            Database Verification
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php verifyDatabase(); ?>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>

<?php
function verifyDatabase() {
    echo '<div class="verification-results">';
    
    // Test 1: Basic MySQL Connection
    echo '<div class="test-item mb-3">';
    echo '<h6><i class="fas fa-server me-2"></i>MySQL Server Connection</h6>';
    
    try {
        $dsn = "mysql:host=localhost;charset=utf8mb4";
        $pdo = new PDO($dsn, 'root', '', [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);
        
        echo '<div class="alert alert-success">';
        echo '<i class="fas fa-check-circle me-2"></i>Successfully connected to MySQL server';
        echo '</div>';
        
    } catch (PDOException $e) {
        echo '<div class="alert alert-danger">';
        echo '<i class="fas fa-times-circle me-2"></i>Failed to connect to MySQL: ' . $e->getMessage();
        echo '</div>';
        echo '</div></div>';
        return;
    }
    echo '</div>';
    
    // Test 2: Database Existence
    echo '<div class="test-item mb-3">';
    echo '<h6><i class="fas fa-database me-2"></i>Novel Translator Database</h6>';
    
    try {
        $databases = $pdo->query("SHOW DATABASES LIKE 'novel_translator'")->fetchAll();
        
        if (empty($databases)) {
            echo '<div class="alert alert-warning">';
            echo '<i class="fas fa-exclamation-triangle me-2"></i>Database "novel_translator" does not exist';
            echo '<div class="mt-2">';
            echo '<a href="setup.php" class="btn btn-primary btn-sm">';
            echo '<i class="fas fa-cog me-2"></i>Run Setup';
            echo '</a>';
            echo '</div>';
            echo '</div>';
            echo '</div></div>';
            return;
        } else {
            echo '<div class="alert alert-success">';
            echo '<i class="fas fa-check-circle me-2"></i>Database "novel_translator" exists';
            echo '</div>';
        }
        
    } catch (PDOException $e) {
        echo '<div class="alert alert-danger">';
        echo '<i class="fas fa-times-circle me-2"></i>Error checking database: ' . $e->getMessage();
        echo '</div>';
        echo '</div></div>';
        return;
    }
    echo '</div>';
    
    // Test 3: Switch to database and check tables
    echo '<div class="test-item mb-3">';
    echo '<h6><i class="fas fa-table me-2"></i>Database Tables</h6>';
    
    try {
        $pdo->exec("USE novel_translator");
        $tables = $pdo->query("SHOW TABLES")->fetchAll(PDO::FETCH_COLUMN);
        
        $expectedTables = ['novels', 'chapters', 'name_dictionary', 'translation_logs', 'user_preferences'];
        $missingTables = array_diff($expectedTables, $tables);
        
        if (empty($missingTables)) {
            echo '<div class="alert alert-success">';
            echo '<i class="fas fa-check-circle me-2"></i>All required tables exist (' . count($tables) . ' tables)';
            echo '<div class="mt-2 small">';
            echo 'Tables: ' . implode(', ', $tables);
            echo '</div>';
            echo '</div>';
        } else {
            echo '<div class="alert alert-warning">';
            echo '<i class="fas fa-exclamation-triangle me-2"></i>Missing tables: ' . implode(', ', $missingTables);
            echo '<div class="mt-2">';
            echo '<a href="setup.php" class="btn btn-primary btn-sm">';
            echo '<i class="fas fa-cog me-2"></i>Run Setup';
            echo '</a>';
            echo '</div>';
            echo '</div>';
        }
        
    } catch (PDOException $e) {
        echo '<div class="alert alert-danger">';
        echo '<i class="fas fa-times-circle me-2"></i>Error checking tables: ' . $e->getMessage();
        echo '</div>';
    }
    echo '</div>';
    
    // Test 4: Check sample data
    echo '<div class="test-item mb-3">';
    echo '<h6><i class="fas fa-data me-2"></i>Sample Data</h6>';
    
    try {
        $prefCount = $pdo->query("SELECT COUNT(*) as count FROM user_preferences")->fetch()['count'];
        
        if ($prefCount > 0) {
            echo '<div class="alert alert-success">';
            echo '<i class="fas fa-check-circle me-2"></i>Found ' . $prefCount . ' default preferences';
            echo '</div>';
        } else {
            echo '<div class="alert alert-warning">';
            echo '<i class="fas fa-exclamation-triangle me-2"></i>No default preferences found';
            echo '</div>';
        }
        
    } catch (PDOException $e) {
        echo '<div class="alert alert-danger">';
        echo '<i class="fas fa-times-circle me-2"></i>Error checking data: ' . $e->getMessage();
        echo '</div>';
    }
    echo '</div>';
    
    // Test 5: Application Class Test
    echo '<div class="test-item mb-3">';
    echo '<h6><i class="fas fa-code me-2"></i>Application Classes</h6>';
    
    try {
        // Try to load the config and test the Database class
        require_once 'config/config.php';
        
        $db = Database::getInstance();
        $testResult = $db->fetchOne("SELECT COUNT(*) as count FROM user_preferences");
        
        echo '<div class="alert alert-success">';
        echo '<i class="fas fa-check-circle me-2"></i>Application Database class working correctly';
        echo '<div class="mt-1 small">Retrieved ' . $testResult['count'] . ' preferences via application class</div>';
        echo '</div>';
        
    } catch (Exception $e) {
        echo '<div class="alert alert-warning">';
        echo '<i class="fas fa-exclamation-triangle me-2"></i>Application class test failed: ' . $e->getMessage();
        echo '<div class="mt-2 small text-muted">This might indicate a configuration issue</div>';
        echo '</div>';
    }
    echo '</div>';
    
    // Summary and next steps
    echo '<div class="alert alert-info">';
    echo '<h6><i class="fas fa-info-circle me-2"></i>Next Steps</h6>';
    echo '<div class="d-grid gap-2 d-md-flex">';
    
    if (isset($testResult) && $testResult) {
        echo '<a href="index.php" class="btn btn-success me-md-2">';
        echo '<i class="fas fa-arrow-right me-2"></i>Go to Application';
        echo '</a>';
        echo '<a href="test.php" class="btn btn-info">';
        echo '<i class="fas fa-vial me-2"></i>Run Full Tests';
        echo '</a>';
    } else {
        echo '<a href="setup.php" class="btn btn-primary me-md-2">';
        echo '<i class="fas fa-cog me-2"></i>Run Setup';
        echo '</a>';
        echo '<a href="test.php" class="btn btn-info">';
        echo '<i class="fas fa-vial me-2"></i>Run Diagnostics';
        echo '</a>';
    }
    
    echo '</div>';
    echo '</div>';
    
    echo '</div>';
}
?>
