# Edit Original Text Feature

## Overview

The "Edit Original Text" feature allows users to manually edit the original chapter content after it has been saved from the source website. This provides a manual fallback for cases where automated content extraction may have missed content, included unwanted elements, or requires corrections.

## Features

### Core Functionality
- **Manual Content Editing**: Edit original chapter content in a large, user-friendly text area
- **Content Validation**: Prevents saving empty content
- **Confirmation Dialog**: Requires user confirmation before saving changes
- **Character Counter**: Real-time character count display
- **Audit Logging**: All edits are logged for audit purposes
- **Content Preservation**: Maintains existing chapter structure and associated data

### Advanced Integration
- **Furigana Processing**: Automatically detects and processes furigana markup if present
- **Chunking Support**: Automatically creates chunks if content exceeds size limits
- **Database Transactions**: Ensures data integrity during updates
- **Error Handling**: Comprehensive error handling with user-friendly messages

## User Interface

### Access Points

#### 1. Chapter View Page
- Located alongside existing chapter management buttons
- Blue "Edit Original Text" button with edit icon
- Only visible for chapters with existing content

#### 2. Chapter Title Manager
- "Edit Text" button in the Actions column
- Links directly to chapter view page for editing
- Organized in button groups for clean layout

### Edit Modal Interface

The edit interface opens in a large modal dialog containing:

- **Header**: Clear title showing chapter number being edited
- **Info Alert**: Explanation of what the feature does and potential impacts
- **Text Area**: Large, monospace text editor (20 rows) for content editing
- **Character Counter**: Real-time display of character count
- **Confirmation Checkbox**: Required confirmation before saving
- **Action Buttons**: Cancel and Save Changes buttons

## Technical Implementation

### Backend Components

#### 1. NovelManager::updateChapterOriginalContent()
```php
public function updateChapterOriginalContent(int $novelId, int $chapterNumber, string $originalContent): array
```

**Features:**
- Input validation (non-empty content, valid chapter)
- Database transaction support
- Furigana detection and processing
- Content statistics calculation
- Automatic chunking when needed
- Audit logging
- Error handling with rollback

**Returns:**
- Success/failure status
- Updated chapter data
- Content statistics
- Change summary (length differences)
- Furigana and chunking results

#### 2. API Endpoint (api/chapters.php)
- **Method**: PATCH
- **Action**: `update_original_content`
- **Parameters**: `novel_id`, `chapter_number`, `original_content`
- **Validation**: Comprehensive input validation
- **Response**: JSON with success status and detailed results

### Frontend Components

#### 1. JavaScript Functions (assets/js/chapter-view.js)

**showEditOriginalModal():**
- Creates and displays the edit modal
- Sets up character counter
- Implements confirmation checkbox logic
- Handles modal cleanup

**saveOriginalContent():**
- Validates input before submission
- Makes API request with loading states
- Handles success/error responses
- Provides user feedback
- Reloads page to show changes

#### 2. UI Integration
- Button placement in chapter management areas
- Responsive modal design
- Bootstrap styling consistency
- Icon usage for visual clarity

## Usage Instructions

### For Users

1. **Access the Feature**:
   - Navigate to a chapter view page, OR
   - Use the "Edit Text" button in the chapter title manager

2. **Edit Content**:
   - Click "Edit Original Text" button
   - Review the information alert
   - Edit content in the text area
   - Monitor character count
   - Check the confirmation checkbox

3. **Save Changes**:
   - Click "Save Changes" button
   - Wait for confirmation message
   - Page will reload to show updated content

### For Administrators

#### Monitoring Edits
All edits are logged with the following information:
- Action type: `manual_edit_original_content`
- Novel and chapter IDs
- Content length changes
- User IP and browser information
- Timestamp

#### Database Impact
- Updates `chapters.original_content`
- Updates `chapters.word_count`
- Updates `chapters.updated_at`
- May update furigana-related fields
- May create/update chapter chunks
- Clears existing chunks if content changes significantly

## Security Considerations

### Input Validation
- Content cannot be empty
- Chapter must exist and belong to specified novel
- Proper SQL parameter binding prevents injection

### Access Control
- No additional authentication required (uses existing session)
- Audit logging tracks all changes
- Database transactions ensure data integrity

### Data Protection
- Original content is preserved during validation
- Transaction rollback on errors
- Backup recommendations for important content

## Troubleshooting

### Common Issues

#### 1. "Content cannot be empty" Error
- **Cause**: Attempting to save empty or whitespace-only content
- **Solution**: Ensure content contains actual text

#### 2. "Chapter not found" Error
- **Cause**: Invalid chapter number or novel ID
- **Solution**: Verify chapter exists and URL parameters are correct

#### 3. Modal Not Opening
- **Cause**: JavaScript errors or missing Bootstrap
- **Solution**: Check browser console for errors

#### 4. Save Button Disabled
- **Cause**: Confirmation checkbox not checked or empty content
- **Solution**: Check confirmation checkbox and ensure content is present

### Technical Issues

#### 1. Database Connection Errors
- Check database connectivity
- Verify transaction support
- Review error logs

#### 2. Furigana Processing Errors
- Check FuriganaService availability
- Review furigana markup syntax
- Check error logs for processing details

#### 3. Chunking Errors
- Verify ChapterChunker service
- Check content length and complexity
- Review chunking preferences

## Best Practices

### Content Editing
1. **Backup Important Content**: Save original content before major edits
2. **Test Changes**: Verify content displays correctly after editing
3. **Review Translations**: Consider re-translating after significant changes
4. **Monitor Length**: Be aware of chunking implications for very long content

### System Administration
1. **Regular Backups**: Maintain database backups before bulk edits
2. **Monitor Logs**: Review audit logs for unusual activity
3. **Performance**: Monitor database performance with frequent edits
4. **User Training**: Educate users on proper usage

## Integration Notes

### Existing Features
- **Maintains compatibility** with all existing chapter management features
- **Preserves furigana data** and processing capabilities
- **Integrates with chunking system** for large content
- **Works with translation workflows** without disruption

### Future Enhancements
- Version history for content changes
- Diff view for comparing changes
- Bulk editing capabilities
- Advanced text formatting options

## Conclusion

The Edit Original Text feature provides essential manual control over chapter content while maintaining full integration with existing systems. It offers a reliable fallback for content extraction issues and gives users complete control over their original text before translation.
