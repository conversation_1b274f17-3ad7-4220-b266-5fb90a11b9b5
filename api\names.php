<?php
/**
 * API Endpoint: Name Dictionary Management
 * PUT /api/names.php - Update name dictionary entry
 * POST /api/names.php - Generate romanization for name
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: PUT, POST, DELETE');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

try {
    switch ($method) {
        case 'PUT':
            handleUpdateName();
            break;

        case 'POST':
            handleRomanization();
            break;

        case 'DELETE':
            handleDeleteName();
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    logError('Names API Error: ' . $e->getMessage(), [
        'trace' => $e->getTraceAsString()
    ]);

    jsonResponse([
        'success' => false,
        'error' => 'An error occurred while processing your request'
    ], 500);
}

function handleUpdateName() {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['name_id']) || !is_numeric($input['name_id'])) {
        jsonResponse(['error' => 'Valid name_id is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $nameId = (int)$input['name_id'];

    // Remove IDs from update data
    unset($input['novel_id'], $input['name_id']);

    // Sanitize input data
    $updateData = [];
    if (isset($input['romanization'])) {
        $updateData['romanization'] = sanitizeInput($input['romanization']);
    }
    if (isset($input['translation'])) {
        $updateData['translation'] = sanitizeInput($input['translation']);
    }
    if (isset($input['name_type'])) {
        $updateData['name_type'] = sanitizeInput($input['name_type']);
    }
    if (isset($input['is_verified'])) {
        $updateData['is_verified'] = (bool)$input['is_verified'];
    }

    $novelManager = new NovelManager();
    $result = $novelManager->updateNameDictionary($novelId, $nameId, $updateData);

    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}

function handleRomanization() {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['text']) || empty(trim($input['text']))) {
        jsonResponse(['error' => 'Text is required'], 400);
    }

    $text = sanitizeInput($input['text']);

    $translationService = new TranslationService();
    $result = $translationService->romanizeText($text);

    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}

function handleDeleteName() {
    $input = json_decode(file_get_contents('php://input'), true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    // Validate required fields
    if (!isset($input['novel_id']) || !is_numeric($input['novel_id'])) {
        jsonResponse(['error' => 'Valid novel_id is required'], 400);
    }

    if (!isset($input['name_id']) || !is_numeric($input['name_id'])) {
        jsonResponse(['error' => 'Valid name_id is required'], 400);
    }

    $novelId = (int)$input['novel_id'];
    $nameId = (int)$input['name_id'];

    $novelManager = new NovelManager();
    $result = $novelManager->deleteName($novelId, $nameId);

    if ($result['success']) {
        jsonResponse([
            'success' => true,
            'data' => $result
        ]);
    } else {
        jsonResponse([
            'success' => false,
            'error' => $result['error']
        ], 400);
    }
}
