<?php
/**
 * API Endpoint: Preview Novel
 * GET /api/preview.php?url=<novel_url>
 */

// Set error handling to prevent HTML output
ini_set('display_errors', 0);
error_reporting(0);

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET');
header('Access-Control-Allow-Headers: Content-Type');

/**
 * Clean data to fix UTF-8 encoding issues for JSON encoding
 */
function cleanDataForJson($data) {
    if (is_array($data)) {
        $cleaned = [];
        foreach ($data as $key => $value) {
            $cleanKey = is_string($key) ? mb_convert_encoding($key, 'UTF-8', 'UTF-8') : $key;
            $cleaned[$cleanKey] = cleanDataForJson($value);
        }
        return $cleaned;
    } elseif (is_string($data)) {
        // Remove or replace invalid UTF-8 characters
        $cleaned = mb_convert_encoding($data, 'UTF-8', 'UTF-8');
        // Remove any remaining problematic characters
        $cleaned = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $cleaned);
        return $cleaned;
    } else {
        return $data;
    }
}

// Catch any fatal errors and return JSON
register_shutdown_function(function() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_PARSE, E_CORE_ERROR, E_COMPILE_ERROR])) {
        if (!headers_sent()) {
            header('Content-Type: application/json; charset=utf-8');
            http_response_code(500);
        }
        echo json_encode([
            'success' => false,
            'error' => 'Internal server error: ' . $error['message']
        ]);
        exit;
    }
});

if ($_SERVER['REQUEST_METHOD'] !== 'GET') {
    jsonResponse(['error' => 'Method not allowed'], 405);
}

try {
    // Validate input
    if (!isset($_GET['url']) || empty($_GET['url'])) {
        jsonResponse(['error' => 'URL parameter is required'], 400);
    }

    $url = sanitizeInput($_GET['url']);

    // Validate URL format
    if (!filter_var($url, FILTER_VALIDATE_URL)) {
        jsonResponse(['error' => 'Invalid URL format'], 400);
    }

    // Create novel manager and preview novel
    $novelManager = new NovelManager();
    $result = $novelManager->previewNovel($url);

    if ($result['success']) {
        // Clean data to fix UTF-8 encoding issues
        $cleanedResult = cleanDataForJson($result);

        // Optional chapter limiting for very large novels (configurable)
        $maxPreviewChapters = defined('MAX_PREVIEW_CHAPTERS') ? MAX_PREVIEW_CHAPTERS : 500; // Default to 500 chapters

        if (isset($cleanedResult['chapters']) && count($cleanedResult['chapters']) > $maxPreviewChapters) {
            $cleanedResult['chapters'] = array_slice($cleanedResult['chapters'], 0, $maxPreviewChapters);
            $cleanedResult['total_chapters_limited'] = true;
            $cleanedResult['total_chapters_available'] = $cleanedResult['total_chapters'];
            $cleanedResult['chapters_shown'] = $maxPreviewChapters;
        } else {
            $cleanedResult['total_chapters_limited'] = false;
            $cleanedResult['chapters_shown'] = count($cleanedResult['chapters'] ?? []);
        }

        $responseData = [
            'success' => true,
            'data' => $cleanedResult
        ];

        // Test JSON encoding
        $jsonTest = json_encode($responseData);
        if ($jsonTest === false) {
            jsonResponse(['error' => 'JSON encoding failed: ' . json_last_error_msg()], 500);
        }

        jsonResponse($responseData);
    } else {
        // Provide more specific error messages for common issues
        $errorMessage = $result['error'];
        $httpCode = 400;

        // Provide user-friendly error messages
        if (strpos($errorMessage, 'Access denied') !== false || strpos($errorMessage, '403') !== false) {
            $httpCode = 403;
            $errorMessage = 'Access denied by the novel site. This may be due to anti-bot protection or the novel may be restricted.';
        } elseif (strpos($errorMessage, 'not found') !== false || strpos($errorMessage, '404') !== false) {
            $httpCode = 404;
            $errorMessage = 'Novel not found. Please check if the URL is correct and the novel still exists.';
        } elseif (strpos($errorMessage, 'blocking automated requests') !== false) {
            $httpCode = 429;
            $errorMessage = 'The site is currently blocking automated requests. Please try again later.';
        } elseif (strpos($errorMessage, 'timeout') !== false) {
            $httpCode = 408;
            $errorMessage = 'Request timeout. The novel site may be slow or unavailable.';
        }

        jsonResponse([
            'success' => false,
            'error' => $errorMessage
        ], $httpCode);
    }

} catch (Exception $e) {
    logError('Preview API Error: ' . $e->getMessage(), [
        'url' => $_GET['url'] ?? 'not provided',
        'trace' => $e->getTraceAsString()
    ]);

    // Provide more specific error messages based on the exception
    $errorMessage = 'An error occurred while processing your request';
    $httpCode = 500;

    if (strpos($e->getMessage(), 'Access denied') !== false || strpos($e->getMessage(), '403') !== false) {
        $httpCode = 403;
        $errorMessage = 'Access denied by the novel site. This may be due to anti-bot protection.';
    } elseif (strpos($e->getMessage(), 'not found') !== false || strpos($e->getMessage(), '404') !== false) {
        $httpCode = 404;
        $errorMessage = 'Novel not found. Please check if the URL is correct and the novel still exists.';
    } elseif (strpos($e->getMessage(), 'timeout') !== false) {
        $httpCode = 408;
        $errorMessage = 'Request timeout. The novel site may be slow or unavailable. Please try again.';
    } elseif (strpos($e->getMessage(), 'Invalid') !== false && strpos($e->getMessage(), 'URL') !== false) {
        $httpCode = 400;
        $errorMessage = 'Invalid URL format. Please check the novel URL.';
    } elseif (strpos($e->getMessage(), 'Unsupported URL') !== false) {
        $httpCode = 400;
        $errorMessage = 'Unsupported URL format. Please ensure you are using a valid novel URL from a supported platform.';
    }

    jsonResponse([
        'success' => false,
        'error' => $errorMessage
    ], $httpCode);
}
