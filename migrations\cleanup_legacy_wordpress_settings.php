<?php
/**
 * Migration: Clean up legacy WordPress settings
 * Removes legacy WordPress preferences since we now use profile-based system
 */

require_once '../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Starting legacy WordPress settings cleanup...\n";
    
    // List of legacy WordPress preferences to remove
    $legacyPreferences = [
        'wordpress_site_url',
        'wordpress_username', 
        'wordpress_app_password',
        'wordpress_novel_post_type',
        'wordpress_chapter_post_type',
        'wordpress_novel_custom_post_type',
        'wordpress_chapter_custom_post_type',
        'wordpress_use_custom_post_types',
        'wordpress_default_category',
        'wordpress_auto_publish',
        'wordpress_include_original_title'
    ];
    
    // Check if we have any active WordPress profiles
    $profileCount = $db->fetchOne("SELECT COUNT(*) as count FROM wordpress_profiles WHERE is_active = 1");
    
    if (!$profileCount || $profileCount['count'] == 0) {
        echo "⚠️  Warning: No active WordPress profiles found!\n";
        echo "   Legacy settings will NOT be removed to prevent data loss.\n";
        echo "   Please create at least one WordPress profile before running this cleanup.\n";
        exit(0);
    }
    
    echo "✓ Found {$profileCount['count']} active WordPress profile(s)\n";
    
    // Check which legacy preferences exist
    $existingPreferences = $db->fetchAll(
        "SELECT preference_key FROM user_preferences WHERE preference_key IN ('" . 
        implode("','", $legacyPreferences) . "')"
    );
    
    if (empty($existingPreferences)) {
        echo "✓ No legacy WordPress preferences found - cleanup already complete\n";
        exit(0);
    }
    
    echo "Found " . count($existingPreferences) . " legacy WordPress preferences to clean up:\n";
    foreach ($existingPreferences as $pref) {
        echo "  - {$pref['preference_key']}\n";
    }
    
    // Ask for confirmation (in interactive mode)
    if (php_sapi_name() === 'cli') {
        echo "\nThis will permanently remove the legacy WordPress settings.\n";
        echo "The profile-based system will continue to work normally.\n";
        echo "Do you want to continue? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 'y') {
            echo "Cleanup cancelled by user.\n";
            exit(0);
        }
    }
    
    // Remove legacy preferences
    $deletedCount = 0;
    foreach ($legacyPreferences as $prefKey) {
        $result = $db->delete(
            'user_preferences',
            'preference_key = ?',
            [$prefKey]
        );
        
        if ($result) {
            $deletedCount++;
            echo "✓ Removed: $prefKey\n";
        }
    }
    
    echo "\n✅ Legacy WordPress settings cleanup completed!\n";
    echo "   Removed $deletedCount legacy preference(s)\n";
    echo "   WordPress profile system is now the only configuration method\n";
    
    // Verify the cleanup
    $remainingLegacy = $db->fetchAll(
        "SELECT preference_key FROM user_preferences WHERE preference_key IN ('" . 
        implode("','", $legacyPreferences) . "')"
    );
    
    if (empty($remainingLegacy)) {
        echo "✓ Verification: All legacy preferences successfully removed\n";
    } else {
        echo "⚠️  Warning: Some legacy preferences still exist:\n";
        foreach ($remainingLegacy as $pref) {
            echo "  - {$pref['preference_key']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Cleanup failed: " . $e->getMessage() . "\n";
    exit(1);
}
