-- WordPress Connection Log Table
-- Tracks connection attempts and posting activities for monitoring and debugging

CREATE TABLE IF NOT EXISTS wordpress_connection_log (
    id INT AUTO_INCREMENT PRIMARY KEY,
    profile_name VARCHAR(255) NOT NULL,
    site_url VARCHAR(500) NOT NULL,
    attempt_type ENUM('connection', 'posting') NOT NULL,
    post_type VARCHAR(50) NULL,
    item_id INT NULL,
    success TINYINT(1) NOT NULL DEFAULT 0,
    error_message TEXT NULL,
    metadata JSON NULL,
    memory_usage BIGINT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    INDEX idx_profile_created (profile_name, created_at),
    INDEX idx_success_created (success, created_at),
    INDEX idx_attempt_type (attempt_type),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- Add some sample queries for monitoring

-- Get success rate by profile (last 24 hours)
-- SELECT 
--     profile_name,
--     COUNT(*) as total_attempts,
--     SUM(success) as successful_attempts,
--     ROUND((SUM(success) / COUNT(*)) * 100, 2) as success_rate
-- FROM wordpress_connection_log 
-- WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
-- GROUP BY profile_name
-- ORDER BY success_rate ASC;

-- Get most common errors (last 7 days)
-- SELECT 
--     error_message,
--     COUNT(*) as error_count,
--     profile_name
-- FROM wordpress_connection_log 
-- WHERE created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY) 
--   AND success = 0 
--   AND error_message IS NOT NULL
-- GROUP BY error_message, profile_name
-- ORDER BY error_count DESC
-- LIMIT 10;

-- Get posting activity timeline (last 24 hours)
-- SELECT 
--     DATE_FORMAT(created_at, '%Y-%m-%d %H:00:00') as hour,
--     attempt_type,
--     COUNT(*) as attempts,
--     SUM(success) as successful
-- FROM wordpress_connection_log 
-- WHERE created_at >= DATE_SUB(NOW(), INTERVAL 24 HOUR)
-- GROUP BY hour, attempt_type
-- ORDER BY hour DESC;
