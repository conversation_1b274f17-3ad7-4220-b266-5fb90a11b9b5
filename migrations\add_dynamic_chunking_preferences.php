<?php
/**
 * Migration: Add Dynamic Chunking Preferences
 * Adds new user preferences for enhanced chunking functionality
 */

require_once __DIR__ . '/../config/config.php';

try {
    $db = Database::getInstance();
    
    echo "Adding dynamic chunking preferences...\n";
    
    // New preferences for enhanced chunking
    $newPreferences = [
        'dynamic_chunk_sizing' => 'true',
        'min_chunk_size' => '5000',
        'max_chunk_size' => '20000',
        'context_preservation_size' => '200',
        'adaptive_timeout_handling' => 'true',
        'chunk_recovery_enabled' => 'true'
    ];
    
    foreach ($newPreferences as $key => $value) {
        // Check if preference already exists
        $existing = $db->fetchOne(
            "SELECT id FROM user_preferences WHERE preference_key = ?",
            [$key]
        );
        
        if (!$existing) {
            $db->insert('user_preferences', [
                'preference_key' => $key,
                'preference_value' => $value
            ]);
            echo "✓ Added preference: {$key} = {$value}\n";
        } else {
            echo "✓ Preference already exists: {$key}\n";
        }
    }
    
    // Update existing chunk_size_limit to new recommended value
    $db->update(
        'user_preferences',
        ['preference_value' => '15000'],
        'preference_key = ?',
        ['chunk_size_limit']
    );
    echo "✓ Updated chunk_size_limit to 15000 for better DeepSeek compatibility\n";
    
    // Update existing chunk_overlap_size to new recommended value
    $db->update(
        'user_preferences',
        ['preference_value' => '300'],
        'preference_key = ?',
        ['chunk_overlap_size']
    );
    echo "✓ Updated chunk_overlap_size to 300 for better efficiency\n";
    
    echo "\nDynamic chunking preferences migration completed successfully!\n";
    echo "Enhanced features now available:\n";
    echo "- Dynamic chunk sizing based on content complexity\n";
    echo "- Adaptive timeout handling\n";
    echo "- Intelligent chunk recovery on failures\n";
    echo "- Better context preservation between chunks\n";
    echo "- Optimized chunk sizes for DeepSeek API\n";
    
} catch (Exception $e) {
    echo "Error during migration: " . $e->getMessage() . "\n";
    exit(1);
}
?>
