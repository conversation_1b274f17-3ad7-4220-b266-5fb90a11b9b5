<?php
/**
 * Manual Novel Entry Page
 * Allows users to manually create novel entries
 */

require_once 'config/config.php';
require_once 'includes/header.php';

renderHeader('Manual Novel Entry');
?>

<?php 
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php'; 
renderNavigation('manual-entry');
?>

<div class="container mt-4">
    <!-- <PERSON> Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>
            <i class="fas fa-plus-circle me-2"></i>
            Manual Novel Entry
        </h2>
        <div>
            <a href="novels.php" class="btn btn-outline-secondary me-2">
                <i class="fas fa-list me-1"></i>
                My Novels
            </a>
            <a href="index.php" class="btn btn-outline-primary">
                <i class="fas fa-search me-1"></i>
                URL Preview
            </a>
        </div>
    </div>

    <!-- Manual Entry Form -->
    <div class="card shadow">
        <div class="card-header bg-success text-white">
            <h4 class="mb-0">
                <i class="fas fa-edit me-2"></i>
                Create New Novel Entry
            </h4>
        </div>
        <div class="card-body">
            <form id="manual-novel-form">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="original-title" class="form-label">
                                <i class="fas fa-book me-1"></i>
                                Original Title <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="original-title" 
                                   placeholder="Enter the original novel title" required>
                            <div class="form-text">The title in its original language</div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="translated-title" class="form-label">
                                <i class="fas fa-language me-1"></i>
                                Translated Title
                            </label>
                            <input type="text" class="form-control" id="translated-title" 
                                   placeholder="Enter translated title (optional)">
                            <div class="form-text">English translation of the title</div>
                        </div>
                    </div>
                </div>

                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="author" class="form-label">
                                <i class="fas fa-user me-1"></i>
                                Author
                            </label>
                            <input type="text" class="form-control" id="author" 
                                   placeholder="Enter author name">
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label for="source-url" class="form-label">
                                <i class="fas fa-link me-1"></i>
                                Source URL
                            </label>
                            <input type="url" class="form-control" id="source-url" 
                                   placeholder="https://example.com/novel-source">
                            <div class="form-text">Original source where the novel can be found</div>
                        </div>
                    </div>
                </div>

                <div class="mb-3">
                    <label for="original-synopsis" class="form-label">
                        <i class="fas fa-align-left me-1"></i>
                        Original Synopsis/Description <span class="text-danger">*</span>
                    </label>
                    <textarea class="form-control" id="original-synopsis" rows="6" 
                              placeholder="Enter the original synopsis or description of the novel" required></textarea>
                    <div class="form-text">The synopsis in its original language</div>
                </div>

                <div class="mb-3">
                    <label for="translated-synopsis" class="form-label">
                        <i class="fas fa-language me-1"></i>
                        Translated Synopsis/Description
                    </label>
                    <textarea class="form-control" id="translated-synopsis" rows="6" 
                              placeholder="Enter translated synopsis (optional)"></textarea>
                    <div class="form-text">English translation of the synopsis</div>
                </div>

                <div class="mb-3">
                    <label for="publication-date" class="form-label">
                        <i class="fas fa-calendar me-1"></i>
                        Publication Date
                    </label>
                    <input type="date" class="form-control" id="publication-date">
                </div>

                <div class="d-flex justify-content-between">
                    <button type="button" class="btn btn-secondary" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-2"></i>
                        Cancel
                    </button>
                    <button type="submit" class="btn btn-success">
                        <i class="fas fa-save me-2"></i>
                        Create Novel
                    </button>
                </div>
            </form>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <div id="message-container" class="mt-4" style="display: none;">
        <!-- Messages will be inserted here -->
    </div>

    <!-- Help Section -->
    <div class="card mt-4">
        <div class="card-header bg-info text-white">
            <h5 class="mb-0">
                <i class="fas fa-info-circle me-2"></i>
                Manual Entry Guide
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <h6>When to Use Manual Entry</h6>
                    <ul class="list-unstyled">
                        <li><i class="fas fa-check text-success me-2"></i>Novels from unsupported websites</li>
                        <li><i class="fas fa-check text-success me-2"></i>Sites with anti-bot protection</li>
                        <li><i class="fas fa-check text-success me-2"></i>Personal translations or drafts</li>
                        <li><i class="fas fa-check text-success me-2"></i>Offline content</li>
                    </ul>
                </div>
                <div class="col-md-6">
                    <h6>Next Steps</h6>
                    <ol>
                        <li>Create the novel entry with this form</li>
                        <li>Add chapters manually using the chapter entry page</li>
                        <li>Use the translation system to translate content</li>
                        <li>Manage names and consistency through the name dictionary</li>
                    </ol>
                </div>
            </div>
        </div>
    </div>
</div>

<?php renderFooter(['assets/js/manual-entry.js']); ?>
