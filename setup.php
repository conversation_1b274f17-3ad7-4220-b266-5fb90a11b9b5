<?php
/**
 * Database Setup Script
 * Novel Translation Application
 */

// Don't load config.php yet as it might try to connect to non-existent database
// require_once 'config/config.php';

// Define basic constants needed for setup
define('APP_NAME', 'Novel Translator');
define('APP_ROOT', dirname(__FILE__));

// Basic error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Setup - <?= APP_NAME ?></title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-light">
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card shadow">
                    <div class="card-header bg-primary text-white">
                        <h4 class="mb-0">
                            <i class="fas fa-cog me-2"></i>
                            Database Setup
                        </h4>
                    </div>
                    <div class="card-body">
                        <?php
                        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
                            setupDatabase();
                        } else {
                            showSetupForm();
                        }
                        ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>

<?php
function showSetupForm() {
    ?>
    <div class="mb-4">
        <h5>Welcome to Novel Translator Setup</h5>
        <p class="text-muted">
            This setup will create the necessary database and tables for the Novel Translation Application.
        </p>
    </div>

    <div class="alert alert-info">
        <h6><i class="fas fa-info-circle me-2"></i>Prerequisites</h6>
        <ul class="mb-0">
            <li>MySQL server running</li>
            <li>PHP 8.2 or higher</li>
            <li>cURL extension enabled</li>
            <li>PDO MySQL extension enabled</li>
        </ul>
    </div>

    <div class="alert alert-warning">
        <h6><i class="fas fa-exclamation-triangle me-2"></i>Database Configuration</h6>
        <p class="mb-2">Current database settings:</p>
        <ul class="mb-0">
            <li><strong>Host:</strong> localhost</li>
            <li><strong>Database:</strong> novel_translator</li>
            <li><strong>Username:</strong> root</li>
            <li><strong>Password:</strong> (empty)</li>
        </ul>
        <small class="text-muted">
            You can modify these settings in <code>config/database.php</code>
        </small>
    </div>

    <form method="POST">
        <div class="d-grid">
            <button type="submit" class="btn btn-primary btn-lg">
                <i class="fas fa-play me-2"></i>
                Run Database Setup
            </button>
        </div>
    </form>
    <?php
}

function setupDatabase() {
    echo '<div class="setup-log">';

    try {
        echo '<div class="alert alert-info">';
        echo '<h6><i class="fas fa-spinner fa-spin me-2"></i>Setting up database...</h6>';
        echo '</div>';

        // Read SQL schema
        $sqlFile = __DIR__ . '/sql/schema.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception("SQL schema file not found: {$sqlFile}");
        }

        $sql = file_get_contents($sqlFile);
        if (!$sql) {
            throw new Exception("Failed to read SQL schema file");
        }

        // Connect to MySQL without specifying database initially
        $dsn = "mysql:host=localhost;charset=utf8mb4";
        $pdo = new PDO($dsn, 'root', '', [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
        ]);

        echo '<div class="progress mb-3">';
        echo '<div class="progress-bar progress-bar-striped progress-bar-animated" style="width: 10%"></div>';
        echo '</div>';

        // Split SQL into individual statements
        $statements = array_filter(
            array_map('trim', explode(';', $sql)),
            function($stmt) { return !empty($stmt) && !preg_match('/^--/', $stmt); }
        );

        $totalStatements = count($statements);
        $executedStatements = 0;
        $createdTables = [];
        $insertedRows = 0;

        echo '<div class="setup-progress mb-3">';
        echo '<small class="text-muted">Executing SQL statements...</small>';
        echo '</div>';

        foreach ($statements as $statement) {
            $trimmedStatement = trim($statement);
            if ($trimmedStatement) {
                try {
                    $pdo->exec($trimmedStatement);
                    $executedStatements++;

                    // Track what was created
                    if (stripos($trimmedStatement, 'CREATE DATABASE') !== false) {
                        echo '<div class="text-success small">✅ Database created</div>';
                    } elseif (stripos($trimmedStatement, 'CREATE TABLE') !== false) {
                        preg_match('/CREATE TABLE\s+(\w+)/i', $trimmedStatement, $matches);
                        if (isset($matches[1])) {
                            $createdTables[] = $matches[1];
                            echo '<div class="text-success small">✅ Table created: ' . $matches[1] . '</div>';
                        }
                    } elseif (stripos($trimmedStatement, 'INSERT INTO') !== false) {
                        $insertedRows++;
                        echo '<div class="text-info small">📝 Default data inserted</div>';
                    }

                    $progress = 10 + (($executedStatements / $totalStatements) * 80);
                    echo '<script>
                        document.querySelector(".progress-bar").style.width = "' . $progress . '%";
                    </script>';

                    // Flush output for real-time updates
                    if (ob_get_level()) {
                        ob_flush();
                    }
                    flush();

                } catch (PDOException $e) {
                    // Skip if database/table already exists, but log it
                    if (strpos($e->getMessage(), 'already exists') !== false) {
                        echo '<div class="text-warning small">⚠️ Already exists: ' . substr($trimmedStatement, 0, 50) . '...</div>';
                        $executedStatements++; // Count as executed
                    } else {
                        throw new Exception("SQL execution failed: " . $e->getMessage() . "\nStatement: " . substr($trimmedStatement, 0, 100));
                    }
                }
            }
        }

        // Update progress to 90%
        echo '<script>document.querySelector(".progress-bar").style.width = "90%";</script>';

        echo '<div class="alert alert-success">';
        echo '<h6><i class="fas fa-check-circle me-2"></i>Database setup completed successfully!</h6>';
        echo '<ul class="mb-0">';
        echo '<li>Database "novel_translator" created/verified</li>';
        echo '<li>' . count($createdTables) . ' tables created: ' . implode(', ', $createdTables) . '</li>';
        echo '<li>' . $insertedRows . ' default preference records inserted</li>';
        echo '</ul>';
        echo '</div>';

        // Test database connection using the same PDO connection
        // Switch to the novel_translator database
        $pdo->exec("USE novel_translator");

        // Test the connection and verify tables
        $testQuery = $pdo->query("SELECT COUNT(*) as count FROM user_preferences");
        $preferenceCount = $testQuery->fetch()['count'];

        $tablesQuery = $pdo->query("SHOW TABLES");
        $tables = $tablesQuery->fetchAll(PDO::FETCH_COLUMN);

        echo '<div class="alert alert-info">';
        echo '<h6><i class="fas fa-database me-2"></i>Database Connection Test</h6>';
        echo '<p class="mb-1">✅ Successfully connected to database</p>';
        echo '<p class="mb-1">✅ Found ' . $preferenceCount . ' default preferences</p>';
        echo '<p class="mb-0">✅ Verified ' . count($tables) . ' tables: ' . implode(', ', $tables) . '</p>';
        echo '</div>';

        // Final progress update
        echo '<script>document.querySelector(".progress-bar").style.width = "95%";</script>';

        // Test application readiness (without loading config.php)
        echo '<div class="alert alert-success">';
        echo '<h6><i class="fas fa-cogs me-2"></i>Application Readiness Check</h6>';
        echo '<p class="mb-0">✅ Database and tables created successfully</p>';
        echo '<p class="mb-0">✅ Default data inserted</p>';
        echo '<p class="mb-0">✅ Ready for application use</p>';
        echo '<div class="mt-2 small text-muted">';
        echo 'Note: Full application testing will be available after setup completion';
        echo '</div>';
        echo '</div>';

        // Check required PHP extensions
        echo '<div class="alert alert-info">';
        echo '<h6><i class="fas fa-php me-2"></i>PHP Environment Check</h6>';
        echo '<ul class="mb-0">';
        echo '<li>PHP Version: ' . PHP_VERSION . ' ✅</li>';
        echo '<li>cURL Extension: ' . (extension_loaded('curl') ? '✅ Enabled' : '❌ Disabled') . '</li>';
        echo '<li>PDO Extension: ' . (extension_loaded('pdo') ? '✅ Enabled' : '❌ Disabled') . '</li>';
        echo '<li>PDO MySQL: ' . (extension_loaded('pdo_mysql') ? '✅ Enabled' : '❌ Disabled') . '</li>';
        echo '<li>DOM Extension: ' . (extension_loaded('dom') ? '✅ Enabled' : '❌ Disabled') . '</li>';
        echo '<li>libxml Extension: ' . (extension_loaded('libxml') ? '✅ Enabled' : '❌ Disabled') . '</li>';
        echo '</ul>';
        echo '</div>';

        // Final progress
        echo '<script>document.querySelector(".progress-bar").style.width = "100%";</script>';

        echo '<div class="alert alert-success">';
        echo '<h6><i class="fas fa-rocket me-2"></i>Setup Complete!</h6>';
        echo '<p>Your Novel Translation Application is ready to use.</p>';
        echo '<div class="d-grid gap-2 d-md-flex">';
        echo '<a href="verify-db.php" class="btn btn-info me-md-2">';
        echo '<i class="fas fa-check me-2"></i>Verify Database';
        echo '</a>';
        echo '<a href="test.php" class="btn btn-warning me-md-2">';
        echo '<i class="fas fa-vial me-2"></i>Run Tests';
        echo '</a>';
        echo '<a href="index.php" class="btn btn-success">';
        echo '<i class="fas fa-arrow-right me-2"></i>Go to Application';
        echo '</a>';
        echo '</div>';
        echo '</div>';

    } catch (Exception $e) {
        echo '<div class="alert alert-danger">';
        echo '<h6><i class="fas fa-exclamation-triangle me-2"></i>Setup Failed</h6>';
        echo '<p><strong>Error:</strong> ' . htmlspecialchars($e->getMessage()) . '</p>';

        // Provide specific troubleshooting based on error type
        $errorMsg = $e->getMessage();
        if (strpos($errorMsg, 'Access denied') !== false) {
            echo '<div class="mt-2">';
            echo '<h6>Troubleshooting: Database Access</h6>';
            echo '<ul class="mb-0">';
            echo '<li>Check if MySQL server is running</li>';
            echo '<li>Verify username and password in <code>config/database.php</code></li>';
            echo '<li>Ensure the MySQL user has CREATE DATABASE privileges</li>';
            echo '</ul>';
            echo '</div>';
        } elseif (strpos($errorMsg, 'Connection refused') !== false) {
            echo '<div class="mt-2">';
            echo '<h6>Troubleshooting: Connection</h6>';
            echo '<ul class="mb-0">';
            echo '<li>Check if MySQL server is running on localhost:3306</li>';
            echo '<li>Verify the database host in <code>config/database.php</code></li>';
            echo '<li>Check firewall settings</li>';
            echo '</ul>';
            echo '</div>';
        } elseif (strpos($errorMsg, 'SQL execution failed') !== false) {
            echo '<div class="mt-2">';
            echo '<h6>Troubleshooting: SQL Error</h6>';
            echo '<ul class="mb-0">';
            echo '<li>Check the SQL schema file for syntax errors</li>';
            echo '<li>Verify MySQL version compatibility</li>';
            echo '<li>Check if tables already exist and need to be dropped</li>';
            echo '</ul>';
            echo '</div>';
        }

        echo '</div>';

        echo '<div class="mt-3">';
        echo '<button onclick="location.reload()" class="btn btn-primary me-2">';
        echo '<i class="fas fa-redo me-2"></i>Try Again';
        echo '</button>';
        echo '<a href="test.php" class="btn btn-info">';
        echo '<i class="fas fa-vial me-2"></i>Run Diagnostics';
        echo '</a>';
        echo '</div>';
    }
    
    echo '</div>';
}
?>
