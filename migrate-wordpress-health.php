<?php
/**
 * Database Migration for WordPress Connection Health Monitoring
 * Run this script to add the wordpress_connection_log table
 */

require_once 'config/config.php';

echo "WordPress Connection Health Migration\n";
echo "====================================\n\n";

try {
    $db = Database::getInstance();
    
    // Check if table already exists
    $tableExists = $db->fetchOne(
        "SELECT COUNT(*) as count FROM information_schema.tables 
         WHERE table_schema = DATABASE() AND table_name = 'wordpress_connection_log'"
    );
    
    if ($tableExists['count'] > 0) {
        echo "✓ Table 'wordpress_connection_log' already exists.\n";
        
        // Check if we need to add any missing columns
        $columns = $db->fetchAll(
            "SELECT COLUMN_NAME FROM information_schema.columns 
             WHERE table_schema = DATABASE() AND table_name = 'wordpress_connection_log'"
        );
        
        $existingColumns = array_column($columns, 'COLUMN_NAME');
        $requiredColumns = ['id', 'profile_name', 'site_url', 'attempt_type', 'post_type', 'item_id', 'success', 'error_message', 'metadata', 'memory_usage', 'created_at'];
        
        $missingColumns = array_diff($requiredColumns, $existingColumns);
        
        if (empty($missingColumns)) {
            echo "✓ All required columns are present.\n";
        } else {
            echo "⚠ Missing columns detected: " . implode(', ', $missingColumns) . "\n";
            echo "Adding missing columns...\n";
            
            // Add missing columns (this is a simplified approach)
            foreach ($missingColumns as $column) {
                switch ($column) {
                    case 'metadata':
                        $db->query("ALTER TABLE wordpress_connection_log ADD COLUMN metadata JSON NULL");
                        echo "✓ Added 'metadata' column\n";
                        break;
                    case 'memory_usage':
                        $db->query("ALTER TABLE wordpress_connection_log ADD COLUMN memory_usage BIGINT NULL");
                        echo "✓ Added 'memory_usage' column\n";
                        break;
                }
            }
        }
    } else {
        echo "Creating 'wordpress_connection_log' table...\n";
        
        $createTableSQL = "
        CREATE TABLE wordpress_connection_log (
            id INT AUTO_INCREMENT PRIMARY KEY,
            profile_name VARCHAR(255) NOT NULL,
            site_url VARCHAR(500) NOT NULL,
            attempt_type ENUM('connection', 'posting') NOT NULL,
            post_type VARCHAR(50) NULL,
            item_id INT NULL,
            success TINYINT(1) NOT NULL DEFAULT 0,
            error_message TEXT NULL,
            metadata JSON NULL,
            memory_usage BIGINT NULL,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            
            INDEX idx_profile_created (profile_name, created_at),
            INDEX idx_success_created (success, created_at),
            INDEX idx_attempt_type (attempt_type),
            INDEX idx_created_at (created_at)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci";
        
        $db->query($createTableSQL);
        echo "✓ Table 'wordpress_connection_log' created successfully.\n";
    }
    
    // Test the connection monitor
    echo "\nTesting WordPress Connection Monitor...\n";
    
    try {
        $monitor = new WordPressConnectionMonitor();
        
        // Test logging a connection attempt
        $monitor->logConnectionAttempt('test_profile', 'https://example.com', true);
        echo "✓ Connection logging test successful.\n";
        
        // Test getting stats
        $stats = $monitor->getConnectionStats('test_profile', 1);
        if ($stats['success']) {
            echo "✓ Statistics retrieval test successful.\n";
        } else {
            echo "⚠ Statistics retrieval test failed: " . $stats['error'] . "\n";
        }
        
        // Clean up test data
        $db->query("DELETE FROM wordpress_connection_log WHERE profile_name = 'test_profile'");
        echo "✓ Test data cleaned up.\n";
        
    } catch (Exception $e) {
        echo "✗ Connection monitor test failed: " . $e->getMessage() . "\n";
    }
    
    echo "\n====================================\n";
    echo "Migration completed successfully!\n\n";
    
    echo "Next steps:\n";
    echo "1. The WordPress connection monitoring is now active\n";
    echo "2. Connection attempts and posting activities will be logged automatically\n";
    echo "3. You can view statistics via the API: api/wordpress-health.php\n";
    echo "4. Logs will be automatically cleaned after 30 days\n\n";
    
    echo "API endpoints available:\n";
    echo "- GET api/wordpress-health.php?action=stats&profile=PROFILE_NAME\n";
    echo "- GET api/wordpress-health.php?action=issues&profile=PROFILE_NAME\n";
    echo "- GET api/wordpress-health.php?action=profiles\n";
    echo "- GET api/wordpress-health.php?action=recent_activity\n";
    echo "- POST api/wordpress-health.php (action: clean_logs, test_all_profiles)\n\n";
    
} catch (Exception $e) {
    echo "✗ Migration failed: " . $e->getMessage() . "\n";
    echo "Please check your database connection and try again.\n";
    exit(1);
}
?>
