<?php
/**
 * Database Configuration
 * Novel Translation Application
 */

class Database {
    private static $instance = null;
    private $connection;
    private $lastConnectionTime;
    private $connectionTimeout = 28800; // 8 hours (MySQL default wait_timeout)

    // Database configuration
    private const DB_HOST = 'localhost';
    private const DB_NAME = 'novel_translator';
    private const DB_USER = 'root';
    private const DB_PASS = '';
    private const DB_CHARSET = 'utf8mb4';

    private function __construct() {
        $this->connect();
    }

    private function connect() {
        try {
            $dsn = "mysql:host=" . self::DB_HOST . ";dbname=" . self::DB_NAME . ";charset=" . self::DB_CHARSET;
            $options = [
                PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES => false,
                PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES " . self::DB_CHARSET,
                PDO::ATTR_PERSISTENT => false, // Disable persistent connections for better control
                PDO::MYSQL_ATTR_USE_BUFFERED_QUERY => true
            ];

            $this->connection = new PDO($dsn, self::DB_USER, self::DB_PASS, $options);
            $this->lastConnectionTime = time();

            // Log successful connection for debugging
            error_log("Database: New connection established at " . date('Y-m-d H:i:s'));

        } catch (PDOException $e) {
            error_log("Database connection failed: " . $e->getMessage());
            throw new Exception("Database connection failed: " . $e->getMessage());
        }
    }

    public static function getInstance(): Database {
        if (self::$instance === null) {
            self::$instance = new Database();
        }
        return self::$instance;
    }

    public function getConnection(): PDO {
        $this->ensureConnection();
        return $this->connection;
    }

    /**
     * Ensure database connection is alive and reconnect if necessary
     */
    private function ensureConnection() {
        $currentTime = time();

        // Check if connection might have timed out
        if (($currentTime - $this->lastConnectionTime) > ($this->connectionTimeout - 300)) { // 5 minutes buffer
            if (!$this->isConnectionAlive()) {
                error_log("Database: Connection lost, reconnecting...");
                $this->connect();
            }
        }
    }

    /**
     * Check if database connection is still alive
     */
    private function isConnectionAlive(): bool {
        try {
            if (!$this->connection) {
                return false;
            }

            // Simple query to test connection
            $stmt = $this->connection->query('SELECT 1');
            $this->lastConnectionTime = time();
            return $stmt !== false;

        } catch (PDOException $e) {
            error_log("Database: Connection test failed: " . $e->getMessage());
            return false;
        }
    }
    
    public function query(string $sql, array $params = []): PDOStatement {
        $this->ensureConnection();

        try {
            $stmt = $this->connection->prepare($sql);
            $stmt->execute($params);
            $this->lastConnectionTime = time(); // Update last activity time
            return $stmt;
        } catch (PDOException $e) {
            // Check if it's a connection error and retry once
            if ($this->isConnectionError($e)) {
                error_log("Database: Query failed due to connection error, retrying: " . $e->getMessage());
                $this->connect(); // Force reconnection

                try {
                    $stmt = $this->connection->prepare($sql);
                    $stmt->execute($params);
                    $this->lastConnectionTime = time();
                    return $stmt;
                } catch (PDOException $retryException) {
                    error_log("Database: Retry also failed: " . $retryException->getMessage());
                    throw new Exception("Query failed after retry: " . $retryException->getMessage());
                }
            }

            error_log("Database: Query failed: " . $e->getMessage() . " SQL: " . $sql);
            throw new Exception("Query failed: " . $e->getMessage());
        }
    }

    /**
     * Check if PDO exception is related to connection issues
     */
    private function isConnectionError(PDOException $e): bool {
        $connectionErrorCodes = [
            '2006', // MySQL server has gone away
            '2013', // Lost connection to MySQL server during query
            '2003', // Can't connect to MySQL server
            'HY000' // General error that might include connection issues
        ];

        foreach ($connectionErrorCodes as $code) {
            if (strpos($e->getCode(), $code) !== false ||
                strpos($e->getMessage(), 'server has gone away') !== false ||
                strpos($e->getMessage(), 'Lost connection') !== false) {
                return true;
            }
        }

        return false;
    }
    
    public function fetchOne(string $sql, array $params = []): ?array {
        $stmt = $this->query($sql, $params);
        $result = $stmt->fetch();
        return $result ?: null;
    }
    
    public function fetchAll(string $sql, array $params = []): array {
        $stmt = $this->query($sql, $params);
        return $stmt->fetchAll();
    }
    
    public function insert(string $table, array $data): int {
        $columns = implode(', ', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return (int) $this->connection->lastInsertId();
    }
    
    public function update(string $table, array $data, string $where, array $whereParams = []): int {
        $setParts = [];
        $setParams = [];
        $paramIndex = 1;

        // Use positional parameters for SET clause
        foreach ($data as $column => $value) {
            $setParts[] = "{$column} = ?";
            $setParams[] = $value;
        }
        $setClause = implode(', ', $setParts);

        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($setParams, $whereParams);

        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function delete(string $table, string $where, array $params = []): int {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        $stmt = $this->query($sql, $params);
        return $stmt->rowCount();
    }
    
    public function beginTransaction(): bool {
        return $this->connection->beginTransaction();
    }
    
    public function commit(): bool {
        return $this->connection->commit();
    }
    
    public function rollback(): bool {
        return $this->connection->rollBack();
    }
}
