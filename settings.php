<?php
/**
 * Settings Page
 * Configuration interface for WordPress integration and other application settings
 */

require_once 'config/config.php';
require_once 'includes/header.php';

// Legacy WordPress settings handling removed - now using profile-based system only

// Initialize database connection for profile management
$db = Database::getInstance();

renderHeader('Settings');
?>

<?php 
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php'; 
renderNavigation('settings');
?>

<div class="container mt-4">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">
                        <i class="fas fa-cog me-2"></i>
                        Application Settings
                    </h4>
                </div>
                <div class="card-body">

                    
                    <!-- WordPress Profiles Management -->
                    <div class="mb-4">
                        <h5 class="border-bottom pb-2">
                            <i class="fab fa-wordpress me-2"></i>
                            WordPress Domain Profiles
                        </h5>

                        <p class="text-muted small">
                            Manage multiple WordPress domain profiles for posting content to different WordPress sites.
                            This allows you to easily switch between different WordPress configurations and post the same content to multiple domains.
                        </p>

                        <?php
                        // Get WordPress profiles and statistics
                        require_once 'classes/WordPressProfileService.php';
                        $profileService = new WordPressProfileService();
                        $profiles = $profileService->getAllProfiles();
                        $profileStats = $profileService->getPostingStatistics();
                        ?>

                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">Configured Profiles (<?= count($profiles) ?>)</h6>
                            <button type="button" class="btn btn-primary btn-sm" data-action="create-profile">
                                <i class="fas fa-plus me-1"></i>
                                Create New Profile
                            </button>
                        </div>

                        <?php if (empty($profiles)): ?>
                            <div class="alert alert-info">
                                <i class="fas fa-info-circle me-2"></i>
                                No WordPress profiles configured yet. Create your first profile to start posting to WordPress.
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-sm table-bordered">
                                    <thead class="table-light">
                                        <tr>
                                            <th>Profile Name</th>
                                            <th>Site URL</th>
                                            <th>Status</th>
                                            <th>Posts</th>
                                            <th>Actions</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($profiles as $profile): ?>
                                            <?php
                                            $stats = array_filter($profileStats, function($s) use ($profile) {
                                                return $s['id'] == $profile['id'];
                                            });
                                            $stat = !empty($stats) ? reset($stats) : ['novels_posted' => 0, 'chapters_posted' => 0];
                                            ?>
                                            <tr>
                                                <td>
                                                    <strong><?= htmlspecialchars($profile['profile_name']) ?></strong>
                                                    <?php if (!$profile['is_active']): ?>
                                                        <span class="badge bg-secondary ms-1">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small><?= htmlspecialchars($profile['site_url']) ?></small>
                                                </td>
                                                <td>
                                                    <?php if ($profile['is_active']): ?>
                                                        <span class="badge bg-success">Active</span>
                                                    <?php else: ?>
                                                        <span class="badge bg-secondary">Inactive</span>
                                                    <?php endif; ?>
                                                </td>
                                                <td>
                                                    <small class="text-muted">
                                                        <?= $stat['novels_posted'] ?> novels, <?= $stat['chapters_posted'] ?> chapters
                                                    </small>
                                                </td>
                                                <td>
                                                    <div class="btn-group btn-group-sm">
                                                        <button type="button" class="btn btn-outline-primary btn-sm"
                                                                data-action="edit-profile" data-profile-id="<?= $profile['id'] ?>"
                                                                title="Edit Profile">
                                                            <i class="fas fa-edit"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-info btn-sm"
                                                                data-action="test-profile" data-profile-id="<?= $profile['id'] ?>"
                                                                title="Test Connection">
                                                            <i class="fas fa-plug"></i>
                                                        </button>
                                                        <button type="button" class="btn btn-outline-danger btn-sm"
                                                                data-action="delete-profile" data-profile-id="<?= $profile['id'] ?>"
                                                                title="Delete Profile">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </div>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>


                </div>
            </div>

            <!-- WordPress Setup Guide -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="fas fa-question-circle me-2"></i>
                        WordPress Setup Guide
                    </h5>
                </div>
                <div class="card-body">
                    <h6>1. Create Application Password</h6>
                    <ol>
                        <li>Log into your WordPress admin panel</li>
                        <li>Go to <strong>Users → Your Profile</strong></li>
                        <li>Scroll down to <strong>Application Passwords</strong></li>
                        <li>Enter a name like "Novel Translator" and click <strong>Add New Application Password</strong></li>
                        <li>Copy the generated password and paste it in the profile form above</li>
                    </ol>

                    <h6>2. Profile-Based WordPress Integration</h6>
                    <ul>
                        <li><strong>Create multiple profiles</strong> for different WordPress sites</li>
                        <li><strong>Each profile</strong> can have different post types, categories, and settings</li>
                        <li><strong>Post the same content</strong> to multiple WordPress domains easily</li>
                        <li><strong>Track posting history</strong> per profile and domain</li>
                    </ul>

                    <h6>3. Post Structure</h6>
                    <ul>
                        <li><strong>Novels</strong> will be created as pages, posts, or custom post types (configurable per profile)</li>
                        <li><strong>Chapters</strong> will be created as posts or custom post types with the novel title as prefix</li>
                        <li>Both original and translated titles can be included (configurable per profile)</li>
                        <li>Content will be properly formatted with paragraphs and furigana support</li>
                    </ul>

                    <h6>4. Testing</h6>
                    <p>Use the test connection button (plug icon) next to each profile to verify your WordPress credentials and settings.</p>

                </div>
            </div>
        </div>
    </div>
</div>



<?php renderFooter(['assets/js/wordpress-profiles.js']); ?>
