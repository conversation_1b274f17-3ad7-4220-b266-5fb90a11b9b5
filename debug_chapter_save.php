<?php
/**
 * Debug script for chapter save issues
 * This script helps identify where the JSON error is occurring
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

try {
    // Include config
    require_once 'config/config.php';
    
    echo "=== Chapter Save Debug Script ===\n";
    echo "PHP Version: " . PHP_VERSION . "\n";
    echo "Memory Limit: " . ini_get('memory_limit') . "\n";
    echo "Max Execution Time: " . ini_get('max_execution_time') . "\n\n";
    
    // Test 1: Database connection
    echo "1. Testing Database Connection...\n";
    try {
        $db = Database::getInstance();
        echo "   ✓ Database connection successful\n";
    } catch (Exception $e) {
        echo "   ✗ Database connection failed: " . $e->getMessage() . "\n";
        exit(1);
    }
    
    // Test 2: NovelManager initialization
    echo "\n2. Testing NovelManager initialization...\n";
    try {
        $novelManager = new NovelManager();
        echo "   ✓ NovelManager initialized successfully\n";
    } catch (Exception $e) {
        echo "   ✗ NovelManager initialization failed: " . $e->getMessage() . "\n";
        exit(1);
    }
    
    // Test 3: ChapterChunker functionality
    echo "\n3. Testing ChapterChunker functionality...\n";
    try {
        $chunker = new ChapterChunker();
        $testContent = "这是一个测试内容。This is test content for checking the chunker functionality.";
        $stats = $chunker->calculateContentStats($testContent);
        echo "   ✓ ChapterChunker working correctly\n";
        echo "   Content stats: " . json_encode($stats) . "\n";
    } catch (Exception $e) {
        echo "   ✗ ChapterChunker failed: " . $e->getMessage() . "\n";
        exit(1);
    }
    
    // Test 4: Check for a test novel and chapter
    echo "\n4. Looking for test data...\n";
    try {
        $novels = $db->fetchAll("SELECT id, original_title, platform FROM novels LIMIT 5");
        if (empty($novels)) {
            echo "   ! No novels found in database\n";
        } else {
            echo "   Found " . count($novels) . " novels:\n";
            foreach ($novels as $novel) {
                echo "   - Novel ID {$novel['id']}: {$novel['original_title']} ({$novel['platform']})\n";
                
                // Check for chapters
                $chapters = $db->fetchAll(
                    "SELECT id, chapter_number, original_title, original_content IS NOT NULL as has_content 
                     FROM chapters WHERE novel_id = ? LIMIT 3", 
                    [$novel['id']]
                );
                
                if (!empty($chapters)) {
                    foreach ($chapters as $chapter) {
                        $contentStatus = $chapter['has_content'] ? 'HAS CONTENT' : 'NO CONTENT';
                        echo "     Chapter {$chapter['chapter_number']}: {$chapter['original_title']} ({$contentStatus})\n";
                    }
                }
            }
        }
    } catch (Exception $e) {
        echo "   ✗ Database query failed: " . $e->getMessage() . "\n";
    }
    
    // Test 5: Test crawler initialization
    echo "\n5. Testing crawler initialization...\n";
    $platforms = ['shuba69', 'kakuyomu', 'syosetu'];
    foreach ($platforms as $platform) {
        try {
            $crawler = $novelManager->getCrawler($platform);
            echo "   ✓ {$platform} crawler initialized successfully\n";
        } catch (Exception $e) {
            echo "   ✗ {$platform} crawler failed: " . $e->getMessage() . "\n";
        }
    }

    // Test 6: Test actual chapter save functionality
    echo "\n6. Testing chapter save functionality...\n";
    try {
        // Find a chapter without content to test with
        $testChapter = $db->fetchOne(
            "SELECT c.*, n.platform FROM chapters c
             JOIN novels n ON c.novel_id = n.id
             WHERE c.original_content IS NULL
             AND c.chapter_url IS NOT NULL
             LIMIT 1"
        );

        if ($testChapter) {
            echo "   Found test chapter: Novel ID {$testChapter['novel_id']}, Chapter {$testChapter['chapter_number']}\n";
            echo "   Platform: {$testChapter['platform']}\n";
            echo "   Chapter URL: {$testChapter['chapter_url']}\n";

            // Test the save operation
            echo "   Testing saveChapter method...\n";
            $result = $novelManager->saveChapter($testChapter['novel_id'], $testChapter['chapter_number']);

            if ($result['success']) {
                echo "   ✓ Chapter save test successful\n";
                echo "   Content length: " . strlen($result['chapter']['original_content'] ?? '') . " characters\n";
            } else {
                echo "   ✗ Chapter save test failed: " . $result['error'] . "\n";
            }
        } else {
            echo "   ! No test chapters found (all chapters already have content)\n";
        }
    } catch (Exception $e) {
        echo "   ✗ Chapter save test failed with exception: " . $e->getMessage() . "\n";
    }
    
    echo "\n=== Debug Complete ===\n";
    echo "If all tests pass, the issue might be in the content extraction or specific chapter data.\n";
    echo "Check the error logs for more details when attempting to save a chapter.\n";
    
} catch (Exception $e) {
    echo "\n✗ Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "\n✗ Fatal PHP error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

// Get any unexpected output
$output = ob_get_clean();
echo $output;
?>
