<?php
require_once 'config/config.php';
require_once 'classes/BaseCrawler.php';
require_once 'crawlers/Shuba69Crawler.php';

$crawler = new Shuba69Crawler();
$chapterUrl = 'https://69shuba.cx/txt/44425/29853970';

echo "Debugging content extraction for: $chapterUrl\n\n";

try {
    $chapterData = $crawler->getChapterContent($chapterUrl);
    $content = $chapterData['original_content'];
    
    echo "Raw content (first 1000 bytes):\n";
    echo "---\n";
    echo substr($content, 0, 1000) . "\n";
    echo "---\n\n";
    
    echo "Hex dump (first 200 bytes):\n";
    echo bin2hex(substr($content, 0, 200)) . "\n\n";
    
    echo "Character analysis:\n";
    echo "Length: " . strlen($content) . " bytes\n";
    echo "MB Length: " . mb_strlen($content, 'UTF-8') . " characters\n";
    echo "Detected encoding: " . mb_detect_encoding($content) . "\n";
    echo "Valid UTF-8: " . (mb_check_encoding($content, 'UTF-8') ? 'Yes' : 'No') . "\n";
    echo "Valid GBK: " . (mb_check_encoding($content, 'GBK') ? 'Yes' : 'No') . "\n";
    echo "Valid GB2312: " . (mb_check_encoding($content, 'GB2312') ? 'Yes' : 'No') . "\n";
    
    // Try converting from different encodings
    echo "\nTrying different encoding conversions:\n";
    
    $encodings = ['GBK', 'GB2312', 'UTF-8'];
    foreach ($encodings as $encoding) {
        echo "From $encoding to UTF-8:\n";
        $converted = mb_convert_encoding($content, 'UTF-8', $encoding);
        $chineseCount = preg_match_all('/[\x{4E00}-\x{9FFF}]/u', $converted);
        echo "Chinese chars: $chineseCount, Valid UTF-8: " . (mb_check_encoding($converted, 'UTF-8') ? 'Yes' : 'No') . "\n";
        if ($chineseCount > 0) {
            echo "Sample: " . mb_substr($converted, 0, 100, 'UTF-8') . "\n";
        }
        echo "\n";
    }
    
} catch (Exception $e) {
    echo "Error: " . $e->getMessage() . "\n";
}
?>
