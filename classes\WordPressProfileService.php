<?php
/**
 * WordPress Profile Service
 * Manages WordPress domain profiles for multi-domain posting
 */

class WordPressProfileService {
    private $db;
    
    public function __construct() {
        $this->db = Database::getInstance();
    }
    
    /**
     * Get all WordPress profiles
     */
    public function getAllProfiles(bool $activeOnly = false): array {
        $sql = "SELECT * FROM wordpress_profiles";
        $params = [];
        
        if ($activeOnly) {
            $sql .= " WHERE is_active = 1";
        }
        
        $sql .= " ORDER BY profile_name ASC";
        
        return $this->db->fetchAll($sql, $params);
    }
    
    /**
     * Get a specific profile by ID
     */
    public function getProfile(int $profileId): ?array {
        return $this->db->fetchOne(
            "SELECT * FROM wordpress_profiles WHERE id = ?",
            [$profileId]
        );
    }
    
    /**
     * Get a profile by name
     */
    public function getProfileByName(string $profileName): ?array {
        return $this->db->fetchOne(
            "SELECT * FROM wordpress_profiles WHERE profile_name = ?",
            [$profileName]
        );
    }
    
    /**
     * Create a new WordPress profile
     */
    public function createProfile(array $profileData): array {
        try {
            // Validate required fields
            $required = ['profile_name', 'site_url', 'username', 'app_password'];
            foreach ($required as $field) {
                if (empty($profileData[$field])) {
                    return [
                        'success' => false,
                        'error' => "Field '$field' is required"
                    ];
                }
            }
            
            // Check if profile name already exists
            $existing = $this->getProfileByName($profileData['profile_name']);
            if ($existing) {
                return [
                    'success' => false,
                    'error' => 'Profile name already exists'
                ];
            }
            
            // Prepare data with defaults
            $data = [
                'profile_name' => trim($profileData['profile_name']),
                'site_url' => rtrim(trim($profileData['site_url']), '/'),
                'username' => trim($profileData['username']),
                'app_password' => trim($profileData['app_password']),
                'novel_post_type' => $profileData['novel_post_type'] ?? 'page',
                'chapter_post_type' => $profileData['chapter_post_type'] ?? 'post',
                'novel_custom_post_type' => trim($profileData['novel_custom_post_type'] ?? ''),
                'chapter_custom_post_type' => trim($profileData['chapter_custom_post_type'] ?? ''),
                'use_custom_post_types' => !empty($profileData['use_custom_post_types']),
                'default_category' => trim($profileData['default_category'] ?? ''),
                'auto_publish' => !isset($profileData['auto_publish']) || !empty($profileData['auto_publish']),
                'include_original_title' => !isset($profileData['include_original_title']) || !empty($profileData['include_original_title']),
                'is_active' => !isset($profileData['is_active']) || !empty($profileData['is_active'])
            ];
            
            $profileId = $this->db->insert('wordpress_profiles', $data);
            
            if ($profileId) {
                return [
                    'success' => true,
                    'profile_id' => $profileId,
                    'message' => 'Profile created successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to create profile'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Update an existing WordPress profile
     */
    public function updateProfile(int $profileId, array $profileData): array {
        try {
            // Check if profile exists
            $existing = $this->getProfile($profileId);
            if (!$existing) {
                return [
                    'success' => false,
                    'error' => 'Profile not found'
                ];
            }
            
            // Check if new profile name conflicts with another profile
            if (!empty($profileData['profile_name']) && $profileData['profile_name'] !== $existing['profile_name']) {
                $nameConflict = $this->getProfileByName($profileData['profile_name']);
                if ($nameConflict && $nameConflict['id'] != $profileId) {
                    return [
                        'success' => false,
                        'error' => 'Profile name already exists'
                    ];
                }
            }
            
            // Prepare update data
            $data = [];
            $allowedFields = [
                'profile_name', 'site_url', 'username', 'app_password',
                'novel_post_type', 'chapter_post_type', 'novel_custom_post_type',
                'chapter_custom_post_type', 'use_custom_post_types', 'default_category',
                'auto_publish', 'include_original_title', 'is_active'
            ];
            
            foreach ($allowedFields as $field) {
                if (array_key_exists($field, $profileData)) {
                    if (in_array($field, ['site_url', 'profile_name', 'username', 'app_password', 'novel_custom_post_type', 'chapter_custom_post_type', 'default_category'])) {
                        $data[$field] = $field === 'site_url' ? rtrim(trim($profileData[$field]), '/') : trim($profileData[$field]);
                    } elseif (in_array($field, ['use_custom_post_types', 'auto_publish', 'include_original_title', 'is_active'])) {
                        $data[$field] = !empty($profileData[$field]);
                    } else {
                        $data[$field] = $profileData[$field];
                    }
                }
            }
            
            if (empty($data)) {
                return [
                    'success' => false,
                    'error' => 'No valid fields to update'
                ];
            }
            
            $updated = $this->db->update(
                'wordpress_profiles',
                $data,
                'id = ?',
                [$profileId]
            );
            
            if ($updated) {
                return [
                    'success' => true,
                    'message' => 'Profile updated successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'No changes made or update failed'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Delete a WordPress profile
     */
    public function deleteProfile(int $profileId): array {
        try {
            // Check if profile exists
            $profile = $this->getProfile($profileId);
            if (!$profile) {
                return [
                    'success' => false,
                    'error' => 'Profile not found'
                ];
            }
            
            // Check if profile is being used by any posts
            $postsUsingProfile = $this->db->fetchOne(
                "SELECT COUNT(*) as count FROM wordpress_posts WHERE profile_id = ?",
                [$profileId]
            );
            
            if ($postsUsingProfile && $postsUsingProfile['count'] > 0) {
                return [
                    'success' => false,
                    'error' => 'Cannot delete profile: it is being used by ' . $postsUsingProfile['count'] . ' posts'
                ];
            }
            
            $deleted = $this->db->delete('wordpress_profiles', 'id = ?', [$profileId]);
            
            if ($deleted) {
                return [
                    'success' => true,
                    'message' => 'Profile deleted successfully'
                ];
            } else {
                return [
                    'success' => false,
                    'error' => 'Failed to delete profile'
                ];
            }
            
        } catch (Exception $e) {
            return [
                'success' => false,
                'error' => 'Database error: ' . $e->getMessage()
            ];
        }
    }
    
    /**
     * Test connection for a specific profile
     */
    public function testProfileConnection(int $profileId): array {
        $profile = $this->getProfile($profileId);
        if (!$profile) {
            return [
                'success' => false,
                'error' => 'Profile not found'
            ];
        }
        
        // Create temporary WordPress service with this profile's settings
        $tempService = new WordPressService();
        return $tempService->testConnectionWithProfile($profile);
    }
    
    /**
     * Get posting statistics for all profiles
     */
    public function getPostingStatistics(): array {
        return $this->db->fetchAll(
            "SELECT 
                p.id,
                p.profile_name,
                p.site_url,
                p.is_active,
                COUNT(CASE WHEN wp.post_type = 'novel' THEN 1 END) as novels_posted,
                COUNT(CASE WHEN wp.post_type = 'chapter' THEN 1 END) as chapters_posted,
                COUNT(wp.id) as total_posts
             FROM wordpress_profiles p
             LEFT JOIN wordpress_posts wp ON p.id = wp.profile_id
             GROUP BY p.id, p.profile_name, p.site_url, p.is_active
             ORDER BY p.profile_name ASC"
        );
    }
    
    /**
     * Get profiles that have posted a specific novel
     */
    public function getNovelPostingProfiles(int $novelId): array {
        return $this->db->fetchAll(
            "SELECT p.*, wp.wordpress_post_id, wp.wordpress_url, wp.post_status
             FROM wordpress_profiles p
             INNER JOIN wordpress_posts wp ON p.id = wp.profile_id
             WHERE wp.novel_id = ? AND wp.post_type = 'novel'
             ORDER BY p.profile_name ASC",
            [$novelId]
        );
    }
    
    /**
     * Get profiles that have posted a specific chapter
     */
    public function getChapterPostingProfiles(int $chapterId): array {
        return $this->db->fetchAll(
            "SELECT p.*, wp.wordpress_post_id, wp.wordpress_url, wp.post_status
             FROM wordpress_profiles p
             INNER JOIN wordpress_posts wp ON p.id = wp.profile_id
             WHERE wp.chapter_id = ? AND wp.post_type = 'chapter'
             ORDER BY p.profile_name ASC",
            [$chapterId]
        );
    }
}
