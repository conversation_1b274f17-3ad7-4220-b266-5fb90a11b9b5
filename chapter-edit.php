<?php
/**
 * Chapter Translation Editor Page
 * Dedicated page for editing chapter translations with TinyMCE rich text editor
 */

require_once 'config/config.php';
require_once 'includes/header.php';

// Get parameters from URL
$novelId = isset($_GET['novel_id']) ? (int)$_GET['novel_id'] : 0;
$chapterNumber = isset($_GET['chapter']) ? (int)$_GET['chapter'] : 0;

if (!$novelId || !$chapterNumber) {
    header('Location: index.php');
    exit;
}

// Additional CSS for the editor
$additionalCSS = ['assets/css/chapter-edit.css'];
renderHeader('Edit Chapter Translation', $additionalCSS);
?>

<?php 
function renderNavigation_called() {} // Prevent auto-render
include 'includes/navigation.php'; 
renderNavigation();
?>

<div class="container-fluid mt-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2 class="mb-1">
                        <i class="fas fa-edit text-primary me-2"></i>
                        Edit Chapter Translation
                    </h2>
                    <nav aria-label="breadcrumb">
                        <ol class="breadcrumb mb-0">
                            <li class="breadcrumb-item">
                                <a href="index.php">
                                    <i class="fas fa-home"></i> Dashboard
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="novel-details.php?id=<?= $novelId ?>">
                                    <i class="fas fa-book"></i> Novel Details
                                </a>
                            </li>
                            <li class="breadcrumb-item">
                                <a href="chapter-view.php?novel_id=<?= $novelId ?>&chapter=<?= $chapterNumber ?>">
                                    <i class="fas fa-file-alt"></i> Chapter <?= $chapterNumber ?>
                                </a>
                            </li>
                            <li class="breadcrumb-item active" aria-current="page">
                                <i class="fas fa-edit"></i> Edit Translation
                            </li>
                        </ol>
                    </nav>
                </div>
                <div class="btn-group">
                    <button type="button" class="btn btn-outline-secondary" onclick="window.history.back()">
                        <i class="fas fa-arrow-left me-1"></i>
                        Back
                    </button>
                    <a href="chapter-view.php?novel_id=<?= $novelId ?>&chapter=<?= $chapterNumber ?>" 
                       class="btn btn-outline-primary">
                        <i class="fas fa-eye me-1"></i>
                        View Chapter
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Loading State -->
    <div id="loading-state" class="text-center py-5">
        <div class="spinner-border text-primary" role="status">
            <span class="visually-hidden">Loading...</span>
        </div>
        <p class="mt-3 text-muted">Loading chapter data...</p>
    </div>

    <!-- Editor Container -->
    <div id="editor-container" style="display: none;">
        <!-- Chapter Info Card -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-8">
                                <h5 class="card-title mb-1" id="chapter-title">
                                    <i class="fas fa-file-alt text-muted me-2"></i>
                                    Loading...
                                </h5>
                                <p class="card-text text-muted mb-0" id="novel-title">
                                    <i class="fas fa-book text-muted me-2"></i>
                                    Loading...
                                </p>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <div class="editor-stats">
                                    <small class="text-muted d-block">
                                        <i class="fas fa-clock me-1"></i>
                                        Last saved: <span id="last-saved">Never</span>
                                    </small>
                                    <small class="text-muted d-block">
                                        <i class="fas fa-spell-check me-1"></i>
                                        Word count: <span id="word-count">0</span>
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Editor Toolbar -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="editor-toolbar">
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-success" id="save-btn" onclick="chapterEditor.saveChapter()">
                            <i class="fas fa-save me-1"></i>
                            Save Changes
                        </button>
                        <button type="button" class="btn btn-outline-success" id="auto-save-toggle" 
                                onclick="chapterEditor.toggleAutoSave()" title="Toggle auto-save">
                            <i class="fas fa-clock me-1"></i>
                            Auto-save: <span id="auto-save-status">ON</span>
                        </button>
                    </div>
                    
                    <div class="btn-group me-3" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="chapterEditor.showOriginal()" 
                                title="View original text">
                            <i class="fas fa-language me-1"></i>
                            Show Original
                        </button>
                        <button type="button" class="btn btn-outline-secondary" onclick="chapterEditor.toggleFullscreen()" 
                                title="Toggle fullscreen mode">
                            <i class="fas fa-expand me-1"></i>
                            Fullscreen
                        </button>
                    </div>

                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-outline-info" onclick="chapterEditor.showHelp()" 
                                title="Editor help">
                            <i class="fas fa-question-circle me-1"></i>
                            Help
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Main Editor Row -->
        <div class="row">
            <!-- Original Text Panel (Hidden by default) -->
            <div class="col-lg-6 d-none" id="original-panel">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-file-alt text-muted me-2"></i>
                            Original Text
                            <button type="button" class="btn btn-sm btn-outline-secondary float-end" 
                                    onclick="chapterEditor.hideOriginal()">
                                <i class="fas fa-times"></i>
                            </button>
                        </h6>
                    </div>
                    <div class="card-body">
                        <div id="original-content" class="original-text-content">
                            Loading original text...
                        </div>
                    </div>
                </div>
            </div>

            <!-- Editor Panel -->
            <div class="col-lg-12" id="editor-panel">
                <div class="card h-100">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-edit text-primary me-2"></i>
                            Translation Editor
                        </h6>
                    </div>
                    <div class="card-body p-0">
                        <!-- TinyMCE Editor will be initialized here -->
                        <textarea id="translation-editor" name="translation-editor">
                            Loading translation content...
                        </textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Error State -->
    <div id="error-state" class="text-center py-5" style="display: none;">
        <div class="text-danger">
            <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
            <h5>Error Loading Chapter</h5>
            <p id="error-message">An error occurred while loading the chapter.</p>
            <div class="mt-3">
                <button class="btn btn-primary me-2" onclick="chapterEditor.loadChapter()">
                    <i class="fas fa-sync-alt me-1"></i>
                    Retry
                </button>
                <a href="chapter-view.php?novel_id=<?= $novelId ?>&chapter=<?= $chapterNumber ?>" 
                   class="btn btn-outline-secondary">
                    <i class="fas fa-arrow-left me-1"></i>
                    Back to Chapter
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Original Text Modal -->
<div class="modal fade" id="originalTextModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-file-alt me-2"></i>
                    Original Text Reference
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="modal-original-content" class="original-text-content">
                    Loading...
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Help Modal -->
<div class="modal fade" id="helpModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="fas fa-question-circle me-2"></i>
                    Editor Help
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-6">
                        <h6><i class="fas fa-keyboard me-2"></i>Keyboard Shortcuts</h6>
                        <ul class="list-unstyled">
                            <li><kbd>Ctrl+S</kbd> - Save changes</li>
                            <li><kbd>Ctrl+Z</kbd> - Undo</li>
                            <li><kbd>Ctrl+Y</kbd> - Redo</li>
                            <li><kbd>F11</kbd> - Toggle fullscreen</li>
                            <li><kbd>Ctrl+O</kbd> - Show original text</li>
                        </ul>
                    </div>
                    <div class="col-md-6">
                        <h6><i class="fas fa-lightbulb me-2"></i>Tips</h6>
                        <ul class="list-unstyled">
                            <li>• Auto-save runs every 30 seconds</li>
                            <li>• Use the original text panel for reference</li>
                            <li>• Preserve paragraph structure when possible</li>
                            <li>• Keep character names consistent</li>
                            <li>• Maintain the story's tone and style</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">Got it!</button>
            </div>
        </div>
    </div>
</div>

<!-- TinyMCE Script -->
<script src="https://cdn.tiny.cloud/1/l0oi6sreor4yotc9b3y7r042f2aqgt5o769v70qizl5azg7x/tinymce/7/tinymce.min.js" referrerpolicy="origin"></script>

<?php renderFooter(['assets/js/chapter-edit.js']); ?>

<script>
// Pass parameters to JavaScript
window.novelId = <?= $novelId ?>;
window.chapterNumber = <?= $chapterNumber ?>;
</script>
