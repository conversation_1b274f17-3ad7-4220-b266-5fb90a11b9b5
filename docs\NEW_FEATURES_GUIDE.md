# New Translation Features Guide

This guide explains the new re-translate and delete translation features added to the novel translation application.

## Overview

Two major new features have been added:

1. **Re-translate Feature**: Clear existing translations and re-run the Google Gemini AI translation process
2. **Delete Translation Results Feature**: Remove translated content while preserving original text

Both features support individual chapter operations and bulk operations for multiple chapters.

## Features

### 1. Re-translate Feature

**Purpose**: Re-translate chapters that have already been translated, useful for:
- Improving translation quality with updated prompts
- Re-translating after updating the name dictionary
- Fixing translation errors

**How it works**:
- Clears existing translation data (translated_content, translated_title)
- Resets translation_status to 'pending'
- Re-runs the complete translation process using Google Gemini AI
- Preserves original text and furigana data

### 2. Delete Translation Results Feature

**Purpose**: Remove translation data while keeping original content, useful for:
- Freeing up database space
- Starting fresh with different translation approaches
- Removing poor quality translations

**How it works**:
- Removes translated_content and translated_title
- Resets translation_status to 'pending'
- Preserves all original Japanese text and furigana
- Maintains chapter structure and metadata

## User Interface

### Bulk Operations Mode

1. **Activate Bulk Mode**: Click "Bulk Select" button in the chapter list header
2. **Select Chapters**: Use checkboxes to select desired chapters
3. **Bulk Actions**: Use the bulk action buttons that appear:
   - **Translate Selected**: Translate multiple untranslated chapters
   - **Re-translate**: Clear and re-translate selected translated chapters
   - **Clear Translations**: Remove translations from selected chapters

### Individual Chapter Operations

For each chapter, new action buttons are available:
- **Re-translate**: Available for completed chapters (orange button)
- **Clear**: Available for completed chapters (red button)

### Selection Controls

- **Select All**: Select all chapters at once
- **Clear**: Clear all selections
- **Exit Bulk Mode**: Return to normal view

## API Endpoints

### Bulk Translation
```http
PUT /api/chapters.php
Content-Type: application/json

{
    "novel_id": 1,
    "chapter_numbers": [1, 2, 3],
    "target_language": "en"
}
```

### Bulk Re-translation
```http
PUT /api/chapters.php
Content-Type: application/json

{
    "novel_id": 1,
    "chapter_numbers": [1, 2, 3],
    "target_language": "en",
    "action": "retranslate"
}
```

### Clear Translations
```http
DELETE /api/chapters.php
Content-Type: application/json

{
    "novel_id": 1,
    "chapter_numbers": [1, 2, 3]
}
```

### Single Chapter Operations

Replace `chapter_numbers` array with `chapter_number` integer for single chapter operations.

## Database Changes

The features use existing database schema but modify these fields in the `chapters` table:
- `translated_content`: Set to NULL when clearing
- `translated_title`: Set to NULL when clearing  
- `translation_status`: Reset to 'pending'
- `translation_date`: Set to NULL when clearing
- `word_count`: Reset to 0 when clearing

## Safety Features

### Confirmation Dialogs
- Re-translate operations show confirmation with warning about clearing existing translations
- Delete operations show confirmation with "cannot be undone" warning
- Bulk operations show count of affected chapters

### Data Preservation
- Original Japanese text is never modified
- Furigana data is preserved
- Chapter structure and metadata remain intact
- Name dictionary entries are preserved

### Error Handling
- Individual chapter failures in bulk operations don't affect other chapters
- Detailed error reporting for each chapter
- Transaction rollback for critical failures
- Graceful degradation for network issues

## Usage Tips

1. **Before Re-translating**: Update name dictionary entries for better consistency
2. **Bulk Operations**: Use for efficiency when processing many chapters
3. **Individual Operations**: Use for fine-tuning specific chapters
4. **Backup**: Consider backing up translations before bulk clear operations
5. **Network**: Ensure stable internet connection for bulk translation operations

## Compatibility

- **PHP Version**: Requires PHP 8.2+
- **Database**: Compatible with existing MySQL schema
- **Browser**: Modern browsers with JavaScript enabled
- **API**: Maintains backward compatibility with existing endpoints

## Troubleshooting

### Common Issues

1. **"No chapters selected"**: Activate bulk mode and select chapters first
2. **"Chapter not found"**: Refresh the page to reload chapter data
3. **Translation failures**: Check Google Gemini API quota and network connection
4. **Permission errors**: Ensure proper database write permissions

### Error Messages

- **Success**: Green toast notifications with operation summary
- **Warnings**: Yellow toast for non-critical issues
- **Errors**: Red toast with specific error details
- **Network**: Specific messages for connection issues

## Performance Considerations

- Bulk operations process chapters sequentially to avoid API rate limits
- Large bulk operations may take several minutes to complete
- Progress is shown via loading indicators
- Failed chapters are reported individually without stopping the process
