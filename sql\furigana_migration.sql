-- Furigana Support Migration
-- Add furigana-related columns and tables to support ruby text

-- Add furigana storage columns to chapters table
ALTER TABLE chapters 
ADD COLUMN original_content_with_furigana LONGTEXT AFTER original_content,
ADD COLUMN furigana_processing_status ENUM('none', 'detected', 'processed', 'error') DEFAULT 'none' AFTER translation_status,
ADD COLUMN furigana_count INT DEFAULT 0 AFTER word_count;

-- Create furigana dictionary table for consistent furigana handling
CREATE TABLE furigana_dictionary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT NOT NULL,
    kanji_text VARCHAR(200) NOT NULL,
    furigana_text VARCHAR(200) NOT NULL,
    frequency INT DEFAULT 1,
    first_appearance_chapter INT,
    is_verified BOOLEAN DEFAULT FALSE,
    processing_preference ENUM('preserve', 'translate', 'remove', 'romanize') DEFAULT 'preserve',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_furigana_per_novel (novel_id, kanji_text, furigana_text),
    INDEX idx_novel_furigana (novel_id),
    INDEX idx_kanji_text (kanji_text),
    INDEX idx_frequency (frequency DESC)
);

-- Add furigana preferences to user_preferences
INSERT INTO user_preferences (preference_key, preference_value) VALUES
('furigana_display_mode', 'ruby'),  -- 'ruby', 'parentheses', 'hidden', 'separate'
('furigana_translation_mode', 'preserve'),  -- 'preserve', 'translate', 'remove', 'romanize'
('furigana_ai_processing', 'true'),  -- whether to use AI for furigana processing
('furigana_auto_detect', 'true');  -- whether to auto-detect furigana during crawling

-- Add index for furigana processing status
ALTER TABLE chapters ADD INDEX idx_furigana_status (furigana_processing_status);

-- Update translation_logs to track furigana processing
ALTER TABLE translation_logs 
ADD COLUMN furigana_processing_time DECIMAL(5,2) AFTER translation_time_seconds,
ADD COLUMN furigana_count INT DEFAULT 0 AFTER api_tokens_used;
