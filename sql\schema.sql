-- Novel Translation Application Database Schema
-- PHP 8.2 + MySQL

CREATE DATABASE IF NOT EXISTS novel_translator CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE novel_translator;

-- Novels table - stores novel metadata
CREATE TABLE novels (
    id INT AUTO_INCREMENT PRIMARY KEY,
    url VARCHAR(500) NOT NULL UNIQUE,
    platform ENUM('kakuyomu', 'syosetu', 'shuba69', 'manual') NOT NULL,
    original_title VARCHAR(500) NOT NULL,
    translated_title VARCHAR(500),
    author VA<PERSON>HA<PERSON>(200),
    publication_date DATE,
    total_chapters INT DEFAULT 0,
    original_synopsis TEXT,
    translated_synopsis TEXT,
    status ENUM('active', 'completed', 'dropped') DEFAULT 'active',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_platform (platform),
    INDEX idx_status (status),
    INDEX idx_created (created_at)
);

-- Chapters table - stores chapter content
CREATE TABLE chapters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT NOT NULL,
    chapter_number INT NOT NULL,
    chapter_url VARCHAR(500),
    original_title VARCHAR(500),
    translated_title VARCHAR(500),
    original_content LONGTEXT,
    translated_content LONGTEXT,
    word_count INT DEFAULT 0,
    translation_status ENUM('pending', 'translating', 'completed', 'error') DEFAULT 'pending',
    translation_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_chapter (novel_id, chapter_number),
    INDEX idx_novel_chapter (novel_id, chapter_number),
    INDEX idx_translation_status (translation_status)
);

-- Name dictionary - stores name mappings for consistency
CREATE TABLE name_dictionary (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT NOT NULL,
    original_name VARCHAR(200) NOT NULL,
    romanization VARCHAR(200),
    translation VARCHAR(200),
    name_type ENUM('character', 'location', 'organization', 'country', 'skill', 'monster', 'other') DEFAULT 'other',
    frequency INT DEFAULT 1,
    first_appearance_chapter INT,
    is_verified BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    UNIQUE KEY unique_name_per_novel (novel_id, original_name),
    INDEX idx_novel_names (novel_id),
    INDEX idx_name_type (name_type),
    INDEX idx_frequency (frequency DESC)
);

-- Translation logs - track translation history and API usage
CREATE TABLE translation_logs (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT,
    chapter_id INT,
    translation_type ENUM('synopsis', 'chapter', 'title') NOT NULL,
    original_text_length INT,
    translated_text_length INT,
    api_tokens_used INT,
    translation_time_seconds DECIMAL(5,2),
    status ENUM('success', 'error', 'partial') DEFAULT 'success',
    error_message TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE SET NULL,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE SET NULL,
    INDEX idx_novel_logs (novel_id),
    INDEX idx_chapter_logs (chapter_id),
    INDEX idx_translation_date (created_at)
);

-- User preferences (for future expansion)
CREATE TABLE user_preferences (
    id INT AUTO_INCREMENT PRIMARY KEY,
    preference_key VARCHAR(100) NOT NULL UNIQUE,
    preference_value TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Chapter chunks table - stores split chapter parts for large chapters
CREATE TABLE chapter_chunks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    chapter_id INT NOT NULL,
    chunk_number INT NOT NULL,
    original_content TEXT NOT NULL,
    translated_content TEXT,
    character_count INT NOT NULL,
    word_count INT NOT NULL,
    chunk_type ENUM('paragraph', 'scene_break', 'dialogue', 'narrative') DEFAULT 'paragraph',
    translation_status ENUM('pending', 'translating', 'completed', 'error') DEFAULT 'pending',
    translation_date TIMESTAMP NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
    UNIQUE KEY unique_chunk (chapter_id, chunk_number),
    INDEX idx_chapter_chunks (chapter_id, chunk_number),
    INDEX idx_chunk_status (translation_status)
);

-- WordPress integration table - tracks posting status
CREATE TABLE wordpress_posts (
    id INT AUTO_INCREMENT PRIMARY KEY,
    novel_id INT,
    chapter_id INT,
    wordpress_post_id BIGINT NOT NULL,
    post_type ENUM('novel', 'chapter') NOT NULL,
    wordpress_url VARCHAR(500),
    wordpress_domain VARCHAR(255) NOT NULL,
    post_status ENUM('draft', 'published', 'private') DEFAULT 'published',
    last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (novel_id) REFERENCES novels(id) ON DELETE CASCADE,
    FOREIGN KEY (chapter_id) REFERENCES chapters(id) ON DELETE CASCADE,
    UNIQUE KEY unique_novel_post_domain (novel_id, post_type, wordpress_domain),
    UNIQUE KEY unique_chapter_post_domain (chapter_id, wordpress_domain),
    INDEX idx_wordpress_post_id (wordpress_post_id),
    INDEX idx_post_type (post_type),
    INDEX idx_wordpress_domain (wordpress_domain)
);

-- Insert default preferences
INSERT INTO user_preferences (preference_key, preference_value) VALUES
('default_target_language', 'en'),
('translation_model', 'gemini-1.5-flash'),
('max_chapters_per_batch', '5'),
('auto_detect_names', 'true'),
('translation_quality', 'balanced'),
('chunk_size_limit', '25000'),
('chunk_overlap_size', '500'),
('enable_smart_chunking', 'true'),
('wordpress_site_url', ''),
('wordpress_username', ''),
('wordpress_app_password', ''),
('wordpress_novel_post_type', 'page'),
('wordpress_chapter_post_type', 'post'),
('wordpress_novel_custom_post_type', ''),
('wordpress_chapter_custom_post_type', ''),
('wordpress_use_custom_post_types', 'false'),
('wordpress_default_category', ''),
('wordpress_auto_publish', 'true'),
('wordpress_include_original_title', 'true');
