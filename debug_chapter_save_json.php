<?php
/**
 * Debug script for chapter save JSON issues
 * This script helps identify the exact cause of JSON parsing errors
 */

// Enable error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);
ini_set('log_errors', 1);

// Start output buffering to catch any unexpected output
ob_start();

require_once 'config/config.php';

echo "=== Chapter Save JSON Debug ===\n";

try {
    $novelManager = new NovelManager();
    $db = Database::getInstance();

    // Find a test chapter that doesn't have content yet
    $testChapter = $db->fetchOne(
        "SELECT c.*, n.platform, n.original_title
         FROM chapters c
         JOIN novels n ON c.novel_id = n.id
         WHERE c.original_content IS NULL OR c.original_content = ''
         LIMIT 1"
    );

    if (!$testChapter) {
        echo "No test chapters found. Creating a test scenario...\n";

        // Find any chapter with content to test JSON encoding
        $testChapter = $db->fetchOne(
            "SELECT c.*, n.platform, n.original_title
             FROM chapters c
             JOIN novels n ON c.novel_id = n.id
             WHERE c.original_content IS NOT NULL AND c.original_content != ''
             LIMIT 1"
        );

        if (!$testChapter) {
            echo "No chapters found in database.\n";
            exit;
        }

        echo "Using existing chapter for JSON encoding test...\n";
    }
    
    echo "Test Chapter: Novel ID {$testChapter['novel_id']}, Chapter {$testChapter['chapter_number']}\n";
    echo "Platform: {$testChapter['platform']}\n";
    echo "Novel: {$testChapter['original_title']}\n\n";
    
    // Test 1: Simulate the exact API call
    echo "=== Test 1: Simulating API Call ===\n";
    
    $testData = [
        'novel_id' => $testChapter['novel_id'],
        'chapter_number' => $testChapter['chapter_number']
    ];
    
    echo "Request data: " . json_encode($testData) . "\n";
    
    // Test 2: Call saveChapter method directly
    echo "\n=== Test 2: Direct saveChapter Call ===\n";
    
    try {
        $result = $novelManager->saveChapter($testChapter['novel_id'], $testChapter['chapter_number']);
        
        echo "Save result success: " . ($result['success'] ? 'YES' : 'NO') . "\n";
        
        if (!$result['success']) {
            echo "Error: " . $result['error'] . "\n";
        } else {
            echo "Message: " . $result['message'] . "\n";
            
            // Test 3: JSON encoding of the result
            echo "\n=== Test 3: JSON Encoding Test ===\n";
            
            $jsonTest = json_encode($result);
            if ($jsonTest === false) {
                echo "❌ JSON encoding FAILED: " . json_last_error_msg() . "\n";
                
                // Try to identify problematic data
                echo "Analyzing result data...\n";
                
                foreach ($result as $key => $value) {
                    $keyJson = json_encode([$key => $value]);
                    if ($keyJson === false) {
                        echo "❌ Problem with key '$key': " . json_last_error_msg() . "\n";
                        
                        if (is_string($value)) {
                            echo "   String length: " . strlen($value) . "\n";
                            echo "   UTF-8 valid: " . (mb_check_encoding($value, 'UTF-8') ? 'YES' : 'NO') . "\n";
                            echo "   Contains null bytes: " . (strpos($value, "\0") !== false ? 'YES' : 'NO') . "\n";
                            echo "   Preview: " . substr($value, 0, 100) . "...\n";
                        } elseif (is_array($value)) {
                            echo "   Array with " . count($value) . " elements\n";
                            
                            // Test each array element
                            foreach ($value as $subKey => $subValue) {
                                $subJson = json_encode([$subKey => $subValue]);
                                if ($subJson === false) {
                                    echo "   ❌ Problem with sub-key '$subKey': " . json_last_error_msg() . "\n";
                                }
                            }
                        }
                    } else {
                        echo "✅ Key '$key' encodes OK\n";
                    }
                }
                
                // Test with cleaned data
                echo "\n=== Test 4: Cleaning Data ===\n";
                
                function cleanDataForJson($data) {
                    if (is_array($data)) {
                        return array_map('cleanDataForJson', $data);
                    } elseif (is_string($data)) {
                        // Remove null bytes and fix encoding
                        $cleaned = str_replace("\0", '', $data);
                        if (!mb_check_encoding($cleaned, 'UTF-8')) {
                            $cleaned = mb_convert_encoding($cleaned, 'UTF-8', 'auto');
                        }
                        // Remove any remaining problematic characters
                        $cleaned = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $cleaned);
                        return $cleaned;
                    }
                    return $data;
                }
                
                $cleanedResult = cleanDataForJson($result);
                $cleanedJson = json_encode($cleanedResult);
                
                if ($cleanedJson === false) {
                    echo "❌ Even cleaned data fails JSON encoding: " . json_last_error_msg() . "\n";
                } else {
                    echo "✅ Cleaned data encodes successfully\n";
                    echo "Cleaned JSON length: " . strlen($cleanedJson) . " characters\n";
                }
                
            } else {
                echo "✅ JSON encoding successful\n";
                echo "JSON length: " . strlen($jsonTest) . " characters\n";
                echo "JSON preview: " . substr($jsonTest, 0, 200) . "...\n";
            }
        }
        
    } catch (Exception $e) {
        echo "❌ Exception during saveChapter: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
    
    // Test 5: Check for output buffer issues
    echo "\n=== Test 5: Output Buffer Check ===\n";
    
    $bufferContents = ob_get_contents();
    if (strlen($bufferContents) > 0) {
        echo "Buffer contains " . strlen($bufferContents) . " characters\n";
        echo "Buffer preview: " . substr($bufferContents, 0, 100) . "...\n";
    } else {
        echo "Buffer is clean\n";
    }
    
} catch (Exception $e) {
    echo "❌ Fatal error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
} catch (Error $e) {
    echo "❌ Fatal PHP error: " . $e->getMessage() . "\n";
    echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
}

echo "\n=== Debug Complete ===\n";

// Get any unexpected output
$output = ob_get_clean();
echo $output;
?>
