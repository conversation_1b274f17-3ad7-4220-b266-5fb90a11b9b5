TranslationService: getNameDictionary query returned 80 entries for novel 6
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng
DeepSeekTranslationService: Name mapping - 公主 → Princess
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia
DeepSeekTranslationService: Name mapping - 林游 → Lin You
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier
DeepSeekTranslationService: Name mapping - 白房子 → White House
DeepSeekTranslationService: Name mapping - 沙漠 → Desert
DeepSeekTranslationService: Name mapping - 地下 → Underground
DeepSeekTranslationService: Name mapping - 神庙 → Temple
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl
DeepSeekTranslationService: Name mapping - 主人 → Master
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League")
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch
DeepSeekTranslationService: Name mapping - 头环 → Head ring
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor
DeepSeekTranslationService: Name mapping - 机甲 → Mecha
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University
DeepSeekTranslationService: Name mapping - 博士 → Doctor
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: getNameDictionary query returned 80 entries, filtered to 80 valid entries for novel 6
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103 (using translation)
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng (using translation)
DeepSeekTranslationService: Name mapping - 公主 → Princess (using translation)
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian (using translation)
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei (using translation)
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 林游 → Lin You (using translation)
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia (using translation)
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world (using translation)
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier (using translation)
DeepSeekTranslationService: Name mapping - 白房子 → White House (using translation)
DeepSeekTranslationService: Name mapping - 沙漠 → Desert (using translation)
DeepSeekTranslationService: Name mapping - 地下 → Underground (using translation)
DeepSeekTranslationService: Name mapping - 神庙 → Temple (using translation)
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain (using translation)
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune (using translation)
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon (using translation)
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University (using translation)
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi (using translation)
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia (using translation)
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique (using translation)
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time (using translation)
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance (using translation)
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon (using translation)
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon (using translation)
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans (using translation)
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon (using translation)
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award (using translation)
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion (using translation)
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat (using translation)
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang (using translation)
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3 (using translation)
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber (using translation)
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl (using translation)
DeepSeekTranslationService: Name mapping - 主人 → Master (using translation)
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death (using translation)
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era (using translation)
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment) (using translation)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League") (using translation)
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted (using translation)
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey (using translation)
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality (using translation)
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer (using translation)
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game (using translation)
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game (using translation)
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology (using translation)
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal (using translation)
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set (using translation)
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing (using translation)
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence (using translation)
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code (using translation)
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family (using translation)
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device (using translation)
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet (using translation)
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment (using translation)
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment (using translation)
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device (using translation)
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device (using translation)
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net (using translation)
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device (using translation)
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen (using translation)
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch (using translation)
DeepSeekTranslationService: Name mapping - 头环 → Head ring (using translation)
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160 (using romanization)
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor (using translation)
DeepSeekTranslationService: Name mapping - 机甲 → Mecha (using translation)
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor (using translation)
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device (using translation)
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 博士 → Doctor (using translation)
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls (using translation)
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist (using translation)
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: getNameDictionary query returned 80 entries, filtered to 80 valid entries for novel 6
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103 (using translation)
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng (using translation)
DeepSeekTranslationService: Name mapping - 公主 → Princess (using translation)
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian (using translation)
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei (using translation)
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 林游 → Lin You (using translation)
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia (using translation)
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world (using translation)
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier (using translation)
DeepSeekTranslationService: Name mapping - 白房子 → White House (using translation)
DeepSeekTranslationService: Name mapping - 沙漠 → Desert (using translation)
DeepSeekTranslationService: Name mapping - 地下 → Underground (using translation)
DeepSeekTranslationService: Name mapping - 神庙 → Temple (using translation)
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain (using translation)
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune (using translation)
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon (using translation)
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University (using translation)
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi (using translation)
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia (using translation)
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique (using translation)
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time (using translation)
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance (using translation)
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon (using translation)
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon (using translation)
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans (using translation)
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon (using translation)
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award (using translation)
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion (using translation)
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat (using translation)
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang (using translation)
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3 (using translation)
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber (using translation)
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl (using translation)
DeepSeekTranslationService: Name mapping - 主人 → Master (using translation)
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death (using translation)
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era (using translation)
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment) (using translation)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League") (using translation)
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted (using translation)
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey (using translation)
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality (using translation)
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer (using translation)
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game (using translation)
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game (using translation)
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology (using translation)
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal (using translation)
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set (using translation)
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing (using translation)
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence (using translation)
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code (using translation)
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family (using translation)
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device (using translation)
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet (using translation)
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment (using translation)
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment (using translation)
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device (using translation)
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device (using translation)
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net (using translation)
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device (using translation)
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen (using translation)
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch (using translation)
DeepSeekTranslationService: Name mapping - 头环 → Head ring (using translation)
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160 (using romanization)
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor (using translation)
DeepSeekTranslationService: Name mapping - 机甲 → Mecha (using translation)
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor (using translation)
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device (using translation)
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 博士 → Doctor (using translation)
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls (using translation)
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist (using translation)
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: getNameDictionary query returned 80 entries, filtered to 80 valid entries for novel 6
TranslationService: Starting chapter translation - Novel ID: 6, Chapter ID: 480
TranslationService: Chapter has no chunks, using regular translation
TranslationService: Chapter found - Title: 第3章 公主
TranslationService: Chapter content length: 6664
TranslationService: getNameDictionary query returned 80 entries, filtered to 80 valid entries for novel 6
TranslationService: Name dictionary entries: 80
TranslationService: Starting title translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103 (using translation)
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng (using translation)
DeepSeekTranslationService: Name mapping - 公主 → Princess (using translation)
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian (using translation)
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei (using translation)
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 林游 → Lin You (using translation)
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia (using translation)
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world (using translation)
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier (using translation)
DeepSeekTranslationService: Name mapping - 白房子 → White House (using translation)
DeepSeekTranslationService: Name mapping - 沙漠 → Desert (using translation)
DeepSeekTranslationService: Name mapping - 地下 → Underground (using translation)
DeepSeekTranslationService: Name mapping - 神庙 → Temple (using translation)
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain (using translation)
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune (using translation)
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon (using translation)
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University (using translation)
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi (using translation)
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia (using translation)
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique (using translation)
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time (using translation)
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance (using translation)
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon (using translation)
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon (using translation)
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans (using translation)
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon (using translation)
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award (using translation)
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion (using translation)
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat (using translation)
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang (using translation)
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3 (using translation)
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber (using translation)
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl (using translation)
DeepSeekTranslationService: Name mapping - 主人 → Master (using translation)
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death (using translation)
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era (using translation)
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment) (using translation)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League") (using translation)
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted (using translation)
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey (using translation)
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality (using translation)
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer (using translation)
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game (using translation)
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game (using translation)
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology (using translation)
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal (using translation)
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set (using translation)
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing (using translation)
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence (using translation)
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code (using translation)
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family (using translation)
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device (using translation)
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet (using translation)
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment (using translation)
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment (using translation)
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device (using translation)
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device (using translation)
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net (using translation)
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device (using translation)
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen (using translation)
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch (using translation)
DeepSeekTranslationService: Name mapping - 头环 → Head ring (using translation)
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160 (using romanization)
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor (using translation)
DeepSeekTranslationService: Name mapping - 机甲 → Mecha (using translation)
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor (using translation)
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device (using translation)
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 博士 → Doctor (using translation)
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls (using translation)
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist (using translation)
DeepSeekTranslationService: Attempt 1 of 3
DeepSeekTranslationService: Original title response: Chapter 3: Princess
DeepSeekTranslationService: Cleaned title result: Chapter 3: Princess
TranslationService: DeepSeek translation successful
TranslationService: Original title response: Chapter 3: Princess
TranslationService: Cleaned title result: Chapter 3: Princess
TranslationService: Title result: {"success":true,"error":"none"}
TranslationService: Starting content translation
TranslationService: Using DeepSeek API for translation
DeepSeekTranslationService: Using name dictionary with 80 entries
DeepSeekTranslationService: Name mapping - 赵立 → Zhao Lì (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏 → Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 幻梦103 → Huanmeng 103 (using translation)
DeepSeekTranslationService: Name mapping - 小梦 → Xiaomeng (using translation)
DeepSeekTranslationService: Name mapping - 公主 → Princess (using translation)
DeepSeekTranslationService: Name mapping - 马连 → Ma Lian (using translation)
DeepSeekTranslationService: Name mapping - 刘备 → Liu Bei (using translation)
DeepSeekTranslationService: Name mapping - 诸葛亮 → Zhuge Liang (using translation)
DeepSeekTranslationService: Name mapping - 朱辞夏公主 → Princess Zhu Cixia (using translation)
DeepSeekTranslationService: Name mapping - 林游 → Lin You (using translation)
DeepSeekTranslationService: Name mapping - 辞夏公主 → Princess Cixia (using translation)
DeepSeekTranslationService: Name mapping - 虚拟世界 → Virtual world (using translation)
DeepSeekTranslationService: Name mapping - 空天母舰 → Sky aircraft carrier (using translation)
DeepSeekTranslationService: Name mapping - 白房子 → White House (using translation)
DeepSeekTranslationService: Name mapping - 沙漠 → Desert (using translation)
DeepSeekTranslationService: Name mapping - 地下 → Underground (using translation)
DeepSeekTranslationService: Name mapping - 神庙 → Temple (using translation)
DeepSeekTranslationService: Name mapping - 雪山 → Snow Mountain (using translation)
DeepSeekTranslationService: Name mapping - 圣山之巅 → Peak of the Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 沙丘 → Sand dune (using translation)
DeepSeekTranslationService: Name mapping - 圣山 → Holy Mountain (using translation)
DeepSeekTranslationService: Name mapping - 网龙 → NetDragon (using translation)
DeepSeekTranslationService: Name mapping - 东华大学 → Donghua University (using translation)
DeepSeekTranslationService: Name mapping - 灵犀 → Lingxi (using translation)
DeepSeekTranslationService: Name mapping - 网龙公司 → NetDragon Corporation (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学 → National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 大夏 → Great Xia (using translation)
DeepSeekTranslationService: Name mapping - 甩枪术 → Curved Bullet Technique (using translation)
DeepSeekTranslationService: Name mapping - 子弹时间 → Bullet time (using translation)
DeepSeekTranslationService: Name mapping - 共鸣 → Resonance (using translation)
DeepSeekTranslationService: Name mapping - 巨龙 → Giant Dragon (using translation)
DeepSeekTranslationService: Name mapping - 机械冥龙 → Mechanical Underworld Dragon (using translation)
DeepSeekTranslationService: Name mapping - 人类大脑可接受指令集 → Brain-Compatible Instruction Set for Humans (using translation)
DeepSeekTranslationService: Name mapping - 意大利炮 → Italian Cannon (using translation)
DeepSeekTranslationService: Name mapping - 图灵奖 → Turing Award (using translation)
DeepSeekTranslationService: Name mapping - 可控核聚变 → Controlled nuclear fusion (using translation)
DeepSeekTranslationService: Name mapping - 班级群 → Class group chat (using translation)
DeepSeekTranslationService: Name mapping - 杨教授 → Professor Yang (using translation)
DeepSeekTranslationService: Name mapping - 烈焰3 → Blazing Flame 3 (using translation)
DeepSeekTranslationService: Name mapping - 图-160战略轰炸机 → Tupolev Tu-160 strategic bomber (using translation)
DeepSeekTranslationService: Name mapping - 粉色头发卡通少女 → Pink-haired cartoon girl (using translation)
DeepSeekTranslationService: Name mapping - 主人 → Master (using translation)
DeepSeekTranslationService: Name mapping - 林肯——死大头 → Lincoln—Big Head of Death (using translation)
DeepSeekTranslationService: Name mapping - 雅达利时代 → Atari era (using translation)
DeepSeekTranslationService: Name mapping - Evo moment37 → Evo moment37 (a specific competitive gaming moment) (using translation)
DeepSeekTranslationService: Name mapping - 刺客联盟 → Wanted (movie title, lit. "Assassin's League") (using translation)
DeepSeekTranslationService: Name mapping - 通缉令 → Wanted (using translation)
DeepSeekTranslationService: Name mapping - 风之旅人 → Journey (using translation)
DeepSeekTranslationService: Name mapping - 虚拟现实 → Virtual Reality (using translation)
DeepSeekTranslationService: Name mapping - 意识转移 → Consciousness Transfer (using translation)
DeepSeekTranslationService: Name mapping - 虚拟游戏 → Virtual Game (using translation)
DeepSeekTranslationService: Name mapping - VR游戏 → VR Game (using translation)
DeepSeekTranslationService: Name mapping - 划时代的人机交互技术 → Epoch-making Human-Computer Interaction Technology (using translation)
DeepSeekTranslationService: Name mapping - 神经信号 → Neural Signal (using translation)
DeepSeekTranslationService: Name mapping - 大脑可接受指令集 → Brain-Compatible Instruction Set (using translation)
DeepSeekTranslationService: Name mapping - 分布式计算 → Distributed Computing (using translation)
DeepSeekTranslationService: Name mapping - 人工智能 → Artificial Intelligence (using translation)
DeepSeekTranslationService: Name mapping - 核心代码 → Core Code (using translation)
DeepSeekTranslationService: Name mapping - 皇室 → Royal Family (using translation)
DeepSeekTranslationService: Name mapping - 脑波神经交互设备 → Brainwave neural interaction device (using translation)
DeepSeekTranslationService: Name mapping - 神经信号采集头盔 → Neural signal acquisition helmet (using translation)
DeepSeekTranslationService: Name mapping - 分析设备 → Analysis equipment (using translation)
DeepSeekTranslationService: Name mapping - 输出设备 → Output equipment (using translation)
DeepSeekTranslationService: Name mapping - 收集装置 → Collection device (using translation)
DeepSeekTranslationService: Name mapping - 信号放大装置 → Signal amplification device (using translation)
DeepSeekTranslationService: Name mapping - 信号防逸散网 → Signal anti-dispersion net (using translation)
DeepSeekTranslationService: Name mapping - 神经信号滤波装置 → Neural signal filtering device (using translation)
DeepSeekTranslationService: Name mapping - 弧面显示屏 → Curved display screen (using translation)
DeepSeekTranslationService: Name mapping - 信号收集贴片 → Signal collection patch (using translation)
DeepSeekTranslationService: Name mapping - 头环 → Head ring (using translation)
DeepSeekTranslationService: Name mapping - 图-160 → Tu-160 (using romanization)
DeepSeekTranslationService: Name mapping - 核能动力炉 → Nuclear power reactor (using translation)
DeepSeekTranslationService: Name mapping - 机甲 → Mecha (using translation)
DeepSeekTranslationService: Name mapping - 骑士铠甲 → Knight armor (using translation)
DeepSeekTranslationService: Name mapping - 神经接入设备 → Neural Access Device (using translation)
DeepSeekTranslationService: Name mapping - 神经调制解调器 → Neural Modem (using translation)
DeepSeekTranslationService: Name mapping - 国家一流大学优秀学子 → Outstanding Student of a National First-Class University (using translation)
DeepSeekTranslationService: Name mapping - 博士 → Doctor (using translation)
DeepSeekTranslationService: Name mapping - 水军 → Paid Trolls (using translation)
DeepSeekTranslationService: Name mapping - 小记者 → Tabloid Journalist (using translation)
DeepSeekTranslationService: Attempt 1 of 3
TranslationService: DeepSeek translation successful
TranslationService: Formatting validation issues: Significant punctuation loss detected
TranslationService: Content result: {"success":true,"error":"none"}
TranslationService: Starting name extraction for non-chunked translation
TranslationService: Starting AI-based name extraction from text length: 6664
TranslationService: Starting AI-based name detection
TranslationService: Attempt 1 using primary (1.5-flash) model
TranslationService: AI parsed 3 high-quality names after filtering
TranslationService: AI detected 0 names
TranslationService: AI detection failed completely, using pattern matching as backup
TranslationService: Selective filtering reduced pattern names from 0 to 0
TranslationService: Successfully extracted 0 names with translations
TranslationService: Found 0 new names
TranslationService: Name dictionary updated
TranslationService: Updating chapter in database
TranslationService: Chapter updated successfully
TranslationService: Logging translation
TranslationService: Translation logged successfully
TranslationService: Preparing successful return
