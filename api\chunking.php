<?php
/**
 * API Endpoint: Chapter Chunking Management
 * GET /api/chunking.php - Get chunking configuration and statistics
 * POST /api/chunking.php - Update chunking configuration
 * PUT /api/chunking.php - Force rechunk a specific chapter
 */

require_once '../config/config.php';

header('Content-Type: application/json; charset=utf-8');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, PUT');
header('Access-Control-Allow-Headers: Content-Type');

$method = $_SERVER['REQUEST_METHOD'];

try {
    $chapterChunker = new ChapterChunker();
} catch (Exception $e) {
    logError('Failed to initialize ChapterChunker: ' . $e->getMessage());
    jsonResponse([
        'success' => false,
        'error' => 'Chunking service initialization failed'
    ], 500);
}

try {
    switch ($method) {
        case 'GET':
            handleGetChunkingInfo($chapterChunker);
            break;
            
        case 'POST':
            handleUpdateChunkingConfig($chapterChunker);
            break;
            
        case 'PUT':
            handleRechunkChapter($chapterChunker);
            break;

        default:
            jsonResponse(['error' => 'Method not allowed'], 405);
    }
    
} catch (Exception $e) {
    logError('Chunking API Error: ' . $e->getMessage(), [
        'method' => $method,
        'trace' => $e->getTraceAsString()
    ]);
    
    jsonResponse([
        'success' => false,
        'error' => 'An error occurred while processing your request'
    ], 500);
}

/**
 * Handle GET request - Get chunking configuration and statistics
 */
function handleGetChunkingInfo($chapterChunker) {
    try {
        $db = Database::getInstance();
        
        // Get current configuration
        $preferences = $db->fetchAll(
            "SELECT preference_key, preference_value FROM user_preferences 
             WHERE preference_key IN ('chunk_size_limit', 'chunk_overlap_size', 'enable_smart_chunking')"
        );
        
        $config = [];
        foreach ($preferences as $pref) {
            $config[$pref['preference_key']] = $pref['preference_value'];
        }
        
        // Get chunking statistics
        $stats = $db->fetchOne(
            "SELECT 
                COUNT(DISTINCT c.id) as total_chapters,
                COUNT(DISTINCT cc.chapter_id) as chunked_chapters,
                COUNT(cc.id) as total_chunks,
                AVG(cc.character_count) as avg_chunk_size,
                MAX(cc.character_count) as max_chunk_size,
                MIN(cc.character_count) as min_chunk_size
             FROM chapters c
             LEFT JOIN chapter_chunks cc ON c.id = cc.chapter_id"
        );
        
        // Get chapters that might need chunking
        $longChapters = $db->fetchAll(
            "SELECT c.id, c.novel_id, c.chapter_number, c.original_title,
                    CHAR_LENGTH(c.original_content) as content_length,
                    COUNT(cc.id) as chunk_count
             FROM chapters c
             LEFT JOIN chapter_chunks cc ON c.id = cc.chapter_id
             WHERE CHAR_LENGTH(c.original_content) > ?
             GROUP BY c.id
             ORDER BY content_length DESC
             LIMIT 10",
            [$config['chunk_size_limit'] ?? 25000]
        );
        
        jsonResponse([
            'success' => true,
            'data' => [
                'configuration' => $config,
                'statistics' => $stats,
                'long_chapters' => $longChapters
            ]
        ]);
        
    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => $e->getMessage()
        ], 500);
    }
}

/**
 * Handle POST request - Update chunking configuration
 */
function handleUpdateChunkingConfig($chapterChunker) {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input) {
        jsonResponse(['error' => 'Invalid JSON input'], 400);
    }

    $allowedSettings = ['chunk_size_limit', 'chunk_overlap_size', 'enable_smart_chunking'];
    $updates = [];

    foreach ($allowedSettings as $setting) {
        if (isset($input[$setting])) {
            $updates[$setting] = $input[$setting];
        }
    }

    if (empty($updates)) {
        jsonResponse(['error' => 'No valid settings provided'], 400);
    }

    try {
        $db = Database::getInstance();
        
        foreach ($updates as $key => $value) {
            // Validate values
            switch ($key) {
                case 'chunk_size_limit':
                    if (!is_numeric($value) || $value < 5000 || $value > 50000) {
                        jsonResponse(['error' => 'Chunk size limit must be between 5,000 and 50,000 characters'], 400);
                    }
                    break;
                case 'chunk_overlap_size':
                    if (!is_numeric($value) || $value < 0 || $value > 2000) {
                        jsonResponse(['error' => 'Chunk overlap size must be between 0 and 2,000 characters'], 400);
                    }
                    break;
                case 'enable_smart_chunking':
                    if (!in_array($value, ['true', 'false', true, false])) {
                        jsonResponse(['error' => 'Enable smart chunking must be true or false'], 400);
                    }
                    $value = $value === true || $value === 'true' ? 'true' : 'false';
                    break;
            }
            
            // Update preference
            $db->update(
                'user_preferences',
                ['preference_value' => (string)$value],
                'preference_key = ?',
                [$key]
            );
        }

        jsonResponse([
            'success' => true,
            'message' => 'Chunking configuration updated successfully',
            'updated_settings' => $updates
        ]);
        
    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => 'Failed to update configuration: ' . $e->getMessage()
        ], 500);
    }
}

/**
 * Handle PUT request - Force rechunk a specific chapter
 */
function handleRechunkChapter($chapterChunker) {
    $rawInput = file_get_contents('php://input');
    $input = json_decode($rawInput, true);

    if (!$input || !isset($input['chapter_id'])) {
        jsonResponse(['error' => 'Chapter ID is required'], 400);
    }

    $chapterId = (int)$input['chapter_id'];

    try {
        $db = Database::getInstance();
        
        // Get chapter content
        $chapter = $db->fetchOne(
            "SELECT id, original_content FROM chapters WHERE id = ?",
            [$chapterId]
        );

        if (!$chapter) {
            jsonResponse(['error' => 'Chapter not found'], 404);
        }

        if (empty($chapter['original_content'])) {
            jsonResponse(['error' => 'Chapter has no content to chunk'], 400);
        }

        // Force rechunk the chapter
        $result = $chapterChunker->splitChapter($chapterId, $chapter['original_content']);

        if ($result['success']) {
            jsonResponse([
                'success' => true,
                'message' => 'Chapter rechunked successfully',
                'data' => $result
            ]);
        } else {
            jsonResponse([
                'success' => false,
                'error' => 'Chunking failed: ' . $result['error']
            ], 500);
        }
        
    } catch (Exception $e) {
        jsonResponse([
            'success' => false,
            'error' => 'Failed to rechunk chapter: ' . $e->getMessage()
        ], 500);
    }
}




